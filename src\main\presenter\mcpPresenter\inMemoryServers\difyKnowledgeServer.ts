import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import axios from 'axios'

// Schema definitions
const DifyKnowledgeSearchArgsSchema = z.object({
  query: z.string().describe('Search query content (required)'),
  topK: z.number().optional().default(5).describe('Number of results returned (default 5)'),
  scoreThreshold: z
    .number()
    .optional()
    .default(0.2)
    .describe('Similarity threshold (0-1, default 0.2)')
})

// Define Dify API return data structure
interface DifySearchResponse {
  query: {
    content: string
  }
  records: Array<{
    segment: {
      id: string
      position: number
      document_id: string
      content: string
      word_count: number
      tokens: number
      keywords: string[]
      index_node_id: string
      index_node_hash: string
      hit_count: number
      enabled: boolean
      status: string
      created_by: string
      created_at: number
      indexing_at: number
      completed_at: number
      document?: {
        id: string
        data_source_type: string
        name: string
      }
    }
    score: number
  }>
}

// Import MCPTextContent interface
import { MCPTextContent } from '@shared/presenter'

export class DifyKnowledgeServer {
  private server: Server
  private configs: Array<{
    apiKey: string
    endpoint: string
    datasetId: string
    description: string
    enabled: boolean
  }> = []

  constructor(env?: {
    configs: {
      apiKey: string
      endpoint: string
      datasetId: string
      description: string
      enabled: boolean
    }[]
  }) {
    console.log('DifyKnowledgeServer constructor', env)
    if (!env) {
      throw new Error('Provide Dify knowledge base configuration')
    }

    const envs = env.configs

    if (!Array.isArray(envs) || envs.length === 0) {
      throw new Error('Provide at least one Dify knowledge base configuration')
    }

    // Handle each configuration
    for (const env of envs) {
      if (!env.apiKey) {
        throw new Error('Provide Dify API Key')
      }
      if (!env.datasetId) {
        throw new Error('Provide Dify Dataset ID')
      }
      if (!env.description) {
        throw new Error(
          'Provide a description of this knowledge base to facilitate whether to search this knowledge base'
        )
      }

      this.configs.push({
        apiKey: env.apiKey,
        datasetId: env.datasetId,
        endpoint: env.endpoint || 'https://api.dify.ai/v1',
        description: env.description,
        enabled: env.enabled
      })
    }

    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/dify-knowledge-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set request handler
    this.setupRequestHandlers()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Set request handler
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.configs
        .filter((conf) => conf.enabled)
        .map((config, index) => {
          const suffix = this.configs.length > 1 ? `_${index + 1}` : ''
          return {
            name: `dify_knowledge_search${suffix}`,
            description: config.description,
            inputSchema: zodToJsonSchema(DifyKnowledgeSearchArgsSchema)
          }
        })

      return { tools }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: parameters } = request.params

      // Check if tool is Dify knowledge search
      if (name.startsWith('dify_knowledge_search')) {
        try {
          // Filter out enabled configurations
          const enabledConfigs = this.configs.filter((config) => config.enabled)
          // Extract index
          let configIndex = 0
          const match = name.match(/_([0-9]+)$/)
          if (match) {
            configIndex = parseInt(match[1], 10) - 1
          }

          // Ensure index is valid
          if (configIndex < 0 || configIndex >= enabledConfigs.length) {
            throw new Error(`Invalid knowledge base index: ${configIndex}`)
          }

          // Get actual configuration index
          const actualConfigIndex = this.configs.findIndex(
            (config) => config === enabledConfigs[configIndex]
          )

          return await this.performDifyKnowledgeSearch(parameters, actualConfigIndex)
        } catch (error) {
          console.error('Dify knowledge search failed:', error)
          return {
            content: [
              {
                type: 'text',
                text: `Search failed: ${error instanceof Error ? error.message : String(error)}`
              }
            ]
          }
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: `Unknown tool: ${name}`
          }
        ]
      }
    })
  }

  // Perform Dify knowledge search
  private async performDifyKnowledgeSearch(
    parameters: Record<string, unknown> | undefined,
    configIndex: number = 0
  ): Promise<{ content: MCPTextContent[] }> {
    const {
      query,
      topK = 5,
      scoreThreshold = 0.2
    } = parameters as {
      query: string
      topK?: number
      scoreThreshold?: number
    }

    if (!query) {
      throw new Error('Query content cannot be empty')
    }

    // Get current configuration
    const config = this.configs[configIndex]

    try {
      const url = `${config.endpoint.replace(/\/$/, '')}/datasets/${config.datasetId}/retrieve`
      console.log('performDifyKnowledgeSearch request', url, {
        query,
        retrieval_model: {
          top_k: topK,
          score_threshold: scoreThreshold
        }
      })

      const response = await axios.post<DifySearchResponse>(
        url,
        {
          query,
          retrieval_model: {
            top_k: topK,
            score_threshold: scoreThreshold,
            reranking_enable: null, // These two fields must be present even if they are empty, otherwise the interface cannot be requested
            score_threshold_enabled: null
          }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      )

      // Handle response data
      const results = response.data.records.map((record) => {
        const docName = record.segment.document?.name || 'Unknown document'
        const docId = record.segment.document_id
        const content = record.segment.content
        const score = record.score

        return {
          title: docName,
          documentId: docId,
          content: content,
          score: score,
          keywords: record.segment.keywords || []
        }
      })

      // Build response
      let resultText = `### Query: ${query}\n\n`

      if (results.length === 0) {
        resultText += 'No related results found.'
      } else {
        resultText += `Found ${results.length} related results:\n\n`

        results.forEach((result, index) => {
          resultText += `#### ${index + 1}. ${result.title} (Similarity: ${(result.score * 100).toFixed(2)}%)\n`
          resultText += `${result.content}\n\n`

          if (result.keywords && result.keywords.length > 0) {
            resultText += `Keywords: ${result.keywords.join(', ')}\n\n`
          }
        })
      }

      return {
        content: [
          {
            type: 'text',
            text: resultText
          }
        ]
      }
    } catch (error) {
      console.error('Dify API request failed:', error)
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `Dify API error (${error.response.status}): ${JSON.stringify(error.response.data)}`
        )
      }
      throw error
    }
  }
}
