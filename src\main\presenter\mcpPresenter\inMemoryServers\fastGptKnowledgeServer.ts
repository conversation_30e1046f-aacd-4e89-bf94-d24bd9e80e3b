import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import axios from 'axios'

// Schema definitions
const FastGptKnowledgeSearchArgsSchema = z.object({
  query: z.string().describe('Search query content (required)'),
  topK: z.number().optional().default(5).describe('Return result count (default 5)'),
  scoreThreshold: z
    .number()
    .optional()
    .default(0.2)
    .describe('Similarity threshold (0-1, default 0.2)')
})

// Define FastGPT API return data structure
interface FastGptSearchResponse {
  code: number
  statusText: string
  data: {
    list: Array<{
      id: string
      q: string
      a: string
      datasetId: string
      collectionId: string
      sourceName: string
      sourceId: string
      score: Array<{
        value: number
        type: string
        index: number
      }>
    }>
  }
}

// Import MCPTextContent interface
import { MCPTextContent } from '@shared/presenter'

export class FastGptKnowledgeServer {
  private server: Server
  private configs: Array<{
    apiKey: string
    endpoint: string
    datasetId: string
    description: string
    enabled: boolean
  }> = []

  constructor(env?: {
    configs: {
      apiKey: string
      endpoint: string
      datasetId: string
      description: string
      enabled: boolean
    }[]
  }) {
    console.log('FastGptKnowledgeServer constructor', env)
    if (!env) {
      throw new Error('Provide FastGPT knowledge base configuration')
    }

    const envs = env.configs

    if (!Array.isArray(envs) || envs.length === 0) {
      throw new Error('Provide at least one FastGPT knowledge base configuration')
    }

    // Handle each configuration
    for (const env of envs) {
      if (!env.apiKey) {
        throw new Error('Provide FastGPT API Key')
      }
      if (!env.datasetId) {
        throw new Error('Provide FastGPT Dataset ID')
      }
      if (!env.description) {
        throw new Error(
          'Provide a description of this knowledge base to facilitate whether to search this knowledge base'
        )
      }

      this.configs.push({
        apiKey: env.apiKey,
        datasetId: env.datasetId,
        endpoint: env.endpoint || 'http://localhost:3000/api',
        description: env.description,
        enabled: env.enabled
      })
    }

    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/fastgpt-knowledge-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set request handler
    this.setupRequestHandlers()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Set request handler
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.configs
        .filter((conf) => conf.enabled)
        .map((config, index) => {
          const suffix = this.configs.length > 1 ? `_${index + 1}` : ''
          return {
            name: `fastgpt_knowledge_search${suffix}`,
            description: config.description,
            inputSchema: zodToJsonSchema(FastGptKnowledgeSearchArgsSchema)
          }
        })
      return { tools }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: parameters } = request.params

      // Check if tool is FastGPT knowledge search
      if (name.startsWith('fastgpt_knowledge_search')) {
        try {
          // Filter out enabled configurations
          const enabledConfigs = this.configs.filter((config) => config.enabled)
          // Extract index
          let configIndex = 0
          const match = name.match(/_([0-9]+)$/)
          if (match) {
            configIndex = parseInt(match[1], 10) - 1
          }

          // Ensure index is valid
          if (configIndex < 0 || configIndex >= enabledConfigs.length) {
            throw new Error(`Invalid knowledge base index: ${configIndex}`)
          }
          // Get actual index of configuration
          const actualConfigIndex = this.configs.findIndex(
            (config) => config === enabledConfigs[configIndex]
          )

          return await this.performFastGptKnowledgeSearch(parameters, actualConfigIndex)
        } catch (error) {
          console.error('FastGPT knowledge search failed:', error)
          return {
            content: [
              {
                type: 'text',
                text: `Search failed: ${error instanceof Error ? error.message : String(error)}`
              }
            ]
          }
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: `Unknown tool: ${name}`
          }
        ]
      }
    })
  }

  // Perform FastGPT knowledge search
  private async performFastGptKnowledgeSearch(
    parameters: Record<string, unknown> | undefined,
    configIndex: number = 0
  ): Promise<{ content: MCPTextContent[] }> {
    const {
      query,
      topK = 5,
      scoreThreshold = 0.2
    } = parameters as {
      query: string
      topK?: number
      scoreThreshold?: number
    }

    if (!query) {
      throw new Error('Query content cannot be empty')
    }

    // Get current configuration
    const config = this.configs[configIndex]

    try {
      const url = `${config.endpoint.replace(/\/$/, '')}/core/dataset/searchTest`

      const response = await axios.post<FastGptSearchResponse>(
        url,
        {
          datasetId: config.datasetId,
          text: query,
          limit: 20000,
          similarity: scoreThreshold,
          searchMode: 'embedding',
          usingReRank: false
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      )

      if (response.data.code !== 200) {
        throw new Error(`FastGPT API error: ${response.data.statusText}`)
      }

      // Handle response data
      const results = response.data.data.list.slice(0, topK).map((record) => {
        return {
          title: record.sourceName || 'Unknown document',
          documentId: record.sourceId,
          content: record.q,
          score: record.score.length > 0 ? record.score[0].value : 0
        }
      })

      // Build response
      let resultText = `### Query: ${query}\n\n`

      if (results.length === 0) {
        resultText += 'No related results found.'
      } else {
        resultText += `Found ${results.length} related results:\n\n`

        results.forEach((result, index) => {
          resultText += `#### ${index + 1}. ${result.title} (Similarity: ${(result.score * 100).toFixed(2)}%)\n`
          resultText += `${result.content}\n\n`
        })
      }

      return {
        content: [
          {
            type: 'text',
            text: resultText
          }
        ]
      }
    } catch (error) {
      console.error('FastGPT API request failed:', error)
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `FastGPT API error (${error.response.status}): ${JSON.stringify(error.response.data)}`
        )
      }
      throw error
    }
  }
}
