# Version Update Tool Instructions

This directory contains scripts for managing application version information, used to replace the existing automatic update mechanism.

## File Description

- `version-template.json`: Version information template file
- `generate-version-files.mjs`: Script to generate version information files for each platform based on the template
- `update-version.mjs`: Convenient script to update version information and generate version files for each platform

## Usage

### 1. Update Version Information

Use the `update-version.mjs` script to quickly update version information and generate version files for each platform:

```bash
node generate-version-files.mjs  --version=0.0.6 --notes="Version update notes"  --date="2023-06-15"
```

Parameter description:
- `--version`: New version number (required, format is X.Y.Z)
- `--notes`: Version update notes (optional)
- `--date`: Release date (optional, defaults to current date)

### 2. Generated Files

The script will generate version information files for the following platforms:

- `winx64.json`: Windows x64 version
- `winarm.json`: Windows ARM64 version
- `linuxx64.json`: Linux x64 version
- `linuxarm.json`: Linux ARM64 version

### 3. Deploy Files

The generated files need to be uploaded to the `/versions/` directory on the server, and the application will check for updates using the following URL:

```
https://calmren.com/auth/{platform}.json
```

Where `{platform}` is the identifier for the current running platform (such as `winx64`, `linuxx64`, etc.).

## Version Information Format

The version information JSON file contains the following fields:

```json
{
  "version": "0.0.5",
  "releaseDate": "2023-06-01",
  "releaseNotes": "Version update notes",
  "githubUrl": "https://github.com/Calmren/docomoe/releases/tag/v0.0.5",
  "downloadUrl": "https://calmren.com/#/download"
}
```

- `version`: Version number, format is X.Y.Z
- `releaseDate`: Release date
- `releaseNotes`: Version update notes (supports Markdown format)
- `githubUrl`: GitHub release page link
- `downloadUrl`: Download link
