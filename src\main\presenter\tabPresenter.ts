/* eslint-disable @typescript-eslint/no-explicit-any */
import { eventBus } from '@/eventbus'
import { WINDOW_EVENTS, CONFIG_EVENTS, SYSTEM_EVENTS, TAB_EVENTS } from '@/events'
import { is } from '@electron-toolkit/utils'
import { ITabPresenter, TabCreateOptions, IWindowPresenter, TabData } from '@shared/presenter'
import { BrowserWindow, WebContentsView, shell, nativeImage } from 'electron'
import { join } from 'path'
import contextMenu from '@/contextMenuHelper'
import { getContextMenuLabels } from '@shared/i18n'
import { app } from 'electron'
import { addWatermarkToNativeImage } from '@/lib/watermark'
import { stitchImagesVertically } from '@/lib/scrollCapture'

export class TabPresenter implements ITabPresenter {
  // Global tab instance storage
  private tabs: Map<number, WebContentsView> = new Map()

  // Store tab state
  private tabState: Map<number, TabData> = new Map()

  // Window ID to its containing tab ID list mapping
  private windowTabs: Map<number, number[]> = new Map()

  // Tab ID to its current window ID mapping
  private tabWindowMap: Map<number, number> = new Map()

  // Store the context menu handler for each tab
  private tabContextMenuDisposers: Map<number, () => void> = new Map()

  private windowPresenter: IWindowPresenter // Store windowPresenter instance

  constructor(windowPresenter: IWindowPresenter) {
    this.windowPresenter = windowPresenter // Assign injected windowPresenter
    this.initBusHandlers()
  }

  private initBusHandlers() {
    eventBus.on(WINDOW_EVENTS.WINDOW_RESIZE, (windowId: number) => {
      const views = this.windowTabs.get(windowId)
      const window = BrowserWindow.fromId(windowId)
      if (window) {
        views?.forEach((view) => {
          this.updateViewBounds(window, this.tabs.get(view)!)
        })
      }
    })

    eventBus.on(WINDOW_EVENTS.WINDOW_CLOSED, (windowId: number) => {
      const views = this.windowTabs.get(windowId)
      const window = BrowserWindow.fromId(windowId)
      if (window) {
        views?.forEach((viewId) => {
          const view = this.tabs.get(viewId)
          if (view) {
            this.detachViewFromWindow(window, view)
          }
        })
      }
    })

    // Add language setting change event handler
    eventBus.on(CONFIG_EVENTS.SETTING_CHANGED, async (key) => {
      if (key === 'language') {
        // Update context menu for all active tabs
        for (const [tabId] of this.tabWindowMap.entries()) {
          await this.setupTabContextMenu(tabId)
        }
      }
    })

    // Add system theme update event handler
    eventBus.on(SYSTEM_EVENTS.SYSTEM_THEME_UPDATED, (isDark: boolean) => {
      // Broadcast theme update to all tabs
      for (const [, view] of this.tabs.entries()) {
        if (!view.webContents.isDestroyed()) {
          view.webContents.send('system-theme-updated', isDark)
        }
      }
    })
  }

  /**
   * Create new tab and add to specified window
   */
  async createTab(
    windowId: number,
    url: string,
    options: TabCreateOptions = {}
  ): Promise<number | null> {
    console.log('createTab', windowId, url, options)
    const window = BrowserWindow.fromId(windowId)
    if (!window) return null

    // Create new BrowserView
    const view = new WebContentsView({
      webPreferences: {
        preload: join(__dirname, '../preload/index.mjs'),
        sandbox: false,
        devTools: is.dev
      }
    })

    view.setBorderRadius(8)
    view.setBackgroundColor('#00ffffff')

    // Load content
    if (url.startsWith('local://')) {
      const viewType = url.replace('local://', '')
      if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
        view.webContents.loadURL(`${process.env['ELECTRON_RENDERER_URL']}#/${viewType}`)
      } else {
        view.webContents.loadFile(join(__dirname, '../renderer/index.html'), {
          hash: `/${viewType}`
        })
      }
    } else {
      view.webContents.loadURL(url)
    }
    if (is.dev) {
      view.webContents.openDevTools({ mode: 'detach' })
    }

    // Store tab information
    const tabId = view.webContents.id
    this.tabs.set(tabId, view)
    this.tabState.set(tabId, {
      id: tabId,
      title: url,
      isActive: options.active ?? true,
      url: url,
      closable: true,
      position: options?.position ?? 0
    })

    // Update window-tab mapping
    if (!this.windowTabs.has(windowId)) {
      this.windowTabs.set(windowId, [])
    }

    const tabs = this.windowTabs.get(windowId)!
    const insertIndex = options.position !== undefined ? options.position : tabs.length
    tabs.splice(insertIndex, 0, tabId)

    this.tabWindowMap.set(tabId, windowId)

    // Add to window
    this.attachViewToWindow(window, view)

    // If activation is required, set as active tab
    if (options.active ?? true) {
      this.activateTab(tabId)
    }

    // Set context menu after creating tab
    await this.setupTabContextMenu(tabId)

    // // Listen for tab-related events
    this.setupWebContentsListeners(view.webContents, tabId, windowId)

    // // Notify renderer process to update tab list
    this.notifyWindowTabsUpdate(windowId)

    return tabId
  }

  /**
   * Destroy tab
   */
  async closeTab(tabId: number): Promise<boolean> {
    return this.destroyTab(tabId)
  }

  /**
   * Activate tab
   */
  async switchTab(tabId: number): Promise<boolean> {
    return this.activateTab(tabId)
  }

  /**
   * Get tab instance
   */
  async getTab(tabId: number): Promise<WebContentsView | undefined> {
    return this.tabs.get(tabId)
  }

  /**
   * Destroy tab
   */
  destroyTab(tabId: number): boolean {
    // Clean up context menu
    this.cleanupTabContextMenu(tabId)

    const view = this.tabs.get(tabId)
    if (!view) return false

    const windowId = this.tabWindowMap.get(tabId)
    if (!windowId) return false

    const window = BrowserWindow.fromId(windowId)
    if (window) {
      // Remove view from window
      this.detachViewFromWindow(window, view)
    }

    // Remove event listeners
    this.removeWebContentsListeners(view.webContents)

    // Remove from data structure
    this.tabs.delete(tabId)
    this.tabState.delete(tabId)
    this.tabWindowMap.delete(tabId)

    if (this.windowTabs.has(windowId)) {
      const tabs = this.windowTabs.get(windowId)!
      const index = tabs.indexOf(tabId)
      if (index !== -1) {
        tabs.splice(index, 1)

        // If there are other tabs and the active tab is closed, activate the adjacent tab
        if (tabs.length > 0) {
          const newActiveIndex = Math.min(index, tabs.length - 1)
          this.activateTab(tabs[newActiveIndex])
        }
      }

      // Notify renderer process to update tab list
      this.notifyWindowTabsUpdate(windowId)
    }

    // Destroy view
    view.webContents.closeDevTools()
    return true
  }

  /**
   * Activate tab
   */
  private activateTab(tabId: number): boolean {
    const view = this.tabs.get(tabId)
    if (!view) return false

    const windowId = this.tabWindowMap.get(tabId)
    if (!windowId) return false

    const window = BrowserWindow.fromId(windowId)
    if (!window) return false

    // Get all tabs in the window
    const tabs = this.windowTabs.get(windowId) || []

    // Update the active state of all tabs and handle view visibility/hiding
    for (const id of tabs) {
      const state = this.tabState.get(id)
      const tabView = this.tabs.get(id)
      if (state && tabView) {
        state.isActive = id === tabId
        // Set view visibility based on active state
        tabView.setVisible(id === tabId)
      }
    }

    // Ensure active view is visible and is at the front
    this.bringViewToFront(window, view)

    // Notify renderer process to update tab list
    this.notifyWindowTabsUpdate(windowId)

    // Notify renderer process to switch active tab
    window.webContents.send('setActiveTab', windowId, tabId)

    return true
  }

  /**
   * Detach tab (do not destroy)
   */
  async detachTab(tabId: number): Promise<boolean> {
    const view = this.tabs.get(tabId)
    if (!view) return false

    const windowId = this.tabWindowMap.get(tabId)
    if (!windowId) return false

    const window = BrowserWindow.fromId(windowId)
    if (window) {
      // Remove view from window
      this.detachViewFromWindow(window, view)
    }

    // Remove from window tab list
    if (this.windowTabs.has(windowId)) {
      const tabs = this.windowTabs.get(windowId)!
      const index = tabs.indexOf(tabId)
      if (index !== -1) {
        tabs.splice(index, 1)
      }

      // Notify renderer process to update tab list
      this.notifyWindowTabsUpdate(windowId)

      // If the window has other tabs, activate one
      if (tabs.length > 0) {
        this.activateTab(tabs[Math.min(index, tabs.length - 1)])
      }
    }

    // Mark as detached
    this.tabWindowMap.delete(tabId)

    return true
  }

  /**
   * Attach tab to target window
   */
  async attachTab(tabId: number, targetWindowId: number, index?: number): Promise<boolean> {
    const view = this.tabs.get(tabId)
    if (!view) return false

    const window = BrowserWindow.fromId(targetWindowId)
    if (!window) return false

    // Ensure target window has tab list
    if (!this.windowTabs.has(targetWindowId)) {
      this.windowTabs.set(targetWindowId, [])
    }

    // Add to target window's tab list
    const tabs = this.windowTabs.get(targetWindowId)!
    const insertIndex = index !== undefined ? index : tabs.length
    tabs.splice(insertIndex, 0, tabId)

    // Update tab's window association
    this.tabWindowMap.set(tabId, targetWindowId)

    // Add view to window
    this.attachViewToWindow(window, view)

    // Activate tab
    this.activateTab(tabId)

    // Notify renderer process to update tab list
    this.notifyWindowTabsUpdate(targetWindowId)

    return true
  }

  /**
   * Move tab from source window to target window
   */
  async moveTab(tabId: number, targetWindowId: number, index?: number): Promise<boolean> {
    const windowId = this.tabWindowMap.get(tabId)

    // If already in target window, only adjust position
    if (windowId === targetWindowId) {
      if (index !== undefined && this.windowTabs.has(windowId)) {
        const tabs = this.windowTabs.get(windowId)!
        const currentIndex = tabs.indexOf(tabId)
        if (currentIndex !== -1 && currentIndex !== index) {
          // Remove current position
          tabs.splice(currentIndex, 1)

          // Calculate new insertion position (considering removal of element after index change)
          const newIndex = index > currentIndex ? index - 1 : index

          // Insert into new position
          tabs.splice(newIndex, 0, tabId)

          // Notify renderer process to update tab list
          this.notifyWindowTabsUpdate(windowId)
          return true
        }
      }
      return false
    }

    // Detach from source window
    const detached = this.detachTab(tabId)
    if (!detached) return false

    // Attach to target window
    return this.attachTab(tabId, targetWindowId, index)
  }

  /**
   * Get all tabs data in a window
   */
  async getWindowTabsData(windowId: number): Promise<TabData[]> {
    const tabsInWindow = this.windowTabs.get(windowId) || []
    return tabsInWindow.map((tabId) => {
      const state = this.tabState.get(tabId) || ({} as TabData)
      return state
    })
  }

  /**
   * Notify renderer process to update tab list
   */
  notifyWindowTabsUpdate(windowId: number): void {
    const window = BrowserWindow.fromId(windowId)
    if (!window || window.isDestroyed()) return

    this.getWindowTabsData(windowId).then((tabListData) => {
      if (!window.isDestroyed() && window.webContents && !window.webContents.isDestroyed()) {
        window.webContents.send('update-window-tabs', windowId, tabListData)
      }
    })
  }

  /**
   * Set event listeners for WebContents
   */
  private setupWebContentsListeners(
    webContents: Electron.WebContents,
    tabId: number,
    windowId: number
  ): void {
    // Handle external links
    webContents.setWindowOpenHandler(({ url }) => {
      // Open link using system default browser
      shell.openExternal(url)
      return { action: 'deny' }
    })

    // Title change
    webContents.on('page-title-updated', (_event, title) => {
      const state = this.tabState.get(tabId)
      if (state) {
        state.title = title || state.url || 'Untitled'
        // Notify renderer process that title has been updated
        const window = BrowserWindow.fromId(windowId)
        if (window && !window.isDestroyed()) {
          window.webContents.send(TAB_EVENTS.TITLE_UPDATED, {
            tabId,
            title: state.title,
            windowId
          })
        }
        this.notifyWindowTabsUpdate(windowId)
      }
    })

    // 检查是否是窗口的第一个标签页
    const isFirstTab = this.windowTabs.get(windowId)?.length === 1

    // 页面加载完成
    if (isFirstTab) {
      eventBus.emit(WINDOW_EVENTS.READY_TO_SHOW)
      webContents.once('did-finish-load', () => {
        eventBus.emit(WINDOW_EVENTS.FIRST_CONTENT_LOADED, windowId)
      })
    }

    // Favicon变更
    webContents.on('page-favicon-updated', (_event, favicons) => {
      if (favicons.length > 0) {
        const state = this.tabState.get(tabId)
        if (state) {
          if (state.icon !== favicons[0]) {
            console.log('page-favicon-updated', state.icon, favicons[0])
            state.icon = favicons[0]
            this.notifyWindowTabsUpdate(windowId)
          }
        }
      }
    })

    // 导航完成
    webContents.on('did-navigate', (_event, url) => {
      const state = this.tabState.get(tabId)
      if (state) {
        state.url = url
        // 如果没有标题，使用URL作为标题
        if (!state.title || state.title === 'Untitled') {
          state.title = url
          const window = BrowserWindow.fromId(windowId)
          if (window && !window.isDestroyed()) {
            window.webContents.send(TAB_EVENTS.TITLE_UPDATED, {
              tabId,
              title: state.title,
              windowId
            })
          }
          this.notifyWindowTabsUpdate(windowId)
        }
      }
    })
  }

  /**
   * 移除WebContents的事件监听
   */
  private removeWebContentsListeners(webContents: Electron.WebContents): void {
    webContents.removeAllListeners('page-title-updated')
    webContents.removeAllListeners('page-favicon-updated')
    webContents.removeAllListeners('did-navigate')
    webContents.removeAllListeners('did-finish-load')
    webContents.setWindowOpenHandler(() => ({ action: 'allow' }))
  }

  /**
   * 将视图添加到窗口
   * 注意：实际实现可能需要根据Electron窗口布局策略调整
   */
  private attachViewToWindow(window: BrowserWindow, view: WebContentsView): void {
    // 这里需要根据实际窗口结构实现
    // 简单实现可能是：
    window.contentView.addChildView(view)
    this.updateViewBounds(window, view)
  }

  /**
   * 从窗口中分离视图
   */
  private detachViewFromWindow(window: BrowserWindow, view: WebContentsView): void {
    // 这里需要根据实际窗口结构实现
    window.contentView.removeChildView(view)
  }

  /**
   * 将视图带到前面（激活）
   */
  private bringViewToFront(window: BrowserWindow, view: WebContentsView): void {
    // 这里需要根据实际窗口结构实现
    window.contentView.addChildView(view)

    this.updateViewBounds(window, view)
  }

  /**
   * Update view size to fit window
   */
  private updateViewBounds(window: BrowserWindow, view: WebContentsView): void {
    // Get window size
    const { width, height } = window.getContentBounds()

    // Set view position and size (leave space for top tab bar)
    const TAB_BAR_HEIGHT = 40 // Tab bar height, needs to be adjusted based on actual UI
    view.setBounds({
      x: 4,
      y: TAB_BAR_HEIGHT,
      width: width - 8,
      height: height - TAB_BAR_HEIGHT - 4
    })
  }

  /**
   * Set context menu for tab
   */
  private async setupTabContextMenu(tabId: number): Promise<void> {
    const view = this.tabs.get(tabId)
    if (!view || view.webContents.isDestroyed()) return

    // If a handler already exists, clean up first
    if (this.tabContextMenuDisposers.has(tabId)) {
      this.tabContextMenuDisposers.get(tabId)?.()
      this.tabContextMenuDisposers.delete(tabId)
    }

    const lang = app.getLocale()
    const labels = await getContextMenuLabels(lang)

    const disposer = contextMenu({
      webContents: view.webContents,
      labels,
      shouldShowMenu() {
        return true
      }
    })

    this.tabContextMenuDisposers.set(tabId, disposer)
  }

  /**
   * Clean up context menu for tab
   */
  private cleanupTabContextMenu(tabId: number): void {
    if (this.tabContextMenuDisposers.has(tabId)) {
      this.tabContextMenuDisposers.get(tabId)?.()
      this.tabContextMenuDisposers.delete(tabId)
    }
  }

  public destroy() {
    // Clean up context menu for all tabs
    for (const [tabId] of this.tabContextMenuDisposers) {
      this.cleanupTabContextMenu(tabId)
    }
    this.tabContextMenuDisposers.clear()

    // Iterate over the map containing tab views or windows
    for (const [tabId] of this.tabWindowMap.entries()) {
      // TODO: Implement cleanup logic for each entry
      // This might involve destroying WebContentsView, removing listeners, etc.
      console.log(`Destroying resources for tab: ${tabId}`)
      this.closeTab(tabId)
    }
    // Clear the map after processing all entries

    this.tabWindowMap.clear()
    this.tabs.clear()
    this.tabState.clear()
    this.windowTabs.clear()
  }

  async moveTabToNewWindow(tabId: number, screenX?: number, screenY?: number): Promise<boolean> {
    const tabInfo = this.tabState.get(tabId)
    const originalWindowId = this.tabWindowMap.get(tabId)

    if (!tabInfo || originalWindowId === undefined) {
      console.error(`moveTabToNewWindow: Tab ${tabId} not found or no window associated.`)
      return false
    }

    // 1. Detach the tab from its current window (do not destroy)
    const detached = await this.detachTab(tabId)
    if (!detached) {
      console.error(
        `moveTabToNewWindow: Failed to detach tab ${tabId} from window ${originalWindowId}.`
      )
      // Attempt to reattach to original window if detachment fails
      // await this.attachTab(tabId, originalWindowId) // Consider if this is the desired fallback
      return false
    }

    // 2. Create a new window with position and options.
    const newWindowOptions: Record<string, any> = {
      // Consider defining a proper type for these options
      forMovedTab: true,
      activateTabId: tabId
    }
    if (screenX !== undefined && screenY !== undefined) {
      newWindowOptions.x = screenX
      newWindowOptions.y = screenY
    }

    const newWindowId = await this.windowPresenter.createShellWindow(newWindowOptions)

    if (newWindowId === null) {
      console.error('moveTabToNewWindow: Failed to create a new window.')
      // Attempt to reattach to original window if new window creation fails
      await this.attachTab(tabId, originalWindowId)
      return false
    }

    // 3. Attach the tab (WebContentsView) to the new window
    const attached = await this.attachTab(tabId, newWindowId)
    if (!attached) {
      console.error(
        `moveTabToNewWindow: Failed to attach tab ${tabId} to new window ${newWindowId}.`
      )
      // If attaching fails, we might need to destroy the newly created window or reattach to original.
      // Consider closing the new empty window:
      // if (newBrowserWindow) newBrowserWindow.close();
      // else this.windowPresenter.close(newWindowId); // Fallback if newBrowserWindow is null
      // And then reattach to original
      // await this.attachTab(tabId, originalWindowId);
      return false
    }

    console.log(`Tab ${tabId} moved from window ${originalWindowId} to new window ${newWindowId}`)
    this.notifyWindowTabsUpdate(originalWindowId) // Notify original window
    this.notifyWindowTabsUpdate(newWindowId) // Notify new window
    return true
  }

  /**
   * Capture tab area screenshot (simple)
   * @param tabId Tab ID
   * @param rect Capture area
   * @returns Returns base64-formatted image data, or null on failure
   */
  async captureTabArea(
    tabId: number,
    rect: { x: number; y: number; width: number; height: number }
  ): Promise<string | null> {
    try {
      const view = this.tabs.get(tabId)
      if (!view || view.webContents.isDestroyed()) {
        console.error(`captureTabArea: Tab ${tabId} not found or destroyed`)
        return null
      }

      // Use Electron's capturePage API to capture screenshot
      const image = await view.webContents.capturePage(rect)

      if (image.isEmpty()) {
        console.error('captureTabArea: Captured image is empty')
        return null
      }

      // Convert to base64 format
      const base64Data = image.toDataURL()
      return base64Data
    } catch (error) {
      console.error('captureTabArea error:', error)
      return null
    }
  }

  /**
   * Stitch multiple screenshots into a long image and add watermark
   * @param imageDataList Array of base64-formatted image data
   * @param options Watermark options
   * @returns Returns stitched and watermarked base64 image data, or null on failure
   */
  async stitchImagesWithWatermark(
    imageDataList: string[],
    options: {
      isDark?: boolean
      version?: string
      texts?: {
        brand?: string
        time?: string
        tip?: string
        model?: string
        provider?: string
      }
    } = {}
  ): Promise<string | null> {
    try {
      if (imageDataList.length === 0) {
        console.error('stitchImagesWithWatermark: No images provided')
        return null
      }

      // If only one image, add watermark directly
      if (imageDataList.length === 1) {
        const nativeImageInstance = nativeImage.createFromDataURL(imageDataList[0])
        const watermarkedImage = await addWatermarkToNativeImage(nativeImageInstance, options)
        return watermarkedImage.toDataURL()
      }

      // Convert base64 images to NativeImage, then to Buffer
      const imageBuffers = imageDataList.map((data) => {
        const image = nativeImage.createFromDataURL(data)
        return image.toPNG()
      })

      // Stitch images
      const stitchedImage = await stitchImagesVertically(imageBuffers)

      // Add watermark
      const watermarkedImage = await addWatermarkToNativeImage(stitchedImage, options)

      // Convert to base64 format
      const base64Data = watermarkedImage.toDataURL()

      console.log(`Successfully stitched ${imageDataList.length} images with watermark`)
      return base64Data
    } catch (error) {
      console.error('stitchImagesWithWatermark error:', error)
      return null
    }
  }
}
