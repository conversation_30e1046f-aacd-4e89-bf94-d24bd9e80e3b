# Modular Deep Research Architecture
*A comprehensive framework for AI-driven research workflows using specialized MCP servers*

## 🎯 Core Concept

The Modular Deep Research Architecture provides a flexible, AI-driven research pipeline that allows LLMs to intelligently select and combine specialized tools for comprehensive information gathering and analysis.

### Key Principles
- **Modularity**: Each server has a focused, specific purpose
- **Intelligence**: LLMs decide which tools to use based on context
- **Efficiency**: Progressive refinement from broad search to deep analysis
- **Flexibility**: Workflow can adapt to different research types

## 🔄 Complete Research Workflow

```mermaid
graph TD
    A[Research Query] --> B{Search Phase}
    B --> C[bochaSearch - API-based]
    B --> D[braveSearch - API + Local]
    B --> E[webScraperServer - API-free]
    B --> F[rssServer - News/Feeds]
    
    C --> G[URL Collection]
    D --> G
    E --> G
    F --> G
    
    G --> H[webScraperServer.UrlContentExtractor]
    H --> I[Clean Content Previews]
    I --> J[LLM Analysis & Selection]
    
    J --> K{Content Extraction}
    K --> L[webScraperServer - Simple Sites]
    K --> M[puppeteerServer - Complex Sites]
    K --> N[advancedCrawlerServer - Research Content]
    
    L --> O[Data Aggregation - MISSING]
    M --> O
    N --> O
    
    O --> P[Artifacts - Report Generation]
```

## 📋 Six-Step Research Template

### Step 1: Search (Discovery Phase)
**Goal**: Cast wide net to discover potential sources

| Tool | Use Case | Output |
|------|----------|---------|
| **bochaSearch** | Primary API search with AI enhancement | Structured web results + AI insights |
| **braveSearch** | Fallback API search + local business | Web results + location data |
| **webScraperServer.DuckDuckGoWebSearch** | API-free search option | Basic search results |
| **rssServer** | News, blogs, specialized feeds | Recent articles from trusted sources |

**LLM Decision Logic**:
```typescript
if (news_query || recent_events) {
  use: rssServer + bochaSearch
}
if (local_business || location_based) {
  use: braveSearch.brave_local_search
}
if (api_limits_reached) {
  use: webScraperServer.DuckDuckGoWebSearch
}
```

### Step 2: Crawl (Content Preview)
**Goal**: Quick preview of ALL discovered sources for intelligent filtering

| Tool | Purpose | Batch Size |
|------|---------|------------|
| **webScraperServer.UrlContentExtractor** | Fast content preview | 20-50 URLs |

**Process**:
1. Take ALL URLs from search phase (typically 20-50)
2. Extract clean text summaries (8KB limit per URL)
3. Provide LLM with content previews for decision making
4. Rate limited (20 req/min) for ethical crawling

### Step 3: Extract (Deep Content Analysis)
**Goal**: Detailed extraction from LLM-selected high-value sources

| Tool | Best For | Output Quality |
|------|----------|----------------|
| **webScraperServer** | Simple static sites | Clean text |
| **puppeteerServer** | JS-heavy, dynamic sites | Complex extractions |
| **advancedCrawlerServer** | Academic, research content | Rich metadata + citations |

**LLM Selection Criteria**:
- Content quality from preview phase
- Source authority (academic, news, official)
- Relevance to research query
- Complementary information (avoid duplicates)

### Step 4: Process (Content Enhancement)
**Status**: ⚠️ **MISSING - Needs Implementation**

**Proposed**: `contentProcessorServer`
```typescript
tools: [
  'clean_content',       // Remove boilerplate, ads, navigation
  'summarize_content',   // Generate abstracts/summaries  
  'chunk_content',       // Split into semantic sections
  'extract_entities',    // NER, keywords, citations
  'quality_score',       // Assess content reliability
  'detect_language',     // Handle multilingual content
  'extract_citations'    // Academic reference extraction
]
```

### Step 5: Aggregate (Data Synthesis)
**Status**: ⚠️ **MISSING - Needs Implementation**

**Proposed**: `dataAggregatorServer`
```typescript
tools: [
  'merge_results',           // Combine multiple sources
  'deduplicate_content',     // Remove redundant information
  'rank_by_relevance',       // Score and prioritize content
  'create_knowledge_graph',  // Build topic relationships
  'identify_conflicts',      // Flag contradictory information
  'generate_timeline',       // Chronological organization
  'extract_key_insights'     // Main findings summary
]
```

### Step 6: Generate (Report Creation)
**Status**: ✅ **Available**

| Tool | Purpose | Output |
|------|---------|---------|
| **Artifacts** | Research report generation | Structured documents, visualizations |

## 🛠️ Current Tool Inventory

### ✅ Available Tools

#### Search Servers
- **bochaSearchServer**: API-based web + AI search
- **braveSearchServer**: API-based web + local search  
- **webScraperServer**: API-free DuckDuckGo search
- **rssServer**: RSS feed aggregation for news/blogs

#### Content Extraction Servers
- **webScraperServer**: Lightweight URL content extraction
- **puppeteerServer**: Advanced browser automation
- **advancedCrawlerServer**: Research-focused crawling

#### Output Servers
- **Artifacts**: Document and visualization generation

### ⚠️ Missing Components
- **contentProcessorServer**: Content cleaning and enhancement
- **dataAggregatorServer**: Multi-source data synthesis

## 🎯 Research Workflow Examples

### Academic Research Workflow
```yaml
query: "quantum computing error correction 2024"
steps:
  1. search: bochaSearch + rssServer (academic feeds)
  2. crawl: webScraperServer (preview 25 papers)
  3. extract: advancedCrawlerServer (top 5 papers)
  4. process: [MISSING] clean + extract citations
  5. aggregate: [MISSING] merge findings + timeline
  6. generate: Artifacts (research report)
```

### News Research Workflow  
```yaml
query: "AI regulation developments this week"
steps:
  1. search: rssServer + bochaSearch (news focus)
  2. crawl: webScraperServer (preview 30 articles)
  3. extract: puppeteerServer (dynamic news sites)
  4. process: [MISSING] summarize + timeline
  5. aggregate: [MISSING] merge + detect conflicts
  6. generate: Artifacts (news briefing)
```

### Business Intelligence Workflow
```yaml
query: "competitor analysis SaaS market 2024"
steps:
  1. search: braveSearch + bochaSearch + rssServer
  2. crawl: webScraperServer (preview 40 sources)
  3. extract: mix of all extraction tools
  4. process: [MISSING] entity extraction + scoring
  5. aggregate: [MISSING] competitive landscape
  6. generate: Artifacts (business report)
```

## 🚀 Implementation Priorities

### Phase 1: Complete Current Architecture ✅
- [x] Search servers (bochaSearch, braveSearch, webScraperServer, rssServer)
- [x] Extraction servers (webScraperServer, puppeteerServer, advancedCrawlerServer)
- [x] Output server (Artifacts)

### Phase 2: Fill Critical Gaps 🎯
- [ ] **contentProcessorServer** - Content cleaning and enhancement
- [ ] **dataAggregatorServer** - Multi-source synthesis

### Phase 3: Advanced Features 🔮
- [ ] Template-driven research workflows
- [ ] Automated quality assessment
- [ ] Multi-language support
- [ ] Real-time research monitoring

## 💡 Key Advantages

1. **API Independence**: webScraperServer + rssServer provide API-free options
2. **Intelligent Routing**: LLMs choose optimal tools for each task
3. **Progressive Refinement**: Broad discovery → Smart filtering → Deep analysis
4. **Cost Efficiency**: Preview before expensive deep extraction
5. **Ethical Compliance**: Rate limiting, robots.txt compliance, PII detection
6. **Scalability**: Modular design allows easy addition of new capabilities

## 🎯 Success Metrics

- **Coverage**: Ability to research any topic with appropriate tool selection
- **Efficiency**: Minimize API calls and processing time through smart filtering
- **Quality**: High-relevance results through LLM-guided source selection
- **Reliability**: Graceful fallbacks when primary tools fail
- **Ethics**: Respectful crawling practices and compliance

---

*This architecture provides a foundation for comprehensive AI-driven research while maintaining flexibility for diverse research needs and ethical web practices.*
