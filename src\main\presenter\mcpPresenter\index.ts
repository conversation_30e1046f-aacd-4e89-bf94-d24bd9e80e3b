import {
  IMCPPresenter,
  MCPServerConfig,
  MCPToolDefinition,
  MCPToolCall,
  McpClient,
  MCPToolResponse,
  Prompt,
  ResourceListEntry,
  Resource,
  PromptListEntry
} from '@shared/presenter'
import { ServerManager } from './serverManager'
import { ToolManager } from './toolManager'
import { eventBus } from '@/eventbus'
import { MCP_EVENTS, NOTIFICATION_EVENTS } from '@/events'
import { IConfigPresenter } from '@shared/presenter'
import { getErrorMessageLabels } from '@shared/i18n'
import { OpenAI } from 'openai'
import { ToolListUnion, Type, FunctionDeclaration } from '@google/genai'
import { CONFIG_EVENTS } from '@/events'
import { presenter } from '@/presenter'

// Define MCP Tool Interface
interface MCPTool {
  id: string
  name: string
  type: string
  description: string
  serverName: string
  inputSchema: {
    properties: Record<string, Record<string, unknown>>
    required: string[]
    [key: string]: unknown
  }
}

// Define tool type interfaces for each LLM provider
interface OpenAIToolCall {
  function: {
    name: string
    arguments: string
  }
}

interface AnthropicToolUse {
  name: string
  input: Record<string, unknown>
}

interface GeminiFunctionCall {
  name: string
  args: Record<string, unknown>
}

// Define tool conversion interfaces
interface OpenAITool {
  type: 'function'
  function: {
    name: string
    description: string
    parameters: {
      type: string
      properties: Record<string, Record<string, unknown>>
      required: string[]
    }
  }
}

interface AnthropicTool {
  name: string
  description: string
  input_schema: {
    type: string
    properties: Record<string, Record<string, unknown>>
    required: string[]
  }
}

// Full version of McpPresenter implementation
export class McpPresenter implements IMCPPresenter {
  private serverManager: ServerManager
  private toolManager: ToolManager
  private configPresenter: IConfigPresenter
  private isInitialized: boolean = false

  constructor(configPresenter?: IConfigPresenter) {
    console.log('Initializing MCP Presenter')

    this.configPresenter = configPresenter || presenter.configPresenter
    this.serverManager = new ServerManager(this.configPresenter)
    this.toolManager = new ToolManager(this.configPresenter, this.serverManager)

    // 监听自定义提示词服务器检查事件
    eventBus.on(CONFIG_EVENTS.CUSTOM_PROMPTS_SERVER_CHECK_REQUIRED, async () => {
      await this.checkAndManageCustomPromptsServer()
    })

    // 延迟初始化，确保其他组件已经准备好
    setTimeout(() => {
      this.initialize()
    }, 1000)
  }

  private async initialize() {
    try {
      // 如果没有提供configPresenter，从presenter中获取
      if (!this.configPresenter.getLanguage) {
        // 重新创建管理器
        this.serverManager = new ServerManager(this.configPresenter)
        this.toolManager = new ToolManager(this.configPresenter, this.serverManager)
      }

      // 加载配置
      const [servers, defaultServers] = await Promise.all([
        this.configPresenter.getMcpServers(),
        this.configPresenter.getMcpDefaultServers()
      ])

      // 先测试npm registry速度
      console.log('[MCP] Testing npm registry speed...')
      try {
        await this.serverManager.testNpmRegistrySpeed()
        console.log(
          `[MCP] npm registry speed test completed, selected best registry: ${this.serverManager.getNpmRegistry()}`
        )
      } catch (error) {
        console.error('[MCP] npm registry speed test failed:', error)
      }

      // 检查并启动 docomoe-inmemory/custom-prompts-server
      const customPromptsServerName = 'docomoe-inmemory/custom-prompts-server'
      if (servers[customPromptsServerName]) {
        console.log(`[MCP] Attempting to start custom prompts server: ${customPromptsServerName}`)

        try {
          await this.serverManager.startServer(customPromptsServerName)
          console.log(`[MCP] Custom prompts server ${customPromptsServerName} started successfully`)

          // 通知渲染进程服务器已启动
          eventBus.emit(MCP_EVENTS.SERVER_STARTED, customPromptsServerName)
        } catch (error) {
          console.error(
            `[MCP] Failed to start custom prompts server ${customPromptsServerName}:`,
            error
          )
        }
      }

      // 如果有默认服务器，尝试启动
      if (defaultServers.length > 0) {
        for (const serverName of defaultServers) {
          if (servers[serverName]) {
            console.log(`[MCP] Attempting to start default server: ${serverName}`)

            try {
              await this.serverManager.startServer(serverName)
              console.log(`[MCP] Default server ${serverName} started successfully`)

              // 通知渲染进程服务器已启动
              eventBus.emit(MCP_EVENTS.SERVER_STARTED, serverName)
            } catch (error) {
              console.error(`[MCP] Failed to start default server ${serverName}:`, error)
            }
          }
        }
      }

      // 标记初始化完成并发出事件
      this.isInitialized = true
      console.log('[MCP] Initialization completed')
      eventBus.emit(MCP_EVENTS.INITIALIZED)

      // 检查并管理自定义提示词服务器
      await this.checkAndManageCustomPromptsServer()
    } catch (error) {
      console.error('[MCP] Initialization failed:', error)
      // 即使初始化失败也标记为已完成，避免系统卡在未初始化状态
      this.isInitialized = true
      eventBus.emit(MCP_EVENTS.INITIALIZED)
    }
  }

  // 添加获取初始化状态的方法
  isReady(): boolean {
    return this.isInitialized
  }

  // 检查并管理自定义提示词服务器
  private async checkAndManageCustomPromptsServer(): Promise<void> {
    const customPromptsServerName = 'docomoe-inmemory/custom-prompts-server'

    try {
      // 获取当前自定义提示词
      const customPrompts = await this.configPresenter.getCustomPrompts()
      const hasCustomPrompts = customPrompts && customPrompts.length > 0

      // 检查服务器是否正在运行
      const isServerRunning = this.serverManager.isServerRunning(customPromptsServerName)

      if (hasCustomPrompts && !isServerRunning) {
        // 有自定义提示词但服务器未运行，启动服务器
        try {
          await this.serverManager.startServer(customPromptsServerName)
          eventBus.emit(MCP_EVENTS.SERVER_STARTED, customPromptsServerName)
        } catch (error) {
          // 启动失败
        }
      } else if (!hasCustomPrompts && isServerRunning) {
        // 没有自定义提示词但服务器正在运行，停止服务器
        try {
          await this.serverManager.stopServer(customPromptsServerName)
          eventBus.emit(MCP_EVENTS.SERVER_STOPPED, customPromptsServerName)
        } catch (error) {
          // 停止失败
        }
      } else if (hasCustomPrompts && isServerRunning) {
        // 有自定义提示词且服务器正在运行，重启服务器以刷新缓存
        try {
          await this.serverManager.stopServer(customPromptsServerName)
          await this.serverManager.startServer(customPromptsServerName)
          eventBus.emit(MCP_EVENTS.SERVER_STARTED, customPromptsServerName)
        } catch (error) {
          // 重启失败
        }
      }

      // 通知客户端列表已更新
      eventBus.emit(MCP_EVENTS.CLIENT_LIST_UPDATED)
    } catch (error) {
      // 处理错误
    }
  }

  // 获取MCP服务器配置
  getMcpServers(): Promise<Record<string, MCPServerConfig>> {
    return this.configPresenter.getMcpServers()
  }

  // Get all MCP servers
  async getMcpClients(): Promise<McpClient[]> {
    const clients = await this.toolManager.getRunningClients()
    const clientsList: McpClient[] = []
    for (const client of clients) {
      const results: MCPToolDefinition[] = []
      const tools = await client.listTools()
      for (const tool of tools) {
        const properties = tool.inputSchema.properties || {}
        const toolProperties = { ...properties }
        for (const key in toolProperties) {
          if (!toolProperties[key].description) {
            toolProperties[key].description = 'Params of ' + key
          }
        }
        results.push({
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description,
            parameters: {
              type: 'object',
              properties: toolProperties,
              required: Array.isArray(tool.inputSchema.required) ? tool.inputSchema.required : []
            }
          },
          server: {
            name: client.serverName,
            icons: client.serverConfig['icons'] as string,
            description: client.serverConfig['description'] as string
          }
        })
      }

      // Create client basic information object
      const clientObj: McpClient = {
        name: client.serverName,
        icon: client.serverConfig['icons'] as string,
        isRunning: client.isServerRunning(),
        tools: results
      }

      // Check and add prompts (if supported)
      if (typeof client.listPrompts === 'function') {
        try {
          const prompts = await client.listPrompts()
          if (prompts && prompts.length > 0) {
            clientObj.prompts = prompts.map((prompt) => ({
              id: prompt.name,
              name: prompt.name,
              content: prompt.description || '',
              description: prompt.description || '',
              arguments: prompt.arguments || [],
              client: {
                name: client.serverName,
                icon: client.serverConfig['icons'] as string
              }
            }))
          }
        } catch (error) {
          console.error(
            `[MCP] Failed to get prompt templates for client ${client.serverName}:`,
            error
          )
        }
      }

      // Check and add resources (if supported)
      if (typeof client.listResources === 'function') {
        try {
          const resources = await client.listResources()
          if (resources && resources.length > 0) {
            clientObj.resources = resources
          }
        } catch (error) {
          console.error(`[MCP] Failed to get resources for client ${client.serverName}:`, error)
        }
      }

      clientsList.push(clientObj)
    }
    return clientsList
  }

  // Get all default MCP servers
  getMcpDefaultServers(): Promise<string[]> {
    return this.configPresenter.getMcpDefaultServers()
  }

  // Add default MCP server
  async addMcpDefaultServer(serverName: string): Promise<void> {
    await this.configPresenter.addMcpDefaultServer(serverName)
  }

  // Remove default MCP server
  async removeMcpDefaultServer(serverName: string): Promise<void> {
    await this.configPresenter.removeMcpDefaultServer(serverName)
  }

  // Toggle the default status of the server
  async toggleMcpDefaultServer(serverName: string): Promise<void> {
    await this.configPresenter.toggleMcpDefaultServer(serverName)
  }

  // Add MCP server
  async addMcpServer(serverName: string, config: MCPServerConfig): Promise<boolean> {
    const existingServers = await this.getMcpServers()
    if (existingServers[serverName]) {
      console.error(`[MCP] Failed to add server: Server name "${serverName}" already exists.`)
      // Get current language and send notification
      const locale = this.configPresenter.getLanguage?.() || 'en-US'
      const errorMessages = getErrorMessageLabels(locale)
      eventBus.emit(NOTIFICATION_EVENTS.SHOW_ERROR, {
        title: errorMessages.addMcpServerErrorTitle || 'Failed to add server',
        message:
          errorMessages.addMcpServerDuplicateMessage?.replace('{serverName}', serverName) ||
          `Server name "${serverName}" already exists. Please choose a different name.`,
        id: `mcp-error-add-server-${serverName}-${Date.now()}`,
        type: 'error'
      })
      return false
    }
    await this.configPresenter.addMcpServer(serverName, config)
    return true
  }

  // Update MCP server configuration
  async updateMcpServer(serverName: string, config: Partial<MCPServerConfig>): Promise<void> {
    const wasRunning = this.serverManager.isServerRunning(serverName)
    await this.configPresenter.updateMcpServer(serverName, config)

    // If the server was running previously, restart it to apply the new configuration
    if (wasRunning) {
      console.log(`[MCP] Configuration updated, restarting server: ${serverName}`)
      try {
        await this.stopServer(serverName) // stopServer will emit SERVER_STOPPED event
        await this.startServer(serverName) // startServer will emit SERVER_STARTED event
        console.log(`[MCP] Server ${serverName} restarted successfully`)
      } catch (error) {
        console.error(`[MCP] Failed to restart server ${serverName}:`, error)
        // Even if the restart fails, ensure the status is correct, marked as not running
        eventBus.emit(MCP_EVENTS.SERVER_STOPPED, serverName)
      }
    }
  }

  // Remove MCP server
  async removeMcpServer(serverName: string): Promise<void> {
    // If the server is running, stop it first
    if (await this.isServerRunning(serverName)) {
      await this.stopServer(serverName)
    }
    await this.configPresenter.removeMcpServer(serverName)
  }

  async isServerRunning(serverName: string): Promise<boolean> {
    return Promise.resolve(this.serverManager.isServerRunning(serverName))
  }

  async startServer(serverName: string): Promise<void> {
    await this.serverManager.startServer(serverName)
    // 通知渲染进程服务器已启动
    eventBus.emit(MCP_EVENTS.SERVER_STARTED, serverName)
  }

  async stopServer(serverName: string): Promise<void> {
    await this.serverManager.stopServer(serverName)
    // 通知渲染进程服务器已停止
    eventBus.emit(MCP_EVENTS.SERVER_STOPPED, serverName)
  }

  async getAllToolDefinitions(): Promise<MCPToolDefinition[]> {
    const enabled = await this.configPresenter.getMcpEnabled()
    if (enabled) {
      return this.toolManager.getAllToolDefinitions()
    }
    return []
  }

  /**
   * Get all client prompts, and attach client information
   * @returns all prompt template list, each prompt template with client information
   */
  async getAllPrompts(): Promise<Array<PromptListEntry>> {
    const enabled = await this.configPresenter.getMcpEnabled()
    if (!enabled) {
      return []
    }

    const clients = await this.toolManager.getRunningClients()
    const promptsList: Array<Prompt & { client: { name: string; icon: string } }> = []

    for (const client of clients) {
      if (typeof client.listPrompts === 'function') {
        try {
          const prompts = await client.listPrompts()
          if (prompts && prompts.length > 0) {
            // Add client information to each prompt template
            const clientPrompts = prompts.map((prompt) => ({
              id: prompt.name,
              name: prompt.name,
              description: prompt.description || '',
              content: prompt.description || '', // Add required content property
              arguments: prompt.arguments || [],
              client: {
                name: client.serverName,
                icon: client.serverConfig['icons'] as string
              }
            }))
            promptsList.push(...clientPrompts)
          }
        } catch (error) {
          console.error(
            `[MCP] Failed to get prompt templates for client ${client.serverName}:`,
            error
          )
        }
      }
    }

    return promptsList
  }

  /**
   * Get all client resources, and attach client information
   * @returns all resource list, each resource with client information
   */
  async getAllResources(): Promise<
    Array<ResourceListEntry & { client: { name: string; icon: string } }>
  > {
    const enabled = await this.configPresenter.getMcpEnabled()
    if (!enabled) {
      return []
    }

    const clients = await this.toolManager.getRunningClients()
    const resourcesList: Array<ResourceListEntry & { client: { name: string; icon: string } }> = []

    for (const client of clients) {
      if (typeof client.listResources === 'function') {
        try {
          const resources = await client.listResources()
          if (resources && resources.length > 0) {
            // Add client information to each resource
            const clientResources = resources.map((resource) => ({
              ...resource,
              client: {
                name: client.serverName,
                icon: client.serverConfig['icons'] as string
              }
            }))
            resourcesList.push(...clientResources)
          }
        } catch (error) {
          console.error(`[MCP] Failed to get resources for client ${client.serverName}:`, error)
        }
      }
    }

    return resourcesList
  }

  async callTool(request: MCPToolCall): Promise<{ content: string; rawData: MCPToolResponse }> {
    const toolCallResult = await this.toolManager.callTool(request)

    // Format tool call result to a string that is easy for large models to parse
    let formattedContent = ''

    // Determine content type
    if (typeof toolCallResult.content === 'string') {
      // Content is already a string
      formattedContent = toolCallResult.content
    } else if (Array.isArray(toolCallResult.content)) {
      // Content is a structured array, needs to be formatted
      const contentParts: string[] = []

      // Process each content item
      for (const item of toolCallResult.content) {
        if (item.type === 'text') {
          contentParts.push(item.text)
        } else if (item.type === 'image') {
          contentParts.push(`[Image: ${item.mimeType}]`)
        } else if (item.type === 'resource') {
          if ('text' in item.resource && item.resource.text) {
            contentParts.push(`[Resource: ${item.resource.uri}]\n${item.resource.text}`)
          } else if ('blob' in item.resource) {
            contentParts.push(`[Binary Resource: ${item.resource.uri}]`)
          } else {
            contentParts.push(`[Resource: ${item.resource.uri}]`)
          }
        } else {
          // Handle other unknown types
          contentParts.push(JSON.stringify(item))
        }
      }

      // Merge all content
      formattedContent = contentParts.join('\n\n')
    }

    // Add error flag (if any)
    if (toolCallResult.isError) {
      formattedContent = `Error: ${formattedContent}`
    }

    return { content: formattedContent, rawData: toolCallResult }
  }

  // Convert MCPToolDefinition to MCPTool
  private mcpToolDefinitionToMcpTool(
    toolDefinition: MCPToolDefinition,
    serverName: string
  ): MCPTool {
    const mcpTool = {
      id: toolDefinition.function.name,
      name: toolDefinition.function.name,
      type: toolDefinition.type,
      description: toolDefinition.function.description,
      serverName,
      inputSchema: {
        properties: toolDefinition.function.parameters.properties as Record<
          string,
          Record<string, unknown>
        >,
        type: toolDefinition.function.parameters.type,
        required: toolDefinition.function.parameters.required
      }
    } as MCPTool
    return mcpTool
  }

  // Tool property filtering function
  private filterPropertieAttributes(tool: MCPTool): Record<string, Record<string, unknown>> {
    const supportedAttributes = [
      'type',
      'nullable',
      'description',
      'properties',
      'items',
      'enum',
      'anyOf'
    ]

    const properties = tool.inputSchema.properties
    const getSubMap = (obj: Record<string, unknown>, keys: string[]): Record<string, unknown> => {
      return Object.fromEntries(Object.entries(obj).filter(([key]) => keys.includes(key)))
    }

    const result: Record<string, Record<string, unknown>> = {}
    for (const [key, val] of Object.entries(properties)) {
      result[key] = getSubMap(val, supportedAttributes)
    }

    return result
  }

  // Add new tool conversion method
  /**
   * Convert MCP tool definition to OpenAI tool format
   * @param mcpTools MCP tool definition array
   * @param serverName server name
   * @returns OpenAI tool format tool definition
   */
  async mcpToolsToOpenAITools(
    mcpTools: MCPToolDefinition[],
    serverName: string
  ): Promise<OpenAITool[]> {
    const openaiTools: OpenAITool[] = mcpTools.map((toolDef) => {
      const tool = this.mcpToolDefinitionToMcpTool(toolDef, serverName)
      return {
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description,
          parameters: {
            type: 'object',
            properties: this.filterPropertieAttributes(tool),
            required: tool.inputSchema.required || []
          }
        }
      }
    })
    // console.log('openaiTools', JSON.stringify(openaiTools))
    return openaiTools
  }

  /**
   * Convert OpenAI tool call back to MCP tool call
   * @param mcpTools MCP tool definition array
   * @param llmTool OpenAI tool call
   * @param serverName server name
   * @returns matching MCP tool call
   */
  async openAIToolsToMcpTool(
    llmTool: OpenAIToolCall,
    providerId: string
  ): Promise<MCPToolCall | undefined> {
    const mcpTools = await this.getAllToolDefinitions()
    const tool = mcpTools.find((tool) => tool.function.name === llmTool.function.name)
    if (!tool) {
      return undefined
    }

    // Create MCP tool call
    const mcpToolCall: MCPToolCall = {
      id: `${providerId}:${tool.function.name}-${Date.now()}`, // Generate unique ID, containing server name
      type: tool.type,
      function: {
        name: tool.function.name,
        arguments: llmTool.function.arguments
      },
      server: {
        name: tool.server.name,
        icons: tool.server.icons,
        description: tool.server.description
      }
    }
    // console.log('mcpToolCall', mcpToolCall, tool)

    return mcpToolCall
  }

  /**
   * Convert MCP tool definition to Anthropic tool format
   * @param mcpTools MCP tool definition array
   * @param serverName server name
   * @returns Anthropic tool format tool definition
   */
  async mcpToolsToAnthropicTools(
    mcpTools: MCPToolDefinition[],
    serverName: string
  ): Promise<AnthropicTool[]> {
    return mcpTools.map((toolDef) => {
      const tool = this.mcpToolDefinitionToMcpTool(toolDef, serverName)
      return {
        name: tool.name,
        description: tool.description,
        input_schema: {
          type: 'object',
          properties: tool.inputSchema.properties,
          required: tool.inputSchema.required as string[]
        }
      }
    })
  }

  /**
   * Convert Anthropic tool use back to MCP tool call
   * @param mcpTools MCP tool definition array
   * @param toolUse Anthropic tool use
   * @param serverName server name
   * @returns matching MCP tool call
   */
  async anthropicToolUseToMcpTool(
    toolUse: AnthropicToolUse,
    providerId: string
  ): Promise<MCPToolCall | undefined> {
    const mcpTools = await this.getAllToolDefinitions()

    const tool = mcpTools.find((tool) => tool.function.name === toolUse.name)
    // console.log('tool', tool, toolUse)
    if (!tool) {
      return undefined
    }

    // Create MCP tool call
    const mcpToolCall: MCPToolCall = {
      id: `${providerId}:${tool.function.name}-${Date.now()}`, // Generate unique ID, containing server name
      type: tool.type,
      function: {
        name: tool.function.name,
        arguments: JSON.stringify(toolUse.input)
      },
      server: {
        name: tool.server.name,
        icons: tool.server.icons,
        description: tool.server.description
      }
    }

    return mcpToolCall
  }

  /**
   * Convert MCP tool definition to Gemini tool format
   * @param mcpTools MCP tool definition array
   * @param serverName server name
   * @returns Gemini tool format tool definition
   */
  async mcpToolsToGeminiTools(
    mcpTools: MCPToolDefinition[] | undefined,
    serverName: string
  ): Promise<ToolListUnion> {
    if (!mcpTools || mcpTools.length === 0) {
      return []
    }

    // 递归清理Schema对象，确保符合Gemini API要求
    const cleanSchema = (schema: Record<string, unknown>): Record<string, unknown> => {
      const cleanedSchema: Record<string, unknown> = {}

      // 处理type字段 - 确保始终有有效值
      if ('type' in schema) {
        const type = schema.type
        if (typeof type === 'string' && type.trim() !== '') {
          cleanedSchema.type = type
        } else if (Array.isArray(type) && type.length > 0) {
          // 如果是类型数组，取第一个非空类型
          const validType = type.find((t) => typeof t === 'string' && t.trim() !== '')
          if (validType) {
            cleanedSchema.type = validType
          } else {
            cleanedSchema.type = 'string' // 默认类型
          }
        } else {
          // 如果没有有效的type，根据其他属性推断
          if ('enum' in schema) {
            cleanedSchema.type = 'string'
          } else if ('properties' in schema) {
            cleanedSchema.type = 'object'
          } else if ('items' in schema) {
            cleanedSchema.type = 'array'
          } else {
            cleanedSchema.type = 'string' // 默认类型
          }
        }
      } else {
        // 如果完全没有type字段，根据其他属性推断
        if ('enum' in schema) {
          cleanedSchema.type = 'string'
        } else if ('properties' in schema) {
          cleanedSchema.type = 'object'
        } else if ('items' in schema) {
          cleanedSchema.type = 'array'
        } else if ('anyOf' in schema || 'oneOf' in schema) {
          // 对于union类型，尝试推断最合适的类型
          cleanedSchema.type = 'string' // 默认为string
        } else {
          cleanedSchema.type = 'string' // 最终默认类型
        }
      }

      // 处理description
      if ('description' in schema && typeof schema.description === 'string') {
        cleanedSchema.description = schema.description
      }

      // 处理enum
      if ('enum' in schema && Array.isArray(schema.enum)) {
        cleanedSchema.enum = schema.enum
        // 确保enum类型是string
        if (!cleanedSchema.type || cleanedSchema.type === '') {
          cleanedSchema.type = 'string'
        }
      }

      // 处理properties
      if (
        'properties' in schema &&
        typeof schema.properties === 'object' &&
        schema.properties !== null
      ) {
        const properties = schema.properties as Record<string, unknown>
        const cleanedProperties: Record<string, unknown> = {}

        for (const [propName, propValue] of Object.entries(properties)) {
          if (typeof propValue === 'object' && propValue !== null) {
            cleanedProperties[propName] = cleanSchema(propValue as Record<string, unknown>)
          }
        }

        if (Object.keys(cleanedProperties).length > 0) {
          cleanedSchema.properties = cleanedProperties
          cleanedSchema.type = 'object'
        }
      }

      // 处理items (数组类型)
      if ('items' in schema && typeof schema.items === 'object' && schema.items !== null) {
        cleanedSchema.items = cleanSchema(schema.items as Record<string, unknown>)
        cleanedSchema.type = 'array'
      }

      // 处理nullable
      if ('nullable' in schema && typeof schema.nullable === 'boolean') {
        cleanedSchema.nullable = schema.nullable
      }

      // 处理anyOf/oneOf (union类型) - 简化为单一类型
      if ('anyOf' in schema && Array.isArray(schema.anyOf)) {
        const anyOfOptions = schema.anyOf as Array<Record<string, unknown>>

        // 尝试找到最适合的类型
        let bestOption = anyOfOptions[0]

        // 优先选择有enum的选项
        for (const option of anyOfOptions) {
          if ('enum' in option && Array.isArray(option.enum)) {
            bestOption = option
            break
          }
        }

        // 如果没有enum，优先选择string类型
        if (!('enum' in bestOption)) {
          for (const option of anyOfOptions) {
            if (option.type === 'string') {
              bestOption = option
              break
            }
          }
        }

        // 递归清理选中的选项
        const cleanedOption = cleanSchema(bestOption)
        Object.assign(cleanedSchema, cleanedOption)
      }

      // 处理oneOf类似anyOf
      if ('oneOf' in schema && Array.isArray(schema.oneOf)) {
        const oneOfOptions = schema.oneOf as Array<Record<string, unknown>>
        const bestOption = oneOfOptions[0] || {}
        const cleanedOption = cleanSchema(bestOption)
        Object.assign(cleanedSchema, cleanedOption)
      }

      // 最终检查：确保必须有type字段
      if (!cleanedSchema.type || cleanedSchema.type === '') {
        cleanedSchema.type = 'string'
      }

      return cleanedSchema
    }

    // 处理每个工具定义，构建符合Gemini API的函数声明
    const functionDeclarations = mcpTools.map((toolDef) => {
      // 转换为内部工具表示
      const tool = this.mcpToolDefinitionToMcpTool(toolDef, serverName)

      // 获取参数属性
      const properties = tool.inputSchema.properties
      const processedProperties: Record<string, Record<string, unknown>> = {}

      // 处理每个属性，应用清理函数
      for (const [propName, propValue] of Object.entries(properties)) {
        if (typeof propValue === 'object' && propValue !== null) {
          const cleaned = cleanSchema(propValue as Record<string, unknown>)
          // 确保清理后的属性有有效的type
          if (cleaned.type && cleaned.type !== '') {
            processedProperties[propName] = cleaned
          } else {
            console.warn(`[MCP] Skipping property ${propName} due to invalid type`)
          }
        }
      }

      // 准备函数声明结构
      const functionDeclaration: FunctionDeclaration = {
        name: tool.id,
        description: tool.description
      }

      if (Object.keys(processedProperties).length > 0) {
        functionDeclaration.parameters = {
          type: Type.OBJECT,
          properties: processedProperties,
          required: tool.inputSchema.required || []
        }
      }

      // 记录没有参数的函数
      if (Object.keys(processedProperties).length === 0) {
        console.log(
          `[MCP] Function ${tool.id} has no parameters, providing minimal parameter structure`
        )
      }

      return functionDeclaration
    })

    // 返回符合Gemini工具格式的结果
    return [
      {
        functionDeclarations
      }
    ]
  }

  /**
   * Convert Gemini function call back to MCP tool call
   * @param mcpTools MCP tool definition array
   * @param fcall Gemini function call
   * @param serverName server name
   * @returns matching MCP tool call
   */
  async geminiFunctionCallToMcpTool(
    fcall: GeminiFunctionCall | undefined,
    providerId: string
  ): Promise<MCPToolCall | undefined> {
    const mcpTools = await this.getAllToolDefinitions()
    if (!fcall) return undefined
    if (!mcpTools) return undefined

    const tool = mcpTools.find((tool) => tool.function.name === fcall.name)
    if (!tool) {
      return undefined
    }

    // Create MCP tool call
    const mcpToolCall: MCPToolCall = {
      id: `${providerId}:${tool.function.name}-${Date.now()}`, // Generate unique ID, containing server name
      type: tool.type,
      function: {
        name: tool.function.name,
        arguments: JSON.stringify(fcall.args)
      },
      server: {
        name: tool.server.name,
        icons: tool.server.icons,
        description: tool.server.description
      }
    }

    return mcpToolCall
  }

  // Get MCP enabled status
  async getMcpEnabled(): Promise<boolean> {
    return this.configPresenter.getMcpEnabled()
  }

  // Set MCP enabled status
  async setMcpEnabled(enabled: boolean): Promise<void> {
    await this.configPresenter?.setMcpEnabled(enabled)
  }

  async resetToDefaultServers(): Promise<void> {
    await this.configPresenter?.getMcpConfHelper().resetToDefaultServers()
  }

  /**
   * Get specified prompt template
   * @param prompt prompt template object (contains client information)
   * @param params prompt template parameters
   * @returns prompt template content
   */
  async getPrompt(prompt: PromptListEntry, args?: Record<string, unknown>): Promise<unknown> {
    const enabled = await this.configPresenter.getMcpEnabled()
    if (!enabled) {
      throw new Error('MCP feature is disabled')
    }

    // Pass client information and prompt template name to toolManager
    return this.toolManager.getPromptByClient(prompt.client.name, prompt.name, args)
  }

  /**
   * Read specified resource
   * @param resource resource object (contains client information)
   * @returns resource content
   */
  async readResource(resource: ResourceListEntry): Promise<Resource> {
    const enabled = await this.configPresenter.getMcpEnabled()
    if (!enabled) {
      throw new Error('MCP feature is disabled')
    }

    // Pass client information and resource URI to toolManager
    return this.toolManager.readResourceByClient(resource.client.name, resource.uri)
  }

  /**
   * Convert MCP tool definition to OpenAI Responses API tool format
   * @param mcpTools MCP tool definition array
   * @param serverName server name
   * @returns OpenAI Responses API tool format tool definition
   */
  async mcpToolsToOpenAIResponsesTools(
    mcpTools: MCPToolDefinition[],
    serverName: string
  ): Promise<OpenAI.Responses.Tool[]> {
    const openaiTools: OpenAI.Responses.Tool[] = mcpTools.map((toolDef) => {
      const tool = this.mcpToolDefinitionToMcpTool(toolDef, serverName)
      return {
        type: 'function',
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object',
          properties: this.filterPropertieAttributes(tool),
          required: tool.inputSchema.required || []
        },
        strict: false
      }
    })
    return openaiTools
  }
}
