import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js'
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js'
import { InMemoryTransport } from '@modelcontextprotocol/sdk/inMemory.js'
import { type Transport } from '@modelcontextprotocol/sdk/shared/transport.js'
import { eventBus } from '@/eventbus'
import { MCP_EVENTS } from '@/events'
import path from 'path'
import { presenter } from '@/presenter'
import { app } from 'electron'
import fs from 'fs'
// import { NO_PROXY, proxyConfig } from '@/presenter/proxyConfig'
import { getInMemoryServer } from './inMemoryServers/builder'
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js'
import {
  PromptListEntry,
  ToolCallResult,
  Tool,
  Prompt,
  ResourceListEntry,
  Resource
} from '@shared/presenter'
// TODO: Types for resources, prompts and notifications https://github.com/modelcontextprotocol/typescript-sdk/blob/main/src/examples/client/simpleStreamableHttp.ts
// Simple OAuth provider for handling Bearer Token
class SimpleOAuthProvider {
  private token: string | null = null

  constructor(authHeader: string | undefined) {
    if (authHeader && authHeader.toLowerCase().startsWith('bearer ')) {
      this.token = authHeader.substring(7) // Remove 'Bearer ' prefix
    }
  }

  async tokens(): Promise<{ access_token: string } | null> {
    if (this.token) {
      return { access_token: this.token }
    }
    return null
  }
}

// Ensure TypeScript can recognize SERVER_STATUS_CHANGED property
type MCPEventsType = typeof MCP_EVENTS & {
  SERVER_STATUS_CHANGED: string
}

// MCP client class
export class McpClient {
  private client: Client | null = null
  private transport: Transport | null = null
  public serverName: string
  public serverConfig: Record<string, unknown>
  private isConnected: boolean = false
  private connectionTimeout: NodeJS.Timeout | null = null
  private nodeRuntimePath: string | null = null
  private npmRegistry: string | null = null

  // Cache
  private cachedTools: Tool[] | null = null
  private cachedPrompts: PromptListEntry[] | null = null
  private cachedResources: ResourceListEntry[] | null = null

  // Function to handle PATH environment variables
  private normalizePathEnv(paths: string[]): { key: string; value: string } {
    const isWindows = process.platform === 'win32'
    const separator = isWindows ? ';' : ':'
    const pathKey = isWindows ? 'Path' : 'PATH'

    // Merge all paths
    const pathValue = paths.filter(Boolean).join(separator)

    return { key: pathKey, value: pathValue }
  }

  // Get system-specific default paths
  private getDefaultPaths(homeDir: string): string[] {
    if (process.platform === 'darwin') {
      return [
        '/bin',
        '/usr/bin',
        '/usr/local/bin',
        '/usr/local/sbin',
        '/opt/homebrew/bin',
        '/opt/homebrew/sbin',
        '/usr/local/opt/node/bin',
        '/opt/local/bin',
        `${homeDir}/.cargo/bin`
      ]
    } else if (process.platform === 'linux') {
      return ['/bin', '/usr/bin', '/usr/local/bin', `${homeDir}/.cargo/bin`]
    } else {
      // Windows
      return [`${homeDir}\\.cargo\\bin`, `${homeDir}\\.local\\bin`]
    }
  }

  constructor(
    serverName: string,
    serverConfig: Record<string, unknown>,
    npmRegistry: string | null = null
  ) {
    this.serverName = serverName
    this.serverConfig = serverConfig
    this.npmRegistry = npmRegistry

    const runtimePath = path
      .join(app.getAppPath(), 'runtime', 'node')
      .replace('app.asar', 'app.asar.unpacked')
    console.info('runtimePath', runtimePath)
    // Check if runtime files exist
    if (process.platform === 'win32') {
      const nodeExe = path.join(runtimePath, 'node.exe')
      const npxCmd = path.join(runtimePath, 'npx.cmd')
      if (fs.existsSync(nodeExe) && fs.existsSync(npxCmd)) {
        this.nodeRuntimePath = runtimePath
      } else {
        this.nodeRuntimePath = null
      }
    } else {
      const nodeBin = path.join(runtimePath, 'bin', 'node')
      const npxBin = path.join(runtimePath, 'bin', 'npx')
      if (fs.existsSync(nodeBin) && fs.existsSync(npxBin)) {
        this.nodeRuntimePath = runtimePath
      } else {
        this.nodeRuntimePath = null
      }
    }
  }

  // Connect to MCP server
  async connect(): Promise<void> {
    if (this.isConnected && this.client) {
      console.info(`MCP server ${this.serverName} is already running`)
      return
    }

    try {
      console.info(`Starting MCP server ${this.serverName}...`, this.serverConfig)

      // Handle customHeaders and AuthProvider
      let authProvider: SimpleOAuthProvider | null = null
      const customHeaders = this.serverConfig.customHeaders
        ? { ...(this.serverConfig.customHeaders as Record<string, string>) } // Create a copy for modification
        : {}

      if (customHeaders.Authorization) {
        authProvider = new SimpleOAuthProvider(customHeaders.Authorization)
        delete customHeaders.Authorization // Remove from headers because it will be handled by AuthProvider
      }

      if (this.serverConfig.type === 'inmemory') {
        const [clientTransport, serverTransport] = InMemoryTransport.createLinkedPair()
        const _args = Array.isArray(this.serverConfig.args) ? this.serverConfig.args : []
        const _env = this.serverConfig.env ? (this.serverConfig.env as Record<string, string>) : {}
        const _server = getInMemoryServer(this.serverName, _args, _env)
        _server.startServer(serverTransport)
        this.transport = clientTransport
      } else if (this.serverConfig.type === 'stdio') {
        // Create appropriate transport
        const command = this.serverConfig.command as string
        const HOME_DIR = app.getPath('home')

        // Define allowlist of environment variables
        const allowedEnvVars = [
          'PATH',
          'path',
          'Path',
          'npm_config_registry',
          'npm_config_cache',
          'npm_config_prefix',
          'npm_config_tmp',
          'NPM_CONFIG_REGISTRY',
          'NPM_CONFIG_CACHE',
          'NPM_CONFIG_PREFIX',
          'NPM_CONFIG_TMP'
          // 'GRPC_PROXY',
          // 'grpc_proxy'
        ]

        // Fix env type problem
        const env: Record<string, string> = {}

        // Check if it is a Node.js-related command
        const isNodeCommand = ['node', 'npm', 'npx'].some((cmd) => command.includes(cmd))

        if (isNodeCommand) {
          // Node.js commands use allowlist processing
          if (process.env) {
            const existingPaths: string[] = []

            // Collect all PATH-related values
            Object.entries(process.env).forEach(([key, value]) => {
              if (value !== undefined) {
                if (['PATH', 'Path', 'path'].includes(key)) {
                  existingPaths.push(value)
                } else if (
                  allowedEnvVars.includes(key) &&
                  !['PATH', 'Path', 'path'].includes(key)
                ) {
                  env[key] = value
                }
              }
            })

            // Get default paths
            const defaultPaths = this.getDefaultPaths(HOME_DIR)

            // Merge all paths
            const allPaths = [...existingPaths, ...defaultPaths]
            if (this.nodeRuntimePath) {
              allPaths.unshift(
                process.platform === 'win32' ? this.nodeRuntimePath : `${this.nodeRuntimePath}/bin`
              )
            }

            // Normalize and set PATH
            const { key, value } = this.normalizePathEnv(allPaths)
            env[key] = value
          }
        } else {
          // Non-Node.js commands, retain all system environment variables, supplement PATH
          Object.entries(process.env).forEach(([key, value]) => {
            if (value !== undefined) {
              env[key] = value
            }
          })

          // Supplement PATH
          const existingPaths: string[] = []
          if (env.PATH) {
            existingPaths.push(env.PATH)
          }
          if (env.Path) {
            existingPaths.push(env.Path)
          }

          // Get default paths
          const defaultPaths = this.getDefaultPaths(HOME_DIR)

          // Merge all paths
          const allPaths = [...existingPaths, ...defaultPaths]
          if (this.nodeRuntimePath) {
            allPaths.unshift(
              process.platform === 'win32' ? this.nodeRuntimePath : `${this.nodeRuntimePath}/bin`
            )
          }

          // Normalize and set PATH
          const { key, value } = this.normalizePathEnv(allPaths)
          env[key] = value
        }

        // Add custom environment variables
        if (this.serverConfig.env) {
          Object.entries(this.serverConfig.env as Record<string, string>).forEach(
            ([key, value]) => {
              if (value !== undefined) {
                // If it is a PATH-related variable, merge it into the main PATH
                if (['PATH', 'Path', 'path'].includes(key)) {
                  const currentPathKey = process.platform === 'win32' ? 'Path' : 'PATH'
                  const separator = process.platform === 'win32' ? ';' : ':'
                  env[currentPathKey] = env[currentPathKey]
                    ? `${value}${separator}${env[currentPathKey]}`
                    : value
                } else {
                  env[key] = value
                }
              }
            }
          )
        }

        if (this.npmRegistry) {
          env.npm_config_registry = this.npmRegistry
        }

        console.log('mcp env', env)
        this.transport = new StdioClientTransport({
          command,
          args: this.serverConfig.args as string[],
          env,
          stderr: 'pipe'
        })
      } else if (this.serverConfig.baseUrl && this.serverConfig.type === 'sse') {
        this.transport = new SSEClientTransport(new URL(this.serverConfig.baseUrl as string), {
          requestInit: { headers: customHeaders },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          authProvider: (authProvider ?? undefined) as any
        })
      } else if (this.serverConfig.baseUrl && this.serverConfig.type === 'http') {
        this.transport = new StreamableHTTPClientTransport(
          new URL(this.serverConfig.baseUrl as string),
          {
            requestInit: { headers: customHeaders },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            authProvider: (authProvider ?? undefined) as any
          }
        )
      } else {
        throw new Error(`Unsupported transport type: ${this.serverConfig.type}`)
      }

      // Create MCP client
      this.client = new Client(
        { name: 'Docomoe', version: app.getVersion() },
        {
          capabilities: {
            resources: {},
            tools: {},
            prompts: {}
          }
        }
      )

      // Set connection timeout
      const timeoutPromise = new Promise<void>((_, reject) => {
        this.connectionTimeout = setTimeout(
          () => {
            console.error(`Connection to MCP server ${this.serverName} timed out`)
            reject(new Error(`Connection to MCP server ${this.serverName} timed out`))
          },
          5 * 60 * 1000
        ) // 5 minutes
      })

      // Connect to server
      const connectPromise = this.client
        .connect(this.transport)
        .then(() => {
          // Clear timeout
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout)
            this.connectionTimeout = null
          }

          this.isConnected = true
          console.info(`MCP server ${this.serverName} connected successfully`)

          // Trigger server status change event
          eventBus.emit((MCP_EVENTS as MCPEventsType).SERVER_STATUS_CHANGED, {
            name: this.serverName,
            status: 'running'
          })
        })
        .catch((error) => {
          console.error(`Failed to connect to MCP server ${this.serverName}:`, error)
          throw error
        })

      // Wait for connection to complete or timeout
      await Promise.race([connectPromise, timeoutPromise])
    } catch (error) {
      // Clear timeout
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout)
        this.connectionTimeout = null
      }

      // Clean up resources
      this.cleanupResources()

      console.error(`Failed to connect to MCP server ${this.serverName}:`, error)

      // Trigger server status change event
      eventBus.emit((MCP_EVENTS as MCPEventsType).SERVER_STATUS_CHANGED, {
        name: this.serverName,
        status: 'stopped'
      })

      throw error
    }
  }

  // Disconnect from MCP server
  async disconnect(): Promise<void> {
    if (!this.isConnected || !this.client) {
      return
    }

    try {
      // Clean up resources
      this.cleanupResources()

      console.log(`Disconnected from MCP server: ${this.serverName}`)

      // Trigger server status change event
      eventBus.emit((MCP_EVENTS as MCPEventsType).SERVER_STATUS_CHANGED, {
        name: this.serverName,
        status: 'stopped'
      })
    } catch (error) {
      console.error(`Failed to disconnect from MCP server ${this.serverName}:`, error)
      throw error
    }
  }

  // Clean up resources
  private cleanupResources(): void {
    // Clear timeout timer
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    // Close transport
    if (this.transport) {
      try {
        this.transport.close()
      } catch (error) {
        console.error(`Failed to close MCP transport:`, error)
      }
    }

    // Reset status
    this.client = null
    this.transport = null
    this.isConnected = false

    // Empty cache
    this.cachedTools = null
    this.cachedPrompts = null
    this.cachedResources = null
  }

  // Check if the server is running
  isServerRunning(): boolean {
    return this.isConnected && !!this.client
  }

  // Call MCP tool
  async callTool(toolName: string, args: Record<string, unknown>): Promise<ToolCallResult> {
    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      // Call tool
      const result = (await this.client.callTool({
        name: toolName,
        arguments: args
      })) as ToolCallResult

      // Check result
      if (result.isError) {
        const errorText = result.content && result.content[0] ? result.content[0].text : 'Unknown Error'
        // If the call fails, empty the tool cache so that it can be retrieved again next time
        this.cachedTools = null
        return {
          isError: true,
          content: [{ type: 'error', text: errorText }]
        }
      }
      return result
    } catch (error) {
      console.error(`Failed to call MCP tool ${toolName}:`, error)
      // Call failed, empty tool cache
      this.cachedTools = null
      throw error
    }
  }

  // List available tools
  async listTools(): Promise<Tool[]> {
    // Check cache
    if (this.cachedTools !== null) {
      return this.cachedTools
    }

    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      const response = await this.client.listTools()
      // Check response format
      if (response && typeof response === 'object' && 'tools' in response) {
        const toolsArray = response.tools
        if (Array.isArray(toolsArray)) {
          // Cache results
          this.cachedTools = toolsArray as Tool[]
          return this.cachedTools
        }
      }
      throw new Error('Invalid tool response format')
    } catch (error) {
      // Try to extract more information from the error object
      const errorMessage = error instanceof Error ? error.message : String(error)
      // If the error indicates that it is not supported, cache an empty array
      if (errorMessage.includes('Method not found') || errorMessage.includes('not supported')) {
        console.warn(`Server ${this.serverName} does not support listTools`)
        this.cachedTools = []
        return this.cachedTools
      } else {
        console.error(`Failed to list MCP tools:`, error)
        // For other errors, do not clear cache (keep null) to allow retry next time
        throw error
      }
    }
  }

  // List available prompts
  async listPrompts(): Promise<PromptListEntry[]> {
    // Check cache
    if (this.cachedPrompts !== null) {
      return this.cachedPrompts
    }

    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      // SDK may not have a listPrompts method, which requires using a generic request
      const response = await this.client.listPrompts()

      // Check response format
      if (response && typeof response === 'object' && 'prompts' in response) {
        const promptsArray = (response as { prompts: unknown }).prompts
        // console.log('promptsArray', JSON.stringify(promptsArray, null, 2))
        if (Array.isArray(promptsArray)) {
          // Ensure each element conforms to the Prompt interface
          const validPrompts = promptsArray.map((p) => ({
            name: typeof p === 'object' && p !== null && 'name' in p ? String(p.name) : 'unknown',
            description:
              typeof p === 'object' && p !== null && 'description' in p
                ? String(p.description)
                : undefined,
            arguments:
              typeof p === 'object' && p !== null && 'arguments' in p ? p.arguments : undefined
          })) as PromptListEntry[]
          // Cache results
          this.cachedPrompts = validPrompts
          return this.cachedPrompts
        }
      }
      throw new Error('Invalid prompt response format')
    } catch (error) {
      // Try to extract more information from the error object
      const errorMessage = error instanceof Error ? error.message : String(error)
      // If the error indicates that it is not supported, cache an empty array
      if (errorMessage.includes('Method not found') || errorMessage.includes('not supported')) {
        console.warn(`Server ${this.serverName} does not support listPrompts`)
        this.cachedPrompts = []
        return this.cachedPrompts
      } else {
        console.error(`Failed to list MCP prompts:`, error)
        // An error occurred, do not clear the cache (keep null) for the next try
        throw error
      }
    }
  }

  // Get a specific prompt
  async getPrompt(name: string, args?: Record<string, unknown>): Promise<Prompt> {
    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      const response = await this.client.getPrompt({
        name,
        arguments: (args as Record<string, string>) || {}
      })
      // Check response format and convert to Prompt type
      if (
        response &&
        typeof response === 'object' &&
        'messages' in response &&
        Array.isArray(response.messages)
      ) {
        return {
          id: name,
          name: name, // Get name from request parameters
          description: response.description || '',
          content: '', // Add required content property
          messages: response.messages as Array<{ role: string; content: { text: string } }>
        }
      }
      throw new Error('Invalid get prompt response format')
    } catch (error) {
      console.error(`Failed to get MCP prompt ${name}:`, error)
      // Get failed, clear prompt cache
      this.cachedPrompts = null
      throw error
    }
  }

  // List available resources
  async listResources(): Promise<ResourceListEntry[]> {
    // Check cache
    if (this.cachedResources !== null) {
      return this.cachedResources
    }

    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      // SDK may not have a listResources method, which requires using a generic request
      const response = await this.client.listResources()

      // Check response format
      if (response && typeof response === 'object' && 'resources' in response) {
        const resourcesArray = (response as { resources: unknown }).resources
        if (Array.isArray(resourcesArray)) {
          // Ensure each element conforms to the ResourceListEntry interface
          const validResources = resourcesArray.map((r) => ({
            uri: typeof r === 'object' && r !== null && 'uri' in r ? String(r.uri) : 'unknown',
            name: typeof r === 'object' && r !== null && 'name' in r ? String(r.name) : undefined
          })) as ResourceListEntry[]
          // Cache results
          this.cachedResources = validResources
          return this.cachedResources
        }
      }
      throw new Error('Invalid resource list response format')
    } catch (error) {
      // Try to extract more information from the error object
      const errorMessage = error instanceof Error ? error.message : String(error)
      // If the error indicates that it is not supported, cache an empty array
      if (errorMessage.includes('Method not found') || errorMessage.includes('not supported')) {
        console.warn(`Server ${this.serverName} does not support listResources`)
        this.cachedResources = []
        return this.cachedResources
      } else {
        console.error(`Failed to list MCP resources:`, error)
        // An error occurred, do not clear the cache (keep null) for the next try
        throw error
      }
    }
  }

  // Read resource
  async readResource(resourceUri: string): Promise<Resource> {
    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.client) {
      throw new Error(`MCP client ${this.serverName} not initialized`)
    }

    try {
      // Use unknown as an intermediate type for conversion
      const rawResource = await this.client.readResource({ uri: resourceUri })

      // Manually construct Resource object
      const resource: Resource = {
        uri: resourceUri,
        text:
          typeof rawResource === 'object' && rawResource !== null && 'text' in rawResource
            ? String(rawResource['text'])
            : JSON.stringify(rawResource)
      }

      return resource
    } catch (error) {
      console.error(`Failed to read MCP resource ${resourceUri}:`, error)
      // Read failed, clear resource cache
      this.cachedResources = null
      throw error
    }
  }
}

// Factory function, used to create MCP client
export async function createMcpClient(serverName: string): Promise<McpClient> {
  // Get MCP server configuration from configPresenter
  const servers = await presenter.configPresenter.getMcpServers()

  // Get server configuration
  const serverConfig = servers[serverName]
  if (!serverConfig) {
    throw new Error(`MCP server ${serverName} not found in configuration`)
  }

  // Create and return MCP client, pass null as npmRegistry
  // Note: this function should only be used in the case of directly creating client instances
  // It should be created through ServerManager normally, so that the tested npm registry can be used
  return new McpClient(serverName, serverConfig as unknown as Record<string, unknown>, null)
}
