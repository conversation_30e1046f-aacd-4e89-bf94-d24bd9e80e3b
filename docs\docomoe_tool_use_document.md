
# Docomoe Tool Use Technical Documentation

## Background and Goals

As LLM (large models) support Function Calling capabilities improve, Docomoe uses **prompt engineering (prompt engineering)**, even without native function calling, it can **simulate Tool Use behavior**.

Design goals:
- Guide models to call tools in a standardized prompt wrapper
- Adapt to various LLMs, including models that do not support native Function Calling
- Support extension to multiple tool use and complex scheduling scenarios

---

## Overview of Structure

| Component                     | Description                                                    |
| :---------------------------- | :-------------------------------------------------------------- |
| `baseProvider.ts`             | Define the basic prompt encapsulation and Tool Use logic       |
| `openAICompatibleProvider.ts` | Implement interaction and function call parsing with OpenAI, etc. |
| Core Function                 | `getFunctionCallWrapPrompt`, `coreStream`, `parseFunctionCalls` |

---

## Overall Process Overview

1. **Prompt Encapsulation**: Wrap user input and Tools with `getFunctionCallWrapPrompt`
2. **Stream Processing**: Receive the delta stream returned by the model with `coreStream`
3. **Function Call Parsing**: Extract Tool Call JSON from natural language with `parseFunctionCalls`

---

## Core Module Explanation

### 1. getFunctionCallWrapPrompt(prompt, functions):

**Function**: Pack the original user prompt and available Tools, guide the LLM to return Tool Call in the specified JSON format.

**Main Logic**:
- List all functions (including name and parameter format)
- Define the standard format, such as:
```json
{ "tool_name": "xxx", "parameters": { "key": "value" } }
```
- Insert the original user input, keeping the natural consistency

**Core Idea**: Let models that do not support native Function Calling understand "can call tools".

---

### 2. coreStream(config):

**Function**: Responsible for sending requests to LLM in a stream and receiving delta data in a stream and processing them in real time.

**Processing Details**:
- Receive delta each time:
  - Detect whether it contains `content`
  - Reassemble each character segment to ensure that it conforms to the JSON format
  - Encapsulate it into a new reply string to prevent fragmentation or disorder
- Reassemble processing:
  - Detect the Tool Call JSON feature (such as `{ "tool_name"` at the beginning)
  - Merge the text segments that may be cut off
  - Detect the complete Tool Call JSON, immediately call `parseFunctionCalls`

**State Machine**: The stream data processing process adopts the state machine model to step by step process the returned delta data.

1. **Receive delta**: Enter the receive state and analyze whether it contains `content`.

```mermaid
stateDiagram-v2
    [*] --> receive delta
    receive delta --> extract and reassemble
    extract and reassemble --> whether contains Tool Call
    whether contains Tool Call --> parseFunctionCalls : yes
    whether contains Tool Call --> continue to add : no
    parseFunctionCalls --> complete call
    continue to add --> [*]
```

---

### 3. parseFunctionCalls(text):

**Function**: Extracts Tool Call JSON in the specified format from natural language output, and parses it into a standard JS Object.

**Main Logic**:
- Regular expression matching `{...}` structure
- Support multiple Tool Call exist at the same time
- Error tolerance correction for abnormal JSON (exceeding characters, missing quotes, etc.)

---

## Overall Flowchart (Mermaid)

```mermaid
flowchart TD
    A[user input prompt] --> B[getFunctionCallWrapPrompt]
    B --> C[wrapped prompt]
    C --> D[coreStream stream send]
    D --> E[LLM return delta]
    E --> F{delta has content}
    F -- yes --> G[extract characters, reassemble]
    G --> H{contains Tool Call}
    H -- yes --> I[parseFunctionCalls]
    H -- no --> J{may contain Tool Call}
    J -- yes --> K[continue to add content]
    J -- no --> L[add content and clear]
    I --> M[return Tool Call result]
    K --> G
    L --> G
```
---

## Sequence Diagram (Mermaid)

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant BaseProvider
    participant LLM
    participant Parser

    User->>Frontend: submit prompt
    Frontend->>BaseProvider: getFunctionCallWrapPrompt(prompt, functions)
    BaseProvider-->>Frontend: return wrapped prompt

    Frontend->>LLM: send request
    LLM-->>Frontend: stream return delta

    loop stream processing
        Frontend->>Frontend: extract characters, reassemble
        Frontend->>Frontend: detect whether there is Tool Call JSON
        alt detect Tool Call
            Frontend->>Parser: parseFunctionCalls(text)
            Parser-->>Frontend: return function call result
        end
    end

    Frontend->>User: display final result
```

---

## Design Highlights

- **Prompt Smart Encapsulation**: Adapt to various models, independent of native technology
- **Stream Processing Fine**: Character-level reassembly and verification
- **Tool Call High Error Tolerance Parsing**: Support irregular, complex, and multi-tool use

---

## Future Enhancements

- Adaptive Prompt Adjustment, guided by model type
- Support nested Tool Call (tool calling within tools)
- Inheritance and independent management of Tool Use in multi-round dialogs