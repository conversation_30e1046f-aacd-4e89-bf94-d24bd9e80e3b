# ConfigPresenter Module Design Document

## Functional Overview

ConfigPresenter is the core configuration management module of Docomoe, responsible for managing various configuration items of the application, including:

1. Application basic settings (language, proxy, sync, etc.)
2. LLM provider configuration
3. Model management (standard models and custom models)
4. MCP (Model Control Protocol) server configuration
5. Data migration and version compatibility handling

## Core Design

### 1. Storage Architecture

ConfigPresenter adopts a hierarchical storage design:

- **Main Configuration Storage**: Use ElectronStore to store application basic settings
- **Model Storage**: Each LLM provider has an independent ElectronStore instance
- **Status Storage**: Model enable status is stored separately in the main configuration

```mermaid
classDiagram
    class ConfigPresenter {
        -store: ElectronStore<IAppSettings>
        -providersModelStores: Map<string, ElectronStore<IModelStore>>
        -userDataPath: string
        -currentAppVersion: string
        -mcpConfHelper: McpConfHelper
        +getSetting()
        +setSetting()
        +getProviders()
        +setProviders()
        +getModelStatus()
        +setModelStatus()
        +getMcpServers()
        +setMcpServers()
    }
```

### 2. Main Interface

#### Application Settings Management

- `getSetting<T>(key: string): T | undefined`
- `setSetting<T>(key: string, value: T): void`

#### Provider Management

- `getProviders(): LLM_PROVIDER[]`
- `setProviders(providers: LLM_PROVIDER[]): void`
- `getProviderById(id: string): LLM_PROVIDER | undefined`
- `setProviderById(id: string, provider: LLM_PROVIDER): void`

#### Model Management

- `getProviderModels(providerId: string): MODEL_META[]`
- `setProviderModels(providerId: string, models: MODEL_META[]): void`
- `getCustomModels(providerId: string): MODEL_META[]`
- `setCustomModels(providerId: string, models: MODEL_META[]): void`
- `addCustomModel(providerId: string, model: MODEL_META): void`
- `removeCustomModel(providerId: string, modelId: string): void`

#### MCP Configuration Management

- `getMcpServers(): Promise<Record<string, MCPServerConfig>>`
- `setMcpServers(servers: Record<string, MCPServerConfig>): Promise<void>`
- `getMcpEnabled(): Promise<boolean>`
- `setMcpEnabled(enabled: boolean): Promise<void>`

### 3. Event System

ConfigPresenter emits the following configuration change events through eventBus:

| Event Name                                | Trigger Time         | Parameters                   |
| ----------------------------------------- | -------------------- | ---------------------------- |
| CONFIG_EVENTS.SETTING_CHANGED             | Any configuration item changes | key, value                   |
| CONFIG_EVENTS.PROVIDER_CHANGED            | Provider list changes | -                            |
| CONFIG_EVENTS.MODEL_STATUS_CHANGED        | Model enable status changes | providerId, modelId, enabled |
| CONFIG_EVENTS.MODEL_LIST_CHANGED          | Model list changes   | providerId                   |
| CONFIG_EVENTS.PROXY_MODE_CHANGED          | Proxy mode changes   | mode                         |
| CONFIG_EVENTS.CUSTOM_PROXY_URL_CHANGED    | Custom proxy URL changes | url                          |
| CONFIG_EVENTS.ARTIFACTS_EFFECT_CHANGED    | Animation effect setting changes | enabled                      |
| CONFIG_EVENTS.SYNC_SETTINGS_CHANGED       | Sync settings changes | { enabled, folderPath }      |
| CONFIG_EVENTS.CONTENT_PROTECTION_CHANGED  | Screen protection setting changes | enabled                      |

### 4. Data Migration Mechanism

ConfigPresenter implements version-aware data migration:

```mermaid
sequenceDiagram
    participant ConfigPresenter
    participant ElectronStore
    participant FileSystem

    ConfigPresenter->>ElectronStore: Check version number
    alt Version inconsistent
        ConfigPresenter->>FileSystem: Migrate old data
        ConfigPresenter->>ElectronStore: Update version number
    end
```

Migration logic includes:

1. Model data is migrated from the main configuration to independent storage
2. Model status is separated from the model object and stored in independent storage
3. Specific URL format correction for specific providers

## Usage Example

### Get current language setting

```typescript
const language = configPresenter.getLanguage()
```

### Add custom model

```typescript
configPresenter.addCustomModel('openai', {
  id: 'gpt-4-custom',
  name: 'GPT-4 Custom',
  maxTokens: 8192
  // ...other properties
})
```

### Enable MCP feature

```typescript
await configPresenter.setMcpEnabled(true)
```

## Best Practices

1. **Configuration Access**: Always access configuration through the getSetting/setSetting methods, without directly operating the store
2. **Event Listening**: Parts that are interested in configuration changes should listen to the corresponding events, rather than polling checks
3. **Model Management**: Custom models should be managed through dedicated methods, avoiding direct operations on storage
4. **Version Compatibility**: Consider default values and migration logic when adding new configuration items

## Extensibility Design

1. **IAppSettings Interface**: Use index signatures to allow arbitrary configuration keys
2. **McpConfHelper**: Separate MCP-related logic into auxiliary classes
3. **Provider Identification**: Support dynamic providers through the providerId string rather than enumerations
