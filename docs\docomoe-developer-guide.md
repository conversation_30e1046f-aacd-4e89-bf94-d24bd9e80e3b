# Docomoe Developer Guide

This guide provides information for developers looking to understand, build, and contribute to the Docomoe project.

## 📑 Table of Contents

- [Project Structure](#project-structure)
- [Architecture Overview](#architecture-overview)
  - [Electron Architecture](#electron-architecture)
  - [Technology Stack](#technology-stack)
  - [Architectural Documents](#architectural-documents)
- [API Documentation](#api-documentation)
- [Model Controller Platform (MCP)](#model-controller-platform-mcp)
- [Development Setup](#development-setup)
- [Building the Application](#building-the-application)
- [Contribution Guidelines](#contribution-guidelines)

## Project Structure

The Docomoe repository is organized into several key directories:

-   **`src/`**: Contains the core source code of the application.
    -   **`src/main/`**: Code for Electron's main process (handles window management, system events, backend logic). Written in TypeScript.
    -   **`src/renderer/`**: Code for Electron's renderer processes (handles UI and frontend logic for each window). Built with Vue.js and TypeScript.
    -   **`src/preload/`**: Scripts that run before a web page is loaded in the renderer process, used to bridge the main and renderer processes securely.
    -   **`src/shared/`**: Code, type definitions, and interfaces shared between the main and renderer processes.
-   **`docs/`**: Contains design documents, user guides, and this developer guide.
-   **`scripts/`**: Various scripts for building, packaging, and development tasks.
-   **`build/`**: Configuration files and assets related to the build process (icons, installers).
-   **`resources/`**: Static assets used by the application at runtime.
-   **`runtime/`**: Contains runtime environment for features like MCP (e.g. Node.js runtime).
-   **`.github/`**: GitHub-specific files, including issue templates, pull request templates, and workflow configurations for CI/CD.

For more details, refer to the "Project Structure" section in [CONTRIBUTING.md](./CONTRIBUTING.md).

## Architecture Overview

### Electron Architecture

Docomoe is an Electron application. This means it has:
-   **Main Process**: A single process that is the entry point of the application. It runs Node.js and has access to system-level operations. It's responsible for creating and managing application windows (renderer processes) and handling application lifecycle events. The code for the main process is in `src/main/`.
-   **Renderer Processes**: Each window in Docomoe runs its own renderer process. This process is responsible for rendering web content (HTML, CSS, JavaScript). The UI is built using web technologies. The code for the renderer process is in `src/renderer/`.
-   **Preload Scripts**: These scripts run in a privileged context in the renderer process and can expose specific Node.js APIs or main process functionalities to the renderer process via an IPC (Inter-Process Communication) bridge. See `src/preload/`.
-   **Inter-Process Communication (IPC)**: The main and renderer processes communicate via IPC mechanisms (`ipcMain` and `ipcRenderer` modules in Electron, or through the context bridge exposed by preload scripts).

### Technology Stack

-   **Backend (Main Process)**: TypeScript
-   **Frontend (Renderer Process)**: Vue.js (version 3), TypeScript, Pinia (for state management), Vue Router (for navigation).
-   **Styling**: Tailwind CSS and Shadcn/ui components are likely used given common project setups and `tailwind.config.js`, `components.json`.
-   **Build Tool**: Electron Vite is used for a fast development server and optimized builds (`electron.vite.config.ts`).
-   **Packaging**: Electron Builder (`electron-builder.yml`).

### Architectural Documents

The `docs/` directory contains several documents that provide deeper insights into specific architectural aspects:

-   [Multi-Window Architecture](./multi-window-architecture.md): Describes how multiple windows are managed.
-   [Event System Design](./event-system-design.md): Details the application's event system.
-   [Config Presenter Architecture](./config-presenter-architecture.md) and [Config Presenter Design](./config-presenter-design.md): Explain the configuration management system.
-   [MCP Presenter Architecture](./mcp-presenter-architecture.md) and [MCP Presenter Design](./mcp-presenter-design.md): Detail the architecture of the Model Controller Platform.

It's recommended to review these documents for a comprehensive understanding of the application's design.

## API Documentation

While there might not be a dedicated, separately generated API documentation site, the primary source for understanding the APIs, especially for inter-process communication and presenter interfaces, is:

-   **`shared/presenter.d.ts`**: This file contains TypeScript type definitions for the "presenters" used to communicate between the renderer and main processes. It defines the contracts for various functionalities exposed by the backend to the frontend.
-   **`src/preload/index.d.ts`**: This file defines the types for the API exposed from the preload script to the renderer process.

Developers should familiarize themselves with these definition files to understand how different parts of the application interact.

## Model Controller Platform (MCP)

The Model Controller Platform (MCP) is a core feature of Docomoe, enabling advanced capabilities like tool calling and search enhancement. As described in the `README.md`:

-   It allows LLMs to use **Resources**, **Prompts**, and **Tools**.
-   It supports features like code execution (via a built-in Node.js runtime), web information retrieval, and file operations.
-   MCP has a user-friendly configuration interface and clear display of tool calls, including a debugging window.
-   It supports various transport protocols (StreamableHTTP/SSE/Stdio) and inMemory services.

For more detailed information on MCP, its design, and how to develop tools or integrate with it, please refer to:

-   [Function Call and MCP](./function-call-and-mcp.md)
-   [MCP Presenter Architecture](./mcp-presenter-architecture.md)
-   [MCP Presenter Design](./mcp-presenter-design.md)
-   The "Excellent MCP (Model Controller Platform) Support" section in the main [README.md](../README.md).

## Development Setup

To set up your local development environment for Docomoe:

1.  **Clone the repository.**
2.  **Install Node.js** (Latest LTS version recommended).
3.  **Install OS-specific dependencies** (e.g., build tools for C++, Git).
4.  **Install project dependencies** (typically using `pnpm install`).

Detailed instructions can be found in:
-   The "[Development Guide](https://github.com/Calmren/docomoe#%EF%B8%8F-development-guide)" section of the main `README.md` file.
-   The "[Local Development Setup](https://github.com/Calmren/docomoe/blob/main/CONTRIBUTING.md#local-development-setup)" section of `CONTRIBUTING.md`.

After setup, you can usually start the development server using `pnpm run dev`.

## Building the Application

Instructions for building the Docomoe application for different platforms (Windows, macOS, Linux) and architectures are available in the `README.md`:

-   Refer to the "[Build](https://github.com/Calmren/docomoe#build)" section in the main `README.md` file for the necessary commands (e.g., `pnpm run build:win`, `pnpm run build:linux`).

The `README.md` also mentions that Windows and Linux packaging is handled by GitHub Actions.

## Contribution Guidelines

If you wish to contribute to Docomoe, please review the contribution guidelines. They cover:

-   Development process for internal and external contributors.
-   Coding style (ESLint, Prettier).
-   Pull Request process.

The full guidelines can be found in:
-   [CONTRIBUTING.md](./CONTRIBUTING.md)
-   The "[Community & Contribution](https://github.com/Calmren/docomoe#community--contribution)" section of the main `README.md` file.

---

This guide should provide a good starting point for developers. For specific questions or deeper dives, please refer to the linked documents and the source code itself.
-   Project Structure: Based on `ls()` output and `CONTRIBUTING.md`.
-   Architecture Overview: Explaining Electron's main/renderer architecture, the tech stack (Vue.js, TypeScript), and linking to relevant documents in `docs/` that I identified earlier.
-   API Documentation: Pointing to `shared/presenter.d.ts` and `src/preload/index.d.ts`.
-   Model Controller Platform (MCP): Explaining its purpose based on `README.md` and linking to `docs/function-call-and-mcp.md` and other MCP-specific architectural documents.
-   Development Setup: Linking to the relevant sections in `README.md` and `CONTRIBUTING.md`.
-   Building the Application: Linking to the relevant section in `README.md`.
-   Contribution Guidelines: Linking to `CONTRIBUTING.md`.

The paths for links to `README.md` sections are constructed assuming the `developer-guide.md` is in the `docs/` directory. For `CONTRIBUTING.md`, I've used a relative link `./CONTRIBUTING.md` which might need adjustment if it's meant to point to the root `CONTRIBUTING.md` (then it should be `../CONTRIBUTING.md`). I will use `../CONTRIBUTING.md` and `../README.md` for files in the root directory, and `./filename.md` for files within the `docs/` directory itself. I've updated the draft to reflect this.
