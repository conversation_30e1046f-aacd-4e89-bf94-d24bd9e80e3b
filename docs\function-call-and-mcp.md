# From MCP to Tool Use

- [From MCP to Tool Use](#from-mcp-to-tool-use)
  - [MCP Tools Mapping and Definition](#mcp-tools-mapping-and-definition)
  - [Anthropic tool API and Context Organization Format](#anthropic-tool-api-and-context-organization-format)
    - [Format Conversion](#format-conversion)
    - [Context Organization](#context-organization)
    - [Streaming Processing](#streaming-processing)
  - [Example: Anthropic's Tool Use Implementation](#example-anthropic's-tool-use-implementation)
    - [Tool Definition](#tool-definition)
    - [User Request Example](#user-request-example)
    - [Large Model Response](#large-model-response)
    - [MCP Module Execution Command](#mcp-module-execution-command)
    - [Final Large Model Response with Context](#final-large-model-response-with-context)
  - [Gemini tool API and Context Organization Format](#gemini-tool-api-and-context-organization-format)
    - [Format Conversion](#format-conversion-1)
    - [Context Organization](#context-organization-1)
    - [Streaming Processing](#streaming-processing-1)
  - [Example: Gemini's Tool Use Implementation](#example-gemini's-tool-use-implementation)
    - [Tool Definition](#tool-definition-1)
    - [User Request Example](#user-request-example-1)
    - [Large Model Response (Calling Tools)](#large-model-response-calling-tools)
    - [MCP Module Execution Command](#mcp-module-execution-command-1)
  - [OpenAI tool API and Context Organization Format](#openai-tool-api-and-context-organization-format)
    - [Format Conversion](#format-conversion-2)
    - [Context Organization](#context-organization-2)
    - [Streaming Processing](#streaming-processing-2)
  - [Example: OpenAI's Tool Use Implementation](#example-openais-tool-use-implementation)
    - [Tool Definition](#tool-definition-2)
    - [User Request Example](#user-request-example-2)
    - [Large Model Response (Calling Tools)](#large-model-response-calling-tools-1)
    - [MCP Module Execution Command](#mcp-module-execution-command-2)
    - [Final Large Model Response with Context](#final-large-model-response-with-context-1)
  - [How to Implement Tool Use for Models that Do Not Support Native Tool Use, and How to Correctly Parse Streaming Content for Function Information](#how-to-implement-tool-use-for-models-that-do-not-support-native-tool-use-and-how-to-correctly-parse-streaming-content-for-function-information)
    - [Prompt Wrapping](#prompt-wrapping)
    - [Streaming Content Parsing](#streaming-content-parsing)

## MCP Tools Mapping and Definition

MCP (Model Context Protocol) is a protocol for standardizing interactions with various models. In this project, MCP tool definitions are managed by the `McpClient` class, providing a consistent tool call interface for different LLM providers.

The basic structure of MCP tools is defined as follows (there are additional official comment fields that are not currently used):
```typescript
{
  name: string;          // Unique identifier for the tool
  description?: string;  // Human-readable description
  inputSchema: {         // JSON Schema for the tool's parameters
    type: "object",
    properties: { ... }  // Tool-specific parameters
  }
}
```

The `callTool` method of `mcpClient.ts` can be used to call tools across providers:
```typescript
async callTool(toolName: string, args: Record<string, unknown>): Promise<ToolCallResult>
```

The tool call result follows a unified format:
```typescript
interface ToolCallResult {
  isError?: boolean;
  content: Array<{
    type: string;
    text: string;
  }>;
}
```

When MCP tools need to be mapped to different providers, the following process is used:
1. Use the `presenter.mcpPresenter.mcpToolsToOpenAITools`, `presenter.mcpPresenter.mcpToolsToAnthropicTools`, or `presenter.mcpPresenter.mcpToolsToGeminiTools` methods to convert.
2. These methods convert the `inputSchema` of MCP tools to the expected parameter formats of each provider.
3. Ensure that the tool name and description are consistent during the conversion process.

## Anthropic tool API and Context Organization Format

Anthropic's Tool API is implemented by the `AnthropicProvider` class, which supports tool use capabilities in the Claude 3 series.

### Format Conversion
Anthropic requires the tool definition to be passed through the `tools` parameter, following the following structure:
```typescript
{
  tools: [
    {
      name: string;
      description: string;
      input_schema: object; // JSON Schema
    }
  ]
}
```

### Context Organization
Anthropic has special requirements for message formats, especially for the tool-related message structures:
1. System messages (system): Independent of the conversation messages, passed through the `system` parameter
2. User messages (user): Contains an `content` array, which can contain text and images
3. Assistant messages (assistant): Can contain tool calls, using `tool_use` type content blocks
4. Tool responses: As part of the user message, using `tool_result` type content blocks

`formatMessages` method responsible for converting standard chat messages to Anthropic format:
```typescript
private formatMessages(messages: ChatMessage[]): {
  system?: string;
  messages: Anthropic.MessageParam[];
}
```

### Streaming Processing
The tool call events returned by the Claude API include:
- `content_block_start` (type `tool_use`): Tool call start
- `content_block_delta` (with `input_json_delta`): Tool parameter streaming update
- `content_block_stop`: Tool call end
- `message_delta` (with `stop_reason: 'tool_use'`): Stop generation due to tool call

These events are converted to standardized `LLMCoreStreamEvent` events:
```typescript
{
  type: 'tool_call_start' | 'tool_call_chunk' | 'tool_call_end';
  tool_call_id?: string;
  tool_call_name?: string;
  tool_call_arguments_chunk?: string;
  tool_call_arguments_complete?: string;
}
```

## Example: Anthropic's Tool Use Implementation
### Tool Definition
First, define a getTime tool:
``` json
{
  "name": "getTime",
  "description": "Get the timestamp (in milliseconds) for a specific time offset. Can be used to get past or future times. Positive values represent future times, negative values represent past times. For example, to get yesterday's timestamp, use -86400000 as the offset (milliseconds in a day).",
  "input_schema": {
    "type": "object",
    "properties": {
      "offset_ms": {
        "type": "number",
        "description": "The millisecond offset relative to the current time. Negative values represent past time, positive values represent future time."
      }
    },
    "required": ["offset_ms"]
  }
}
```
### User Request Example
```json
{
  "role": "user",
  "content": [
    {
      "type": "text",
      "text": "Can you tell me what date was yesterday?"
    }
  ]
}
```
### Large Model Response
```json
{
  "role": "assistant",
  "content": [
    {
      "type": "text",
      "text": "To tell you yesterday's date, I need to get yesterday's timestamp."
    },
    {
      "type": "tool_use",
      "id": "toolu_01ABCDEFGHIJKLMNOPQRST",
      "name": "getTime",
      "input": {"offset_ms": -86400000}
    }
  ]
}
```
### MCP Module Execution Command
```json
{
  "role": "user",
  "content": [
    {
      "type": "tool_result",
      "tool_use_id": "toolu_01ABCDEFGHIJKLMNOPQRST",
      "result": "1684713600000"
    }
  ]
}
```
### Final Large Model Response with Context
```json
{
  "role": "assistant",
  "content": [
    {
      "type": "text",
      "text": "According to the timestamp I got 1684713600000, yesterday's date is May 22, 2023. This timestamp represents the number of milliseconds from January 1, 1970 to yesterday."
    }
  ]
}
```
## Gemini tool API and Context Organization Format

Gemini implements tool calls through the `GeminiProvider` class, which supports tools in Gemini Pro and later models.

### Format Conversion
Gemini requires the tool definition to be passed as follows:
```typescript
{
  tools: [
    {
      functionDeclarations: [
        {
          name: string,
          description: string,
          parameters: object // JSON Schema
        }
      ]
    }
  ]
}
```

### Context Organization
Gemini's message structure is relatively simple, but there are some special handling:
1. System instruction (systemInstruction): Passed as an independent parameter
2. Content array (contents): Contains user and model messages
3. Tool call: Represented by the `functionCall` object
4. Tool response: Represented by the `functionResponse` object

### Streaming Processing
Gemini's streaming response requires special handling of the following cases:
- `functionCall` object represents the start of the tool call
- Tool parameters are passed through the `functionCall.args` object
- The `functionCallResult` event represents a tool response

These events are also converted to standardized `LLMCoreStreamEvent` format, making them easier to handle.

## Example: Gemini's Tool Use Implementation

### Tool Definition
```json
{
  "tools": [
    {
      "functionDeclarations": [
        {
          "name": "getTime",
          "description": "Get the timestamp (in milliseconds) for a specific time offset.",
          "parameters": {
            "type": "object",
            "properties": {
              "offset_ms": {
                "type": "number",
                "description": "The millisecond offset relative to the current time. Negative values represent past time, positive values represent future time."
              }
            },
            "required": ["offset_ms"]
          }
        }
      ]
    }
  ]
}
```

### User Request Example
```json
{
  "role": "user",
  "parts": [
    {
      "text": "Can you tell me what date was yesterday?"
    }
  ]
}
```

### Large Model Response (Calling Tools)
```json
{
  "role": "model",
  "parts": [
    {
      "functionCall": {
        "name": "getTime",
        "args": {
          "offset_ms": -86400000
        }
      }
    }
  ]
}
```

### MCP Module Execution Command
```json
{
  "role": "user",
  "parts": [
    {
      "functionResponse": {
        "name": "getTime",
        "response": 1684713600000
      }
    }
  ]
}
```

### Final Large Model Response with Context
```json
{
  "role": "model",
  "parts": [
    {
      "text": "According to the timestamp I got 1684713600000, yesterday's date is May 22, 2023."
    }
  ]
}
```

## OpenAI tool API and Context Organization Format

OpenAI's tool use implementation is implemented in the `OpenAICompatibleProvider` class, which supports GPT-3.5-Turbo and GPT-4 series models.

### Format Conversion
OpenAI's function call format is most widely used:
```typescript
{
  tools: [
    {
      type: "function",
      function: {
        name: string,
        description: string,
        parameters: object // JSON Schema
      }
    }
  ]
}
```

### Context Organization
OpenAI's message format is relatively standardized:
1. Message array (messages): Contains role and content
2. Tool call: Recorded in the `tool_calls` array in the assistant message
3. Tool response: As a separate `tool` role message, containing a `tool_call_id` reference

### Streaming Processing
OpenAI's streaming events include:
- The `tool_calls` array represents a tool call
- The `delta.tool_calls` array represents incremental updates to tool calls
- Tool parameters are passed through `tool_calls[i].function.arguments`

## Example: OpenAI's Tool Use Implementation

### Tool Definition
```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "getTime",
        "description": "Get the timestamp (in milliseconds) for a specific time offset.",
        "parameters": {
          "type": "object",
          "properties": {
            "offset_ms": {
              "type": "number",
              "description": "The millisecond offset relative to the current time. Negative values represent past time, positive values represent future time."
            }
          },
          "required": ["offset_ms"]
        }
      }
    }
  ]
}
```

### User Request Example
```json
[
  {
    "role": "user",
    "content": "Can you tell me what date was yesterday?"
  }
]
```

### Large Model Response (Calling Tools)
```json
[
  {
    "role": "assistant",
    "content": null,
    "tool_calls": [
      {
        "id": "call_abc123",
        "type": "function",
        "function": {
          "name": "getTime",
          "arguments": "{ \"offset_ms\": -86400000 }"
        }
      }
    ]
  }
]
```

### MCP Module Execution Command
```json
[
  {
    "role": "tool",
    "tool_call_id": "call_abc123",
    "content": "1684713600000"
  }
]
```

### Final Large Model Response with Context
```json
[
  {
    "role": "assistant",
    "content": "According to the timestamp I got 1684713600000, yesterday's date is May 22, 2023."
  }
]
```

## How to Implement Tool Use for Models that Do Not Support Native Tool Use, and How to Correctly Parse Streaming Content for Function Information

For models that do not support native tool calls, the project implements an alternative solution based on prompt engineering:

### Prompt Wrapping
The `prepareFunctionCallPrompt` method in `OpenAICompatibleProvider` implements this functionality:
```typescript
private prepareFunctionCallPrompt(
  messages: ChatCompletionMessageParam[],
  mcpTools: MCPToolDefinition[]
): ChatCompletionMessageParam[]
```

This method adds tool definitions as instructions to the system messages, including:
1. Tool call format instructions (typically using XML-style tags like `<function_call>`)
2. JSON Schema of tool definitions
3. Usage examples and format requirements

### Streaming Content Parsing
Parsing function calls from streaming text is implemented using regular expressions and state machines:
```typescript
protected parseFunctionCalls(
  response: string,
  fallbackIdPrefix: string = 'tool-call'
): Array<{ id: string; type: string; function: { name: string; arguments: string } }>
```

The parsing process handles the following challenges:
1. Detecting the start and end markers of function calls
2. Handling nested JSON structures
3. Handling incomplete or incorrectly formatted function calls
4. Assigning unique IDs to function calls

Streaming parsing is implemented using a state machine (`TagState`) to track the status of tags:
```typescript
type TagState = 'none' | 'start' | 'inside' | 'end'
```

This makes it possible to identify and extract function call information even in complex streaming generations.

### The process for the same getTime example using the prompt engineering solution is as follows:

1. Add the function description to the system prompt:
```
You are a helpful AI assistant. When needed, you can use these tools to help answer questions:

function getTime(offset_ms: number): number
Description: Get the timestamp (in milliseconds) for a specific time offset
Parameters:
  - offset_ms: Time offset (in milliseconds)

When using tools, please use the following format:
<function_call>
{
  "name": "getTime",
  "arguments": {
    "offset_ms": -86400000
  }
}
</function_call>
```

2. Model generates response with function call tags:
```
I need to get yesterday's date. I will call the getTime function to get yesterday's timestamp.

<function_call>
{
  "name": "getTime",
  "arguments": {
    "offset_ms": -86400000
  }
}
</function_call>
```

3. Parse function calls using regular expressions:
```typescript
// Use state machine and regex to extract contents of <function_call> tags
const functionCallMatch = response.match(/<function_call>([\s\S]*?)<\/function_call>/);
if (functionCallMatch) {
  try {
    const parsedCall = JSON.parse(functionCallMatch[1]);
    // Call the function and get the result
  } catch (error) {
    // Handle parsing errors
  }
}
```

4. Add the function result to the context:
```
Function result: 1684713600000

According to the timestamp I got 1684713600000, yesterday's date is May 22, 2023.
```

This method enables models without native tool call support to simulate tool call functionality through carefully crafted prompts and text parsing techniques.


