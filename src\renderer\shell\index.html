<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Docomoe - Shell</title>
    <!-- Content Security Policy for enhanced security -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; font-src 'self' data:; img-src 'self' data: blob: https:; media-src 'self' data: blob:; connect-src 'self' ws: wss: https:; object-src 'none'; base-uri 'self';"
    />
    <!-- Preload critical font for better performance -->
    <link rel="preload" href="../src/assets/geist.ttf" as="font" type="font/ttf" crossorigin="anonymous">
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
