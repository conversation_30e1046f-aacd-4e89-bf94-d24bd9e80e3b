# Multi-Window and Multi-Tab Architecture Design and Refactoring Plan

## Goals

Refactor <PERSON>omoe's Electron app from the current single-window mode to support multiple equivalent windows, and each window supports multiple tabs (using `WebContentsView`). Users should be able to:

1.  Open multiple independent Docomoe windows, each with the same functionality.
2.  Open, close, and switch multiple tabs within each window.
3.  Drag and drop tabs from one window to another.

## Challenges with the Current Architecture (Original)

The original architecture is highly coupled to the concept of a "main window" (`MAIN_WIN`), which does not support multiple windows, let alone multiple tabs.

## New Architecture Design

The core idea is to separate window management and tab management, and implement them collaboratively.

1.  **Core Components:**

    - **`WindowPresenter`**: Manage the lifecycle and collection of `BrowserWindow` instances.
    - **`TabPresenter` (new)**: Globally manage all `WebContentsView` (tabs) instances, including their lifecycle, state, window ownership, and cross-window movement.
    - **`BrowserWindow`**: As a top-level container, contains a lightweight page for rendering the tab bar UI, and manages a set of `WebContentsView` controlled by `TabPresenter`.

2.  **`WindowPresenter` Refactor:**

    - **Responsibility:** Create and manage `BrowserWindow` instances (minimizing, maximizing, closing, etc.).
    - **Window Collection:** Use `Map<number, BrowserWindow>` to store window instances, `key` is `BrowserWindow` `id`.
    - **Window Creation:** `createWindow` is responsible for creating a `BrowserWindow` with a configured tab bar UI and `WebContentsView` container.
    - **Remove Single-Window Reference:** Discard `MAIN_WIN` constant and `mainWindow` getter.
      - **Window Operations:** `minimize`, `maximize`, `close`, etc. methods modified to accept `windowId` or operate on the currently focused window. When closing a window, notify `TabPresenter` to clean up associated tabs.
      - **Event Broadcast:** App-level events (such as theme changes) need to be broadcast to all windows; window-level events need to be handled correctly.

3.  **`TabPresenter` (new):**

    - **Responsibility:** Core tab manager.
    - **Data Structures:**
      - `tabs: Map<number, { view: WebContentsView, state: TabState, windowId: number }>`: Global tab instances and their state storage. Key is `tabId` (`webContents.id`), value is an object containing a `WebContentsView` instance, state object (`TabState`: { URL, title, favicon, isActive, etc. }) and the window ID (`windowId`).
      - `windowTabs: Map<number, number[]>`: Mapping from window IDs (`windowId`) to their contained tab IDs (`tabId[]`), maintains tab order within windows.
    - **Core Methods:**
      - `createTab(windowId, url, options)`: Creates a `WebContentsView`, generates a `TabInfo` object and stores it in the `tabs` Map, and adds the `tabId` to the `windowTabs` array of the corresponding `windowId`. Adds it to the view hierarchy of the `BrowserWindow` corresponding to `windowId` (e.g., `window.contentView.addChildView()`).
      - `destroyTab(tabId)`: Gets `TabInfo` from the `tabs` Map, finds `windowId` and `view`. Removes `view` from the window view hierarchy, destroys the `WebContentsView`, removes the entry from the `tabs` Map, and removes `tabId` from `windowTabs`.
      - `activateTab(tabId)`: Finds the corresponding `TabInfo` in `tabs`, updates its `state.isActive`, and raises the `view` to the front of its containing window. May also need to set the `isActive` of other tabs in the same window to `false`.
      - `detachTab(tabId)`: 从 `tabs` 中获取 `TabInfo`，从其当前窗口的视图层级移除 `view`。更新 `TabInfo` 中的 `windowId` (可能设为 `null` 或特殊值表示已分离)，并从旧窗口的 `windowTabs` 中移除 `tabId`。**注意：此时 `WebContentsView` 实例本身不销毁。**
      - `attachTab(tabId, targetWindowId, index?)`: 找到 `tabs` 中的 `TabInfo`。将其 `view` 添加到 `targetWindowId` 对应窗口的视图层级。更新 `TabInfo` 中的 `windowId` 为 `targetWindowId`。将 `tabId` 插入到 `targetWindowId` 对应的 `windowTabs` 数组的指定 `index` (或末尾)。
      - `moveTab(tabId, targetWindowId, index?)`: 协调 `detachTab` 和 `attachTab` 完成标签移动。
    - **Event/IPC Handling:** Listen for `WebContentsView` events to update the corresponding `TabInfo` state in `tabs`, handle tab operation requests from the rendering process.

4.  **IPC Communication:**

    - **Renderer -> Main:**
      - `requestNewTab(windowId, url)` -> `TabPresenter.createTab`
      - `requestCloseTab(windowId, tabId)` -> `TabPresenter.destroyTab`
      - `requestSwitchTab(windowId, tabId)` -> `TabPresenter.activateTab`
      - `notifyTabDragStart(windowId, tabId)`: Notify when tab drag starts.
      - `notifyTabDrop(sourceWindowId, tabId, targetWindowId, targetIndex)`: 通知拖拽结束及放置目标 -> `TabPresenter.moveTab`。
    - **Main -> Renderer:**
      - `updateWindowTabs(windowId, tabListData)`: 发送标签列表 `{ id, title, faviconUrl, isActive }[]` 给对应窗口渲染进程用于更新UI。
      - `setActiveTab(windowId, tabId)`: Notify the renderer process to highlight the active tab.

5.  **`TrayPresenter` & `ContextMenuHelper`:**

    - Needs access to window and tab lists (`WindowPresenter`, `TabPresenter`)
    - Define tray icon click behavior (e.g., display window list, activate most recently used tab)
    - Context menu should provide relevant actions based on current `WebContentsView`

6.  **Renderer Process (Renderer): Multi-Entry Architecture**

    To clearly separate the responsibilities and build artifacts of the window shell (Window Shell) and tab content (Tab Content), the renderer layer will adopt a multi-entry build strategy. This will require modifications to `electron.vite.config.ts` to support multiple `renderer` inputs.

    - **Entry 1: Window Shell (Window Shell)**

      - **Code Directory:** `src/renderer/shell/`
      - **Entry File:** `src/renderer/shell/index.html` (and its associated `main.ts`)
      - **Responsibility:**
        - Render the main interface frame of `BrowserWindow`, primarily the **tab bar UI** (`TabBar.vue`) at the top.
        - Run an independent, lightweight Vue application instance.
        - **Get `windowId`:** Must be able to identify its own `BrowserWindow` ID (through preload script injection or IPC).
        - **Handle Tab Bar Interactions:** Respond to user clicks on tabs (switching, closing), clicks on the new tab button, and **trigger corresponding IPC messages** (`requestSwitchTab`, `requestCloseTab`, `requestNewTab`) to the main process.
        - **实现标签拖拽:** 在标签栏内实现标签的拖拽排序和跨窗口拖拽的启动，通过 IPC **通知主进程** (`notifyTabDragStart`, `notifyTabDrop`)。
        - **接收状态更新:** 监听主进程发送的针对该窗口的标签列表更新 (`updateWindowTabs`) 和活动标签变更 (`setActiveTab`) 消息，并更新 UI。
      - **加载方式:** 主进程的 `WindowPresenter` 在创建 `BrowserWindow` 时，应加载此入口的 `index.html` (例如 `dist/renderer/shell/index.html`)。

    - **Entry 2: Tab Content (Content Page Inside Tabs)**

      - **Code Directory:** `src/renderer/content/`
      - **Entry File:** `src/renderer/content/index.html` (and its associated `main.ts`)
      - **职责:**
        - 渲染**指定标签页内的实际应用视图** (例如聊天界面、设置页面等)。
        - 运行另一个独立的 Vue 应用实例，包含应用本身的状态管理 (Pinia) 和路由 (Vue Router)。
        - **路由:** 使用 Vue Router 根据加载到 `WebContentsView` 中的 URL (由 `TabPresenter` 控制，例如 `dist/renderer/content/index.html#/chat/123` 或 `dist/renderer/content/index.html#/settings`) 来决定显示哪个具体的视图组件 (`ChatView.vue`, `SettingsView.vue` 等)。
        - **与主进程通信:** 处理视图内部的业务逻辑，并通过 IPC 与主进程交互（例如发送消息、保存设置）。
        - **更新标签状态:** 如果内容需要改变标签的外观（如标题、图标），需通过 IPC 通知主进程 (`TabPresenter`) 更新状态。
      - **加载方式:** 主进程的 `TabPresenter` 在创建 `WebContentsView` 实例时，应加载此入口的 `index.html` 并附加相应的 URL hash/path 以进行内容路由。

    - **Shared Code:**

      - 通用的类型定义、工具函数、常量等应放置在 `@shared` 目录中，供主进程和两个渲染进程入口共享。
      - 如果有跨 `shell` 和 `content` 的共享 Vue 组件或 UI 库，需要规划好共享方式 (例如通过 `@shared` 或独立的 UI 包)。

    - **构建配置 (`electron.vite.config.ts`)**
      - 需要将 `renderer` 配置修改为支持多入口的形式，明确指定 `shell` 和 `content` 的 `input` HTML 文件，并可能需要配置不同的 `resolve.alias` 指向各自的源代码目录 (`src/renderer/shell`, `src/renderer/content`)。

## Refactoring Plan

1.  **Design and Implement `TabPresenter`**: Core data structures, tab lifecycle management, state management, window association, and movement logic (`detachTab`, `attachTab`, `moveTab`).
2.  **Refactor `WindowPresenter`**: Adjust window creation logic to include tab container, and collaborate with `TabPresenter` to handle window events and close logic.
3.  **Implement `WebContentsView` Container**: Set up appropriate views in `BrowserWindow` to contain and manage `WebContentsView` instances (e.g., using `contentView` API).
4.  **Implement Main Process IPC**: Establish a communication channel between `Renderer <-> Main (TabPresenter/WindowPresenter)` and message handlers.
5.  **Implement Renderer Tab Bar UI**: Create components using a frontend framework (e.g., Vue) to display tabs, handle user interactions, and trigger IPC calls.
6.  **Implement Renderer Tab Drag Logic**: Handle drag events, calculate drop targets, and notify the main process via IPC (`notifyTabDragStart`, `notifyTabDrop`).
7.  **Implement Main Process Tab Movement Handling**: `TabPresenter` responds to `notifyTabDrop`, calling `moveTab` to actually perform the `WebContentsView` operation.
8.  **Ensure State Synchronization**: Ensure that tab state (title, URL, favicon, active status) is correctly synchronized between Main (`TabPresenter`) and Renderer (UI).
9.  **Adjust `TrayPresenter` & `ContextMenuHelper`**: Make them suitable for multi-window and multi-tab environments.
10. **Review Lifetime Management**: Ensure the robustness of tab closure, window closure, and application exit logic.
11. **Update Documentation**: Write this detailed design into `docs/multi-window-architecture.md`. (This step)
12. **Test**: Fully test multi-window creation/closure, multi-tab creation/closure/switching, cross-window drag-and-drop, state synchronization, and IPC communication.
