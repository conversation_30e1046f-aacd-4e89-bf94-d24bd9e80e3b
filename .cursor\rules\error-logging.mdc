---
description:
globs:
alwaysApply: false
---
# Error Handling and Logging Guide

## Error Handling Specification

1. Error Types
- User Error: Errors caused by user input or operation
- System Error: Errors during program execution
- Network Error: API calls or network request errors
- Business Error: Errors related to business logic

2. Error Handling Principles
- Always use try-catch to handle possible errors
- Provide meaningful error messages
- Record detailed error logs
- Gracefully degrade handling

3. Error Handling Example:
```typescript
try {
  await someOperation()
} catch (error) {
  if (error instanceof UserError) {
    // Handle user errors by showing a user-friendly message
    showUserFriendlyMessage(error.message)
  } else if (error instanceof NetworkError) {
    // Handle network errors by handling them appropriately
    handleNetworkError(error)
  } else {
    // Handle unknown errors by logging them and showing a generic error message
    logError(error)
    showGenericErrorMessage()
  }
}
```

## Logging Specification

1. Logging Levels
- ERROR: Error messages
- WARN: Warning messages
- INFO: General messages
- DEBUG: Debug messages

2. Logging Contents
- Timestamps
- Logging Levels
- Error Codes
- Error Descriptions
- Stack Traces (if applicable)
- Related Context Information

3. Logging Example:
```typescript
import { logger } from '@/utils/logger'

// Error logs: log error messages with details and context information
logger.error('Failed to save data', {
  error: error,
  context: { userId, operation }
})

// Info logs: log general messages with details and context information
logger.info('User action completed', {
  action: 'save',
  userId,
  timestamp: new Date()
})
```

## Best Practices

1. Error Handling
- Don't swallow errors
- Provide user-friendly error messages
- Implement error retry mechanism
- Use error boundaries to catch rendering errors

2. Logging
- Avoid logging sensitive information
- Use structured logging
- Implement log rotation
- Set appropriate logging levels

3. Monitoring and Alerting
- Set up error monitoring
- Configure critical error alerts
- Periodically check error logs
- Analyze error patterns
