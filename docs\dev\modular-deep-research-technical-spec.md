# Modular Deep Research - Technical Specification

## Architecture Overview

This document provides detailed technical specifications for implementing the modular deep research architecture, building on the existing `customPromptsServer` foundation.

## Server Specifications

### 1. Advanced Crawler Server

**File**: `src/main/presenter/mcpPresenter/inMemoryServers/advancedCrawlerServer.ts`

#### Schema Definitions

```typescript
const CrawlWebsiteArgsSchema = z.object({
  url: z.string().url().describe('Starting URL to crawl'),
  maxDepth: z.number().min(1).max(3).default(1).describe('Maximum crawl depth'),
  maxBreadth: z.number().min(1).max(15).default(10).describe('Max links per level'),
  limit: z.number().min(1).max(50).default(20).describe('Total pages to crawl'),
  selectDomains: z.array(z.string()).optional().default([]).describe('Regex patterns for allowed domains'),
  excludeDomains: z.array(z.string()).optional().default([]).describe('Regex patterns for excluded domains'),
  selectPaths: z.array(z.string()).optional().default([]).describe('Regex patterns for allowed paths'),
  excludePaths: z.array(z.string()).optional().default([]).describe('Regex patterns for excluded paths'),
  allowExternal: z.boolean().default(false).describe('Allow crawling external domains'),
  includeImages: z.boolean().default(false).describe('Extract image URLs'),
  extractDepth: z.enum(['basic', 'advanced']).default('basic').describe('Content extraction depth'),
  timeout: z.number().default(30).describe('Request timeout in seconds')
})

const BatchCrawlArgsSchema = z.object({
  urls: z.array(z.string().url()).describe('Array of URLs to crawl'),
  crawlParams: CrawlWebsiteArgsSchema.omit({ url: true }).describe('Crawl parameters'),
  concurrency: z.number().min(1).max(5).default(3).describe('Concurrent crawl limit')
})
```

#### Core Classes

```typescript
interface CrawlResult {
  url: string
  title: string
  content: string
  images: string[]
  links: string[]
  metadata: {
    depth: number
    timestamp: string
    contentLength: number
    language?: string
    lastModified?: string
  }
  errors: string[]
}

class AdvancedWebCrawler {
  private visitedUrls: Set<string>
  private rateLimiter: RateLimiter
  private memoryManager: MemoryManager
  
  async crawl(params: CrawlParams): Promise<CrawlResult[]>
  private async crawlSinglePage(url: string, depth: number): Promise<CrawlResult | null>
  private extractContent(html: string, strategy: 'basic' | 'advanced'): string
  private filterUrls(urls: string[], params: CrawlParams): string[]
}
```

#### Tools Implementation

```typescript
export class AdvancedCrawlerServer {
  private server: Server
  
  private setupRequestHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'crawl_website',
          description: 'Crawl a website with advanced filtering and extraction options',
          inputSchema: zodToJsonSchema(CrawlWebsiteArgsSchema)
        },
        {
          name: 'batch_crawl_urls',
          description: 'Crawl multiple URLs concurrently with shared parameters',
          inputSchema: zodToJsonSchema(BatchCrawlArgsSchema)
        }
      ]
    }))
  }
}
```

### 2. Content Analysis Server

**File**: `src/main/presenter/mcpPresenter/inMemoryServers/contentAnalysisServer.ts`

#### Schema Definitions

```typescript
const ExtractMainContentArgsSchema = z.object({
  html: z.string().describe('HTML content to analyze'),
  strategy: z.enum(['semantic', 'heuristic', 'hybrid']).default('hybrid').describe('Extraction strategy'),
  maxLength: z.number().default(8000).describe('Maximum content length'),
  preserveFormatting: z.boolean().default(false).describe('Preserve basic formatting')
})

const AnalyzeContentStructureArgsSchema = z.object({
  url: z.string().url().describe('URL to analyze'),
  extractMetadata: z.boolean().default(true).describe('Extract page metadata'),
  extractCitations: z.boolean().default(false).describe('Extract citations and references'),
  extractImages: z.boolean().default(false).describe('Extract and analyze images'),
  extractLinks: z.boolean().default(true).describe('Extract and categorize links'),
  contentQualityScore: z.boolean().default(false).describe('Calculate content quality score')
})
```

#### Content Extraction Strategies

```typescript
interface ContentExtractionStrategy {
  extract(html: string, options: ExtractionOptions): ExtractedContent
}

class SemanticExtractor implements ContentExtractionStrategy {
  // Uses semantic HTML tags (article, main, section)
}

class HeuristicExtractor implements ContentExtractionStrategy {
  // Uses content density and common patterns
}

class HybridExtractor implements ContentExtractionStrategy {
  // Combines semantic and heuristic approaches
}
```

### 3. Research Template Server

**File**: `src/main/presenter/mcpPresenter/inMemoryServers/researchTemplateServer.ts`

#### Integration with customPromptsServer

```typescript
interface ResearchTemplate {
  name: string
  description: string
  category: 'academic' | 'market' | 'technical' | 'news' | 'general'
  steps: ResearchStep[]
  outputTemplate: string
  parameters: TemplateParameter[]
}

interface ResearchStep {
  tool: string
  server: string
  params: Record<string, any>
  condition?: string // Optional conditional execution
  parallel?: boolean // Can run in parallel with other steps
}

class ResearchTemplateEngine {
  constructor(private customPromptsServer: CustomPromptsServer) {}
  
  async executeTemplate(templateName: string, query: string, params: Record<string, any>): Promise<ResearchResult>
  async generateResearchPrompt(type: string, context: ResearchContext): Promise<string>
  private async executeStep(step: ResearchStep, context: ExecutionContext): Promise<StepResult>
}
```

## Template System Integration

### Research Template Schema

```typescript
const ResearchTemplateSchema = z.object({
  name: z.string(),
  description: z.string(),
  category: z.enum(['academic', 'market', 'technical', 'news', 'general']),
  steps: z.array(z.object({
    tool: z.string(),
    server: z.string(),
    params: z.record(z.any()),
    condition: z.string().optional(),
    parallel: z.boolean().default(false)
  })),
  outputTemplate: z.string(),
  parameters: z.array(z.object({
    name: z.string(),
    type: z.enum(['string', 'number', 'boolean', 'array']),
    description: z.string(),
    required: z.boolean().default(false),
    default: z.any().optional()
  }))
})
```

### Default Research Templates

```typescript
const DEFAULT_RESEARCH_TEMPLATES: ResearchTemplate[] = [
  {
    name: 'comprehensive_web_research',
    description: 'Multi-source web research with deep content analysis',
    category: 'general',
    steps: [
      {
        tool: 'bocha_web_search',
        server: 'bochaSearch',
        params: { query: '{{query}}', count: 10 }
      },
      {
        tool: 'DuckDuckGoWebSearch',
        server: 'webScraper',
        params: { query: '{{query}}', maxResults: 8 },
        parallel: true
      },
      {
        tool: 'batch_crawl_urls',
        server: 'advancedCrawler',
        params: { 
          urls: '{{search_results.urls}}',
          crawlParams: { maxDepth: 2, limit: 15 }
        }
      },
      {
        tool: 'batch_content_analysis',
        server: 'contentAnalysis',
        params: { 
          urls: '{{crawl_results.urls}}',
          extractMetadata: true 
        }
      }
    ],
    outputTemplate: 'comprehensive_research_report',
    parameters: [
      {
        name: 'query',
        type: 'string',
        description: 'Research query or topic',
        required: true
      },
      {
        name: 'depth',
        type: 'number',
        description: 'Research depth (1-3)',
        default: 2
      }
    ]
  }
]
```

## Implementation Details

### Memory Management

```typescript
class MemoryManager {
  private readonly MAX_MEMORY_USAGE = 0.8 // 80% of available memory
  
  async checkMemoryUsage(): Promise<MemoryStats>
  async optimizeMemoryUsage(): Promise<void>
  async shouldUseFileStorage(dataSize: number): Promise<boolean>
}
```

### Rate Limiting

```typescript
class AdvancedRateLimiter {
  private requestQueues: Map<string, RequestQueue>
  
  async acquire(domain: string): Promise<void>
  private async waitForSlot(domain: string): Promise<void>
  private getDomainLimits(domain: string): RateLimits
}
```

### Error Handling

```typescript
interface CrawlError {
  url: string
  error: string
  timestamp: string
  retryable: boolean
}

class ErrorRecoveryManager {
  async handleCrawlError(error: CrawlError): Promise<RetryStrategy>
  async shouldRetry(error: CrawlError, attempt: number): Promise<boolean>
}
```

## Server Registration

### Builder Updates

```typescript
// src/main/presenter/mcpPresenter/inMemoryServers/builder.ts
import { AdvancedCrawlerServer } from './advancedCrawlerServer'
import { ContentAnalysisServer } from './contentAnalysisServer'
import { ResearchTemplateServer } from './researchTemplateServer'

export function getInMemoryServer(serverName: string, args: string[], env?: Record<string, unknown>) {
  switch (serverName) {
    // ... existing cases
    case 'advancedCrawler':
      return new AdvancedCrawlerServer(env)
    case 'contentAnalysis':
      return new ContentAnalysisServer()
    case 'researchTemplate':
      return new ResearchTemplateServer()
    // ... rest of cases
  }
}
```

### Configuration Integration

```typescript
// Add to SYSTEM_INMEM_MCP_SERVERS
export const RESEARCH_MCP_SERVERS: Record<string, MCPServerConfig> = {
  'docomoe-inmemory/advanced-crawler-server': {
    command: 'docomoe-inmemory/advanced-crawler-server',
    args: [],
    env: {},
    descriptions: 'Advanced web crawler with filtering and extraction',
    icons: '🕷️',
    autoApprove: ['crawl_website', 'batch_crawl_urls'],
    type: 'inmemory' as MCPServerType,
    disable: false
  },
  'docomoe-inmemory/content-analysis-server': {
    command: 'docomoe-inmemory/content-analysis-server',
    args: [],
    env: {},
    descriptions: 'Intelligent content processing and analysis',
    icons: '🔍',
    autoApprove: ['extract_main_content', 'analyze_content_structure'],
    type: 'inmemory' as MCPServerType,
    disable: false
  },
  'docomoe-inmemory/research-template-server': {
    command: 'docomoe-inmemory/research-template-server',
    args: [],
    env: {},
    descriptions: 'Template-driven research workflows',
    icons: '📋',
    autoApprove: ['apply_research_template', 'generate_research_prompt'],
    type: 'inmemory' as MCPServerType,
    disable: false
  }
}
```

## Testing Strategy

### Unit Tests
- Individual tool functionality
- Content extraction strategies
- Template execution engine
- Error handling scenarios

### Integration Tests
- Multi-server workflows
- Template parameter injection
- Memory management under load
- Rate limiting effectiveness

### Performance Tests
- Concurrent crawling limits
- Memory usage patterns
- Response time benchmarks
- Scalability testing

This technical specification provides the foundation for implementing the modular deep research architecture while maintaining compatibility with existing systems and following established patterns in the Docomoe app.
