import { <PERSON><PERSON><PERSON><PERSON><PERSON>ow, Menu, MenuItemConstructorOptions, WebContents, dialog, net } from 'electron'
import path from 'path'
import sharp from 'sharp'

interface ContextMenuOptions {
  webContents: WebContents
  shouldShowMenu?: (event: Electron.Event, params: Electron.ContextMenuParams) => boolean
  labels?: Record<string, string>
  prepend?: (
    defaultActions: MenuItemConstructorOptions[],
    params: Electron.ContextMenuParams,
    webContents: WebContents
  ) => MenuItemConstructorOptions[]
  append?: (
    defaultActions: MenuItemConstructorOptions[],
    params: Electron.ContextMenuParams,
    webContents: WebContents
  ) => MenuItemConstructorOptions[]
  menu?: (
    defaultActions: MenuItemConstructorOptions[],
    params: Electron.ContextMenuParams,
    webContents: WebContents
  ) => MenuItemConstructorOptions[] | Menu
}

/**
 * Simplified context menu implementation
 * Only includes basic functionality, ensuring proper lifecycle handling and listener cleanup */
export default function contextMenu(options: ContextMenuOptions): () => void {
  const disposables: (() => void)[] = []
  let isDisposed = false

  console.log('contextMenu: initializing context menu', options.webContents.id)

  // Ensure webContents parameter exists
  if (!options.webContents) {
    console.error('contextMenu: WebContents parameter is missing')
    throw new Error('WebContents is required')
  }

  // Handle context menu events
  const handleContextMenu = (event: Electron.Event, params: Electron.ContextMenuParams) => {
    // console.log('contextMenu: trigger', params.x, params.y, params.mediaType)

    if (isDisposed) {
      return
    }

    // Check if the menu should be displayed
    if (
      typeof options.shouldShowMenu === 'function' &&
      options.shouldShowMenu(event, params) === false
    ) {
      return
    }

    // Prepare default menu items - provide some basic menu items
    let menuItems: MenuItemConstructorOptions[] = []

    // Handle image right-click menu
    if (params.mediaType === 'image') {
      // Image copy option
      menuItems.push({
        id: 'copyImage',
        label: options.labels?.copyImage || 'Copy Image',
        click: () => {
          options.webContents.copyImageAt(params.x, params.y)
          console.log('contextMenu: copying image', params.srcURL)
        }
      })

      // Image save as option
      menuItems.push({
        id: 'saveImage',
        label: options.labels?.saveImage || 'Save Image As...',
        click: async () => {
          try {
            // Get file name and URL
            const url = params.srcURL || ''
            let fileName = 'image.png'
            let imageBuffer: Buffer | null = null

            // Check if it's base64 format
            const isBase64 = url.startsWith('data:image/')
            if (!isBase64) {
              // Use file name from path for normal URL
              fileName = path.basename(url || 'image.png')
            } else {
              // Use default file name
              // Try to recognize extension from MIME type
              const mimeMatch = url.match(/^data:image\/([a-zA-Z0-9]+);base64,/)
              if (mimeMatch && mimeMatch[1]) {
                const ext = mimeMatch[1].toLowerCase()
                fileName = `image.${ext === 'jpeg' ? 'jpg' : ext}`
              }
            }

            // Open save dialog
            const { canceled, filePath } = await dialog.showSaveDialog({
              defaultPath: fileName,
              filters: [
                { name: 'Image', extensions: ['png', 'jpg', 'jpeg', 'gif', 'webp'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            })

            if (canceled || !filePath) {
              return
            }

            console.log('contextMenu: start saving pic', filePath)

            // Get image data
            if (isBase64) {
              // Handle base64 data
              const base64Data = url.split(',')[1]
              if (!base64Data) {
                throw new Error('Invalid base64 image data')
              }
              imageBuffer = Buffer.from(base64Data, 'base64')
            } else {
              // Handle normal URL
              const response = await net.fetch(url)
              if (!response.ok) {
                throw new Error(`Failed to download image: ${response.status}`)
              }
              imageBuffer = Buffer.from(await response.arrayBuffer())
            }

            if (!imageBuffer) {
              throw new Error('Unable to get image data')
            }

            // Use sharp to process image and save
            const fileExt = path.extname(filePath).toLowerCase().substring(1)

            // Handle image format based on target file extension
            const sharpInstance = sharp(imageBuffer)

            if (fileExt === 'jpg' || fileExt === 'jpeg') {
              await sharpInstance.jpeg({ quality: 90 }).toFile(filePath)
            } else if (fileExt === 'png') {
              await sharpInstance.png().toFile(filePath)
            } else if (fileExt === 'webp') {
              await sharpInstance.webp().toFile(filePath)
            } else if (fileExt === 'gif') {
              await sharpInstance.gif().toFile(filePath)
            } else {
              // Save as original format
              await sharpInstance.toFile(filePath)
            }

            console.log('contextMenu: pic saved ', filePath)
          } catch (error) {
            console.error('contextMenu: pic save failed', error)
          }
        }
      })

      // Add separator
      menuItems.push({ type: 'separator' })
    }

    // Add basic menu items based on labels setting
    if (params.isEditable) {
      const editFlags = params.editFlags
      // Add basic edit menu
      if (editFlags.canCut && params.selectionText) {
        menuItems.push({
          id: 'cut',
          label: options.labels?.cut || 'Cut',
          role: 'cut',
          enabled: true
        })
      }

      if (editFlags.canCopy && params.selectionText) {
        menuItems.push({
          id: 'copy',
          label: options.labels?.copy || 'Copy',
          role: 'copy',
          enabled: true
        })
      }

      if (editFlags.canPaste) {
        menuItems.push({
          id: 'paste',
          label: options.labels?.paste || 'Paste',
          role: 'paste',
          enabled: true
        })
      }
    } else if (params.selectionText) {
      // Non-input box text selection
      menuItems.push({
        id: 'copy',
        label: options.labels?.copy || 'Copy',
        role: 'copy',
        enabled: true
      })

      // Add separator
      menuItems.push({ type: 'separator' })

      // Add translation option
      menuItems.push({
        id: 'translate',
        label: options.labels?.translate || 'Translate',
        click: () => {
          options.webContents.send(
            'context-menu-translate',
            params.selectionText,
            params.x,
            params.y
          )
        }
      })

      // Add AI ask option
      menuItems.push({
        id: 'askAI',
        label: options.labels?.askAI || 'Ask AI',
        click: () => {
          options.webContents.send('context-menu-ask-ai', params.selectionText)
        }
      })
    }

    // Allow users to add items before the menu
    if (typeof options.prepend === 'function') {
      const prependItems = options.prepend(menuItems, params, options.webContents)
      menuItems = prependItems.concat(menuItems)
    }

    // Allow users to add items after the menu
    if (typeof options.append === 'function') {
      const appendItems = options.append(menuItems, params, options.webContents)
      menuItems = menuItems.concat(appendItems)
    }

    // Allow users to fully customize the menu
    if (typeof options.menu === 'function') {
      const customMenu = options.menu(menuItems, params, options.webContents)

      if (Array.isArray(customMenu)) {
        menuItems = customMenu
      } else {
        // If it's a Menu instance, display it directly
        const window = BrowserWindow.fromWebContents(options.webContents)
        if (window) {
          customMenu.popup({ window })
        }
        return
      }
    }

    // Clean up separators (avoid continuous separators or separators at the beginning/end)
    menuItems = removeUnusedMenuItems(menuItems)

    // Create and display menu
    if (menuItems.length > 0) {
      try {
        const menu = Menu.buildFromTemplate(menuItems)
        console.log('contextMenu: displaying menu')
        const window = BrowserWindow.fromWebContents(options.webContents)
        if (window) {
          menu.popup({
            window,
            x: params.x,
            y: params.y
          })
        }
      } catch (error) {
        console.error('contextMenu: create error', error)
      }
    } else {
      console.warn('contextMenu: The menu will not be displayed')
    }
  }

  // Clean up continuous separators
  const removeUnusedMenuItems = (
    menuTemplate: MenuItemConstructorOptions[]
  ): MenuItemConstructorOptions[] => {
    let notDeletedPreviousElement: MenuItemConstructorOptions | undefined

    return (
      menuTemplate
        // Filter out invisible or undefined menu items
        .filter((menuItem): menuItem is MenuItemConstructorOptions => {
          return (
            menuItem !== undefined && typeof menuItem === 'object' && menuItem.visible !== false
          )
        })
        // Filter out unnecessary separators
        .filter((menuItem, index, array) => {
          const toDelete =
            menuItem.type === 'separator' &&
            (!notDeletedPreviousElement ||
              index === array.length - 1 ||
              array[index + 1].type === 'separator')

          notDeletedPreviousElement = toDelete ? notDeletedPreviousElement : menuItem
          return !toDelete
        })
    )
  }

  // Initialize context menu
  const initialize = (webContents: WebContents) => {
    if (isDisposed) {
      return
    }

    try {
      // Add context menu event listener
      webContents.on('context-menu', handleContextMenu)

      // Clean up when WebContents is destroyed
      const cleanup = () => {
        webContents.removeListener('context-menu', handleContextMenu)
      }

      webContents.once('destroyed', cleanup)

      // Add to cleanup list
      disposables.push(() => {
        webContents.removeListener('context-menu', handleContextMenu)
        webContents.removeListener('destroyed', cleanup)
      })
    } catch (error) {
      console.error('contextMenu: init error', error)
    }
  }

  // Register WebContents
  initialize(options.webContents)

  // Return cleanup function
  return () => {
    if (isDisposed) {
      console.log('contextMenu: already disposed, skipping cleanup')
      return
    }

    console.log('contextMenu: starting cleanup')
    // Clean up all listeners
    for (const dispose of disposables) {
      dispose()
    }

    disposables.length = 0
    isDisposed = true
    console.log('contextMenu: cleanup completed')
  }
}
