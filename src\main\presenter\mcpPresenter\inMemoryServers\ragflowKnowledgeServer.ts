import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import axios from 'axios'

// Schema definitions
const RagflowKnowledgeSearchArgsSchema = z.object({
  query: z.string().describe('Search query content (required)'),
  topK: z.number().optional().default(5).describe('Return result count (default 5)'),
  scoreThreshold: z
    .number()
    .optional()
    .default(0.2)
    .describe('Similarity threshold (0-1, default 0.2)'),
  keyword: z
    .boolean()
    .optional()
    .default(false)
    .describe('Enable keyword matching (default false)'),
  highlight: z
    .boolean()
    .optional()
    .default(false)
    .describe('Highlight matching text (default false)')
})

// Define RAGFlow API return data structure
interface RagflowSearchResponse {
  code: number
  data: {
    chunks: Array<{
      content: string
      content_ltks: string
      document_id: string
      document_keyword: string
      highlight?: string
      id: string
      image_id: string
      important_keywords: string[]
      kb_id: string
      positions: string[]
      similarity: number
      term_similarity: number
      vector_similarity: number
    }>
    doc_aggs: Array<{
      count: number
      doc_id: string
      doc_name: string
    }>
    total: number
  }
}

// Import MCPTextContent interface
import { MCPTextContent } from '@shared/presenter'

export class RagflowKnowledgeServer {
  private server: Server
  private configs: Array<{
    apiKey: string
    endpoint: string
    datasetIds: string[]
    description: string
    enabled: boolean
  }> = []

  constructor(env?: {
    configs: {
      apiKey: string
      endpoint: string
      datasetIds: string[]
      description: string
      enabled: boolean
    }[]
  }) {
    if (!env) {
      throw new Error('Provide RAGFlow knowledge base configuration')
    }

    const envs = env.configs

    if (!Array.isArray(envs) || envs.length === 0) {
      throw new Error('Provide at least one RAGFlow knowledge base configuration')
    }

    // Handle each configuration
    for (const env of envs) {
      if (!env.apiKey) {
        throw new Error('Provide RAGFlow API Key')
      }
      if (!env.datasetIds || !Array.isArray(env.datasetIds) || env.datasetIds.length === 0) {
        throw new Error('Provide at least one RAGFlow Dataset ID')
      }
      if (!env.description) {
        throw new Error(
          'Provide a description of this knowledge base to facilitate whether to search this knowledge base'
        )
      }

      this.configs.push({
        apiKey: env.apiKey,
        datasetIds: env.datasetIds,
        endpoint: env.endpoint || 'http://localhost:8000',
        description: env.description,
        enabled: env.enabled
      })
    }

    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/ragflow-knowledge-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set request handler
    this.setupRequestHandlers()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Set request handler
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.configs
        .filter((conf) => conf.enabled)
        .map((config, index) => {
          const suffix = this.configs.length > 1 ? `_${index + 1}` : ''
          return {
            name: `ragflow_knowledge_search${suffix}`,
            description: config.description,
            inputSchema: zodToJsonSchema(RagflowKnowledgeSearchArgsSchema)
          }
        })
      return { tools }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: parameters } = request.params

      // Check if tool is RAGFlow knowledge search
      if (name.startsWith('ragflow_knowledge_search')) {
        try {
          // Filter out enabled configurations
          const enabledConfigs = this.configs.filter((config) => config.enabled)

          // Extract index
          let configIndex = 0
          const match = name.match(/_([0-9]+)$/)
          if (match) {
            configIndex = parseInt(match[1], 10) - 1
          }

          // Ensure index is valid
          if (configIndex < 0 || configIndex >= enabledConfigs.length) {
            throw new Error(`Invalid knowledge base index: ${configIndex}`)
          }

          // Get actual index of configuration
          const actualConfigIndex = this.configs.findIndex(
            (config) => config === enabledConfigs[configIndex]
          )

          return await this.performRagflowKnowledgeSearch(parameters, actualConfigIndex)
        } catch (error) {
          console.error('RAGFlow knowledge search failed:', error)
          return {
            content: [
              {
                type: 'text',
                text: `Search failed: ${error instanceof Error ? error.message : String(error)}`
              }
            ]
          }
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: `Unknown tool: ${name}`
          }
        ]
      }
    })
  }

  // Perform RAGFlow knowledge search
  private async performRagflowKnowledgeSearch(
    parameters: Record<string, unknown> | undefined,
    configIndex: number = 0
  ): Promise<{ content: MCPTextContent[] }> {
    const {
      query,
      topK = 5,
      scoreThreshold = 0.2,
      keyword = false,
      highlight = false
    } = parameters as {
      query: string
      topK?: number
      scoreThreshold?: number
      keyword?: boolean
      highlight?: boolean
    }

    if (!query) {
      throw new Error('Query content cannot be empty')
    }

    // Get current configuration
    const config = this.configs[configIndex]

    try {
      const url = `${config.endpoint.replace(/\/$/, '')}/api/v1/retrieval`
      console.log('performRagflowKnowledgeSearch request', url, {
        question: query,
        dataset_ids: config.datasetIds,
        top_k: topK,
        similarity_threshold: scoreThreshold,
        keyword,
        highlight
      })

      const response = await axios.post<RagflowSearchResponse>(
        url,
        {
          question: query,
          dataset_ids: config.datasetIds,
          page_size: topK,
          similarity_threshold: scoreThreshold,
          keyword,
          highlight
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${config.apiKey}`
          }
        }
      )

      if (response.data.code !== 0) {
        throw new Error(`RAGFlow API error: ${response.data.code}`)
      }

      // Handle response data
      const results = response.data.data.chunks.map((chunk) => {
        const docName = chunk.document_keyword || 'Unknown document'
        const docId = chunk.document_id
        const content = highlight && chunk.highlight ? chunk.highlight : chunk.content
        const score = chunk.similarity

        return {
          title: docName,
          documentId: docId,
          content: content,
          score: score,
          keywords: chunk.important_keywords || []
        }
      })

      // Build response
      let resultText = `### Query: ${query}\n\n`

      if (results.length === 0) {
        resultText += 'No related results found.'
      } else {
        resultText += `Found ${results.length} related results:\n\n`

        results.forEach((result, index) => {
          resultText += `#### ${index + 1}. ${result.title} (Similarity: ${(result.score * 100).toFixed(2)}%)\n`
          resultText += `${result.content}\n\n`

          if (result.keywords && result.keywords.length > 0) {
            resultText += `Keywords: ${result.keywords.join(', ')}\n\n`
          }
        })
      }

      return {
        content: [
          {
            type: 'text',
            text: resultText
          }
        ]
      }
    } catch (error) {
      console.error('RAGFlow API request failed:', error)
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `RAGFlow API error (${error.response.status}): ${JSON.stringify(error.response.data)}`
        )
      }
      throw error
    }
  }
}
