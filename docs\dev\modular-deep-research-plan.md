# Modular Deep Research Architecture Plan

## Overview

This document outlines the plan to refactor the monolithic `deepResearchServer.ts` into a modular, template-driven architecture that leverages the existing `customPromptsServer` foundation and follows the established MCP server patterns in the Docomoe app.

## Current State Analysis

### Existing Assets
- ✅ **customPromptsServer.ts** - Template system foundation
- ✅ **bochaSearchServer.ts** - Web search via Bocha API  
- ✅ **webScraperServer.ts** - DuckDuckGo search + URL content extraction
- ✅ **puppeteerServer.ts** - Advanced web interactions

### Current Issues with deepResearchServer.ts
- **Monolithic**: Combines search, crawling, content extraction, and documentation generation
- **Rigid**: Fixed workflow with limited flexibility
- **Complex**: 800+ lines with tightly coupled components
- **Hard to maintain**: Changes affect multiple functionalities

## Proposed Modular Architecture

### Core Principle
**LLMs should pick the best tool for each task**, rather than being locked into rigid workflows. Research becomes template-driven and composable.

## Phase 1: Advanced Web Crawler Server

### File: `src/main/presenter/mcpPresenter/inMemoryServers/advancedCrawlerServer.ts`

**Purpose**: Sophisticated web crawling with advanced filtering and configuration options.

**Tools**:
```typescript
interface CrawlerTools {
  crawl_website: {
    url: string
    maxDepth: number (1-3)
    maxBreadth: number (1-15) 
    limit: number (1-50)
    selectDomains?: string[]
    excludeDomains?: string[]
    selectPaths?: string[]
    excludePaths?: string[]
    allowExternal: boolean
    includeImages: boolean
    extractDepth: 'basic' | 'advanced'
    timeout: number
  }
  
  batch_crawl_urls: {
    urls: string[]
    crawlParams: CrawlParams
    concurrency: number (1-5)
  }
}
```

**Key Features**:
- Domain and path filtering with regex support
- Concurrent crawling with rate limiting
- Memory-efficient processing
- Content extraction strategies
- Image and link extraction
- Error handling and recovery

## Phase 2: Content Analysis Server

### File: `src/main/presenter/mcpPresenter/inMemoryServers/contentAnalysisServer.ts`

**Purpose**: Intelligent content processing and analysis.

**Tools**:
```typescript
interface ContentAnalysisTools {
  extract_main_content: {
    html: string
    strategy: 'semantic' | 'heuristic' | 'hybrid'
    maxLength: number
  }
  
  analyze_content_structure: {
    url: string
    extractMetadata: boolean
    extractCitations: boolean
    extractImages: boolean
    extractLinks: boolean
  }
  
  batch_content_analysis: {
    urls: string[]
    analysisParams: AnalysisParams
  }
}
```

**Key Features**:
- Multiple content extraction strategies
- Metadata and citation extraction
- Content quality scoring
- Batch processing capabilities
- Memory management

## Phase 3: Research Template Server

### File: `src/main/presenter/mcpPresenter/inMemoryServers/researchTemplateServer.ts`

**Purpose**: Template-driven research workflows that integrate with `customPromptsServer`.

**Tools**:
```typescript
interface ResearchTemplateTools {
  apply_research_template: {
    templateName: string
    query: string
    parameters: Record<string, any>
  }
  
  generate_research_prompt: {
    researchType: 'academic' | 'market' | 'technical' | 'news'
    query: string
    context: string
    language: string
  }
  
  combine_research_data: {
    sources: ResearchSource[]
    outputFormat: 'markdown' | 'json' | 'structured'
    includeMetadata: boolean
  }
}
```

**Integration with customPromptsServer**:
- Leverage existing template system
- Research-specific prompt templates
- Dynamic parameter injection
- Multi-language support

## Research Templates

### Template Examples

**Academic Research Template**:
```json
{
  "name": "academic_research",
  "description": "Comprehensive academic research workflow",
  "steps": [
    {
      "tool": "bocha_web_search",
      "params": {"query": "{{query}}", "count": 10}
    },
    {
      "tool": "crawl_website", 
      "params": {"maxDepth": 2, "selectDomains": ["edu", "org"]}
    },
    {
      "tool": "analyze_content_structure",
      "params": {"extractCitations": true, "extractMetadata": true}
    }
  ],
  "outputTemplate": "academic_report"
}
```

**Market Research Template**:
```json
{
  "name": "market_research",
  "description": "Market analysis and competitive intelligence",
  "steps": [
    {
      "tool": "bocha_web_search",
      "params": {"query": "{{query}} market analysis", "count": 15}
    },
    {
      "tool": "DuckDuckGoWebSearch",
      "params": {"query": "{{query}} competitors", "maxResults": 10}
    },
    {
      "tool": "batch_content_analysis",
      "params": {"extractMetadata": true}
    }
  ],
  "outputTemplate": "market_report"
}
```

## Implementation Strategy

### Phase 1: Advanced Crawler (Week 1)
1. Extract crawler logic from `deepResearchServer.ts`
2. Create focused `advancedCrawlerServer.ts`
3. Add to server builder and configuration
4. Test with existing workflows

### Phase 2: Content Analysis (Week 2)  
1. Extract content processing logic
2. Create `contentAnalysisServer.ts`
3. Implement multiple extraction strategies
4. Add batch processing capabilities

### Phase 3: Research Templates (Week 3)
1. Design template schema
2. Create `researchTemplateServer.ts` 
3. Integrate with `customPromptsServer`
4. Create default research templates

### Phase 4: Migration & Cleanup (Week 4)
1. Update existing workflows to use new servers
2. Deprecate or simplify `deepResearchServer.ts`
3. Update documentation
4. Performance testing and optimization

## Benefits

### For LLMs
- **Flexibility**: Pick optimal tools for each research task
- **Composability**: Combine tools in novel ways
- **Specialization**: Each tool excels at its specific function

### For Developers
- **Maintainability**: Smaller, focused codebases
- **Reusability**: Tools work independently and together
- **Extensibility**: Easy to add new capabilities
- **Testing**: Isolated components are easier to test

### For Users
- **Performance**: Optimized tools for specific tasks
- **Reliability**: Focused servers with better error handling
- **Customization**: Template-driven research workflows
- **Scalability**: Add new research types easily

## Migration Considerations

### Backward Compatibility
- Keep `deepResearchServer.ts` during transition
- Gradual migration of existing workflows
- Clear deprecation timeline

### Configuration Updates
- Add new servers to `builder.ts`
- Update MCP configuration
- Template management integration

### Testing Strategy
- Unit tests for each new server
- Integration tests for template workflows
- Performance benchmarks
- Memory usage monitoring

## Next Steps

1. **Review and approve** this architectural plan
2. **Start with Phase 1**: Advanced Crawler Server implementation
3. **Create templates** for common research patterns
4. **Test integration** with existing `customPromptsServer`
5. **Gather feedback** from initial implementation

This modular approach aligns with the established patterns in the Docomoe app and provides the flexibility needed for sophisticated research workflows while maintaining the simplicity that makes each component easy to understand and maintain.
