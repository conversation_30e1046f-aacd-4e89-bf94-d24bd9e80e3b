# MCP Presenter Architecture Documentation

## Overview

MCP (Model Context Protocol) Presenter is the core module responsible for managing MCP servers and tools in Docomoe, with the following main features:

1.  **MCP Server Management**: Start, stop, configure, default server settings, and npm registry speed testing.
2.  **MCP Tool Management**: Define, name conflict handling, caching, permission checks, and calls.
3.  **LLM Adaptation**: Convert between MCP tool formats and different LLM providers (OpenAI, Anthropic, Gemini) tool formats.
4.  **Monitoring Server Status and Publishing Events**: Monitor server status and publish related events via `eventBus`.

## Core Components

```mermaid
classDiagram
    class IMCPPresenter {
        <<Interface>>
        +getMcpServers()
        +getMcpClients()
        +startServer()
        +stopServer()
        +callTool()
        +getAllToolDefinitions()
        +mcpToolsToOpenAITools()
        +openAIToolsToMcpTool()
        +mcpToolsToAnthropicTools()
        +anthropicToolUseToMcpTool()
        +mcpToolsToGeminiTools()
        +geminiFunctionCallToMcpTool()
        +addMcpServer()
        +removeMcpServer()
        +updateMcpServer()
        +getMcpDefaultServers()
        +addMcpDefaultServer()
        +removeMcpDefaultServer()
        +toggleMcpDefaultServer()
        +getMcpEnabled()
        +setMcpEnabled()
        +resetToDefaultServers()
    }

    class McpPresenter {
        -serverManager: ServerManager
        -toolManager: ToolManager
        -configPresenter: IConfigPresenter
        +initialize()
        +getMcpServers()
        +getMcpClients()
        +startServer()
        +stopServer()
        +callTool()
        +getAllToolDefinitions()
        +mcpToolsToOpenAITools()
        +openAIToolsToMcpTool()
        +mcpToolsToAnthropicTools()
        +anthropicToolUseToMcpTool()
        +mcpToolsToGeminiTools()
        +geminiFunctionCallToMcpTool()
        +addMcpServer()
        +removeMcpServer()
        +updateMcpServer()
        +getMcpDefaultServers()
        +addMcpDefaultServer()
        +removeMcpDefaultServer()
        +toggleMcpDefaultServer()
        +getMcpEnabled()
        +setMcpEnabled()
        +resetToDefaultServers()
    }

    class ServerManager {
        -clients: Map<string, McpClient>
        -configPresenter: IConfigPresenter
        -npmRegistry: string | null
        +testNpmRegistrySpeed()
        +getNpmRegistry()
        +startServer()
        +stopServer()
        +getRunningClients()
        +getDefaultServerNames()
        +getDefaultClients()
        +getClient()
        +isServerRunning()
    }

    class ToolManager {
        -configPresenter: IConfigPresenter
        -serverManager: ServerManager
        -cachedToolDefinitions: MCPToolDefinition[] | null
        -toolNameToTargetMap: Map<string, object> | null
        +getAllToolDefinitions()
        +callTool()
        +checkToolPermission()
        +handleServerListUpdate()
        +getRunningClients()
    }

    class McpClient {
        +serverName: string
        +serverConfig: Record<string, unknown>
        -client: Client | null
        -transport: Transport | null
        -isConnected: boolean
        -npmRegistry: string | null
        +connect()
        +disconnect()
        +callTool()
        +listTools()
        +readResource()
        +isServerRunning()
    }

    class McpConfHelper {
        -mcpStore: ElectronStore<IMcpSettings>
        +getMcpServers()
        +setMcpServers()
        +getMcpDefaultServers()
        +addMcpDefaultServer()
        +removeMcpDefaultServer()
        +toggleMcpDefaultServer()
        +setMcpEnabled()
        +getMcpEnabled()
        +addMcpServer()
        +removeMcpServer()
        +updateMcpServer()
        +resetToDefaultServers()
        +onUpgrade()
    }

    class IConfigPresenter {
        <<Interface>>
        +getMcpServers()
        +setMcpServers()
        +getMcpDefaultServers()
        +addMcpDefaultServer()
        +removeMcpDefaultServer()
        +toggleMcpDefaultServer()
        +setMcpEnabled()
        +getMcpEnabled()
        +addMcpServer()
        +removeMcpServer()
        +updateMcpServer()
        +resetToDefaultServers()
        +getLanguage()
        // ... other config methods
    }


    McpPresenter ..|> IMCPPresenter
    McpPresenter o-- ServerManager
    McpPresenter o-- ToolManager
    McpPresenter o-- IConfigPresenter

    ServerManager o-- IConfigPresenter
    ServerManager "1" *-- "0..*" McpClient : manages

    ToolManager o-- IConfigPresenter
    ToolManager o-- ServerManager : uses

    McpConfHelper ..> IConfigPresenter : (typically implements relevant parts)
```

## Data Flow

### 1. Initialization and Default Server Startup

```mermaid
sequenceDiagram
    participant AppStartup
    participant McpPresenter
    participant ServerManager
    participant IConfigPresenter
    participant McpClient

    AppStartup->>McpPresenter: constructor(configPresenter)
    McpPresenter->>ServerManager: constructor(configPresenter)
    McpPresenter->>ToolManager: constructor(configPresenter, serverManager)
    AppStartup->>McpPresenter: initialize()
    McpPresenter->>IConfigPresenter: getMcpServers()
    McpPresenter->>IConfigPresenter: getMcpDefaultServers()
    McpPresenter->>ServerManager: testNpmRegistrySpeed()
    ServerManager-->>McpPresenter: (registry selected)
    loop For each defaultServerName
        McpPresenter->>ServerManager: startServer(defaultServerName)
        ServerManager->>IConfigPresenter: getMcpServers() (to get config)
        ServerManager->>McpClient: new McpClient(name, config, npmRegistry)
        ServerManager->>McpClient: connect()
        McpClient->>McpClient: Establish Transport (stdio/sse/http/inmemory)
        McpClient->>MCP Server: Connect Request
        MCP Server-->>McpClient: Connected
        McpClient-->>ServerManager: Connected (triggers status event)
        ServerManager-->>McpPresenter: Success / Error
    end
```

### 2. LLM Tool Call Flow (OpenAI as an Example)

```mermaid
sequenceDiagram
    participant LLM Provider
    participant McpPresenter
    participant ToolManager
    participant McpClient
    participant MCP Server

    Note over LLM Provider, McpPresenter: 1. Get and convert tool definitions (as needed)
    LLM Provider->>McpPresenter: getAllToolDefinitions()
    McpPresenter->>ToolManager: getAllToolDefinitions()
    ToolManager->>ServerManager: getRunningClients()
    ServerManager-->>ToolManager: List<McpClient>
    loop For each client
        ToolManager->>McpClient: listTools()
        McpClient-->>ToolManager: Raw Tool List
    end
    ToolManager->>ToolManager: Handle conflicts, cache definitions, and mappings
    ToolManager-->>McpPresenter: Processed MCPToolDefinition[]
    McpPresenter->>McpPresenter: mcpToolsToOpenAITools(definitions)
    McpPresenter-->>LLM Provider: OpenAI Tool Format

    Note over LLM Provider, McpPresenter: 2. LLM generates tool call requests (OpenAI format)
    LLM Provider->>LLM Provider: LLM decides to call tool(s)
    LLM Provider->>LLM Provider: Generates tool_calls (OpenAI Format)

    Note over LLM Provider, McpPresenter: 3. Convert and execute tool calls
    loop For each tool_call from LLM
        LLM Provider->>McpPresenter: openAIToolsToMcpTool(tool_call)
        McpPresenter-->>LLM Provider: Standard MCPToolCall
        LLM Provider->>McpPresenter: callTool(mcpToolCall)
        McpPresenter->>ToolManager: callTool(mcpToolCall)
        ToolManager->>ToolManager: Lookup target client & original name
        ToolManager->>ToolManager: checkToolPermission()
        alt Permission Denied
            ToolManager-->>McpPresenter: Error Response
        else Permission Granted
            ToolManager->>McpClient: callTool(originalToolName, args)
            McpClient->>MCP Server: Execute Tool
            MCP Server-->>McpClient: Raw Result
            McpClient-->>ToolManager: ToolCallResult
            ToolManager-->>McpPresenter: MCPToolResponse (triggers event)
        end
        McpPresenter-->>LLM Provider: Formatted Response (Success or Error)
    end

    Note over LLM Provider: 4. Handle results and continue (e.g., add tool responses to context, generate next response, or further calls)
    LLM Provider->>LLM Provider: Add tool response(s) to context
    LLM Provider->>LLM Provider: Generate next response or further calls

```

## Key Designs

1.  **Hierarchical Architecture**:
    *   Interface Layer (`IMCPPresenter`): Defines the common API.
    *   Presentation Layer (`McpPresenter`): Coordinator, handles LLM adaptation and delegation.
    *   Management Layer (`ServerManager`, `ToolManager`): Handles server lifecycle and tool management/call logic.
    *   Configuration Layer (`IConfigPresenter`, `McpConfHelper`): Provides and persists configuration.
    *   Client Layer (`McpClient`): Encapsulates communication with individual MCP servers.

2.  **Multi-Protocol Support**:
    *   `McpClient` supports stdio, SSE, HTTP, and InMemory via different `Transport` implementations.

3.  **Tool Management and Adaptation**:
    *   `ToolManager` centralizes tool definition retrieval, **name conflict resolution**, and caching.
    *   `McpPresenter` handles conversion between MCP and LLM-specific formats.
    *   `ToolManager` uses a mapping table (`toolNameToTargetMap`) to route (possibly renamed) tool calls to the correct `McpClient` and original tool name.

4.  **Configuration-Driven and Persisted**:
    *   Configuration is managed by `McpConfHelper`.
    *   Persisted using `electron-store`.

5.  **Error Handling and Event Notification**:
    *   Error handling included in server startup (`ServerManager`), tool calls (`ToolManager`), etc.
    *   Status changes and result events published via `eventBus`.

6.  **Performance and Environment Optimization**:
    *   `ServerManager` automatically tests and selects the fastest npm registry.
    *   `McpClient` fine-tunes environment variables for `stdio` processes (PATH, proxy, npm registry).
