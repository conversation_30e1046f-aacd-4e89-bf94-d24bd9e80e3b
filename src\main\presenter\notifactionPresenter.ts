import { nativeImage, Notification, NotificationConstructorOptions } from 'electron'
import icon from '../../../resources/icon.png?asset'
import { eventBus } from '@/eventbus'
import { NOTIFICATION_EVENTS } from '@/events'
import { presenter } from '.'

interface NotificationItem {
  id: string
  notification: Notification
}

export class NotificationPresenter {
  private notifications: Map<string, NotificationItem> = new Map()

  /**
   * Show a system notification
   */
  async showNotification(options: { id: string; title: string; body: string; silent?: boolean }) {
    const notificationsEnabled = presenter.configPresenter.getNotificationsEnabled()
    if (!notificationsEnabled) {
      return
    }

    // If a notification with the same ID already exists, clear it first
    this.clearNotification(options.id)

    const iconFile = nativeImage.createFromPath(icon)
    const notificationOptions: NotificationConstructorOptions = {
      title: options.title,
      body: options.body,
      silent: options.silent,
      // Add more options as needed, such as icons, etc.
      icon: iconFile
    }

    const notification = new Notification(notificationOptions)

    notification.on('click', () => {
      eventBus.emit(NOTIFICATION_EVENTS.SYS_NOTIFY_CLICKED, options.id)
      this.clearNotification(options.id)
    })

    // Automatically remove from management list when notification is closed
    notification.on('close', () => {
      this.notifications.delete(options.id)
    })

    this.notifications.set(options.id, {
      id: options.id,
      notification
    })

    notification.show()

    return options.id
  }

  /**
   * Clear notification with specified ID
   */
  clearNotification(id: string) {
    const notificationItem = this.notifications.get(id)
    if (notificationItem) {
      // Electron's Notification does not have a direct close method, but can be closed by destroying the object
      // Here we rely on GC to handle it, remove reference from Map
      this.notifications.delete(id)
    }
  }

  /**
   * Clear all notifications
   */
  clearAllNotifications() {
    this.notifications.forEach((item) => {
      this.clearNotification(item.id)
    })
    this.notifications.clear()
  }
}
