# Docomoe User Guide

Welcome to Docomoe! This guide will walk you through installing Docomoe, configuring its features, and using it to interact with various Large Language Models (LLMs).

## 📑 Table of Contents

- [Installation and Setup](#installation-and-setup)
- [Configuring LLM Providers](#configuring-llm-providers)
  - [Cloud LLM Providers](#cloud-llm-providers)
  - [Local Models (Ollama)](#local-models-ollama)
- [Basic Chat Functionalities](#basic-chat-functionalities)
  - [Creating New Conversations](#creating-new-conversations)
  - [Sending Messages](#sending-messages)
  - [Forking Conversations](#forking-conversations)
  - [Retrying Messages](#retrying-messages)
  - [Multi-Window and Multi-Tab](#multi-window-and-multi-tab)
- [Advanced Chat Functionalities](#advanced-chat-functionalities)
  - [Markdown Rendering](#markdown-rendering)
  - [Multi-Modal Content](#multi-modal-content)
  - [Artifacts Rendering](#artifacts-rendering)
- [Using Search Enhancement](#using-search-enhancement)
- [Using Tool Calling (MCP)](#using-tool-calling-mcp)
- [Privacy and Security Features](#privacy-and-security-features)
  - [Screen Projection Hiding](#screen-projection-hiding)
  - [Network Proxies](#network-proxies)
  - [Data Storage and Encryption](#data-storage-and-encryption)
- [DeepLink Support](#deeplink-support)

## Installation and Setup

To get started with Docomoe, download the latest version for your operating system from the [GitHub Releases page](https://github.com/ThinkInAIXYZ/docomoe/releases).

-   **Windows**: Download the `.exe` installer.
-   **macOS**: Download the `.dmg` installation file.
-   **Linux**: Download the `.AppImage` or `.deb` installation file.

After downloading, run the installer and follow the on-screen instructions to complete the installation.

## Configuring LLM Providers

Docomoe supports a wide range of LLM providers, both cloud-based and local.

1.  Launch the Docomoe application.
2.  Click the **Settings icon** (often a gear or cogwheel symbol).
3.  Navigate to the **"Model Providers"** tab.

### Cloud LLM Providers

Docomoe supports various cloud LLMs, including:
- DeepSeek
- OpenAI (including Azure OpenAI)
- Silicon Flow
- Grok
- Gemini
- Anthropic
- DashScope (Alibaba Cloud)
- Doubao (Volcano Engine)
- MiniMax
- Fireworks AI
- PPIO
- GitHub Models
- Moonshot
- OpenRouter
- Qiniu
- Zhipu AI
- Hunyuan (Tencent Cloud)
- And any provider compatible with OpenAI, Gemini, or Anthropic API formats.

To configure a cloud provider:
1.  Select the provider from the list.
2.  Enter your API key and any other required credentials.
3.  Save the configuration.

### Local Models (Ollama)

Docomoe has integrated support for Ollama, allowing you to manage and use local models without command-line operations.
1.  In the "Model Providers" tab, select Ollama.
2.  Docomoe allows you to:
    *   Download Ollama models directly within the application.
    *   Manage your existing Ollama models (deploy, run, remove).
3.  Once configured, you can select your local models when starting a new chat.

## Basic Chat Functionalities

### Creating New Conversations

-   Click the **"+" button** (usually prominently displayed) to start a new chat session.
-   You will typically be prompted to select the LLM you wish to use for this conversation from your configured providers.

### Sending Messages

-   Type your message into the input field at the bottom of the chat window.
-   Press Enter or click the send button to send your message to the LLM.

### Forking Conversations

-   If you want to explore a different line of thought or ask a follow-up question without altering the current conversation flow, you can fork the conversation.
-   Look for a "Fork" option on a specific message or for the entire conversation. This will create a new, separate chat session that branches off from the point you selected.

### Retrying Messages

-   If you're not satisfied with a response or if an error occurred, you can retry sending your message or ask the LLM to generate a new response.
-   This feature often allows you to get multiple variations of an answer.

### Multi-Window and Multi-Tab

Docomoe supports a multi-window and multi-tab architecture, similar to a web browser. This allows for:
-   Parallel multi-session operations.
-   Non-blocking experience, improving efficiency when working with multiple models or conversations simultaneously.

## Advanced Chat Functionalities

### Markdown Rendering

-   Docomoe provides complete Markdown rendering for chat messages.
-   This includes support for headings, lists, bold/italic text, links, and code blocks.
-   Code blocks are rendered using CodeMirror for syntax highlighting and clarity.

### Multi-Modal Content

-   Docomoe supports displaying multi-modal content within chats.
-   This means you can view images generated by models (e.g., using GPT-4o, Gemini, Grok text-to-image capabilities).
-   Support for Mermaid diagrams allows for rendering complex diagrams directly in the chat.

### Artifacts Rendering

-   Docomoe supports Artifacts rendering, which provides diverse ways to present results from LLMs, especially when using Tool Calling (MCP).
-   This can significantly save token consumption and present complex data more effectively than plain text.

## Using Search Enhancement

Docomoe enhances LLM responses by integrating with search engines. This provides more accurate, timely, and verifiable information.

-   **Automatic Search (MCP Mode)**: When using MCP-enabled models, the LLM can intelligently decide when to perform a web search to answer your query. It can use built-in integrations with BoSearch or Brave Search.
-   **Simulated Web Browsing**: Docomoe can simulate user web browsing for mainstream search engines like Google, Bing, Baidu, and Sogou Official Accounts. This allows the LLM to "read" search engine results like a human.
-   **Custom Search Engines**: You can configure Docomoe to use virtually any search engine, including internal corporate networks or specialized vertical domain search engines, by setting up a search assistant model.
-   Search results and other external information sources are often highlighted within the LLM's response for clarity.

## Using Tool Calling (MCP)

Docomoe features excellent Model Controller Platform (MCP) support, allowing LLMs to use tools and access external resources.

-   **Configuration**: MCP services can be configured through a user-friendly interface. DeepLink support allows for one-click installation of MCP services.
-   **Capabilities**: MCP enables:
    *   **Code Execution**: Run code snippets in a built-in Node.js environment.
    *   **Web Information Retrieval**: Fetch content from web pages.
    *   **File Operations**: Interact with local files.
    *   **Custom Tools**: Integrate other custom or third-party tools.
-   **Display and Debugging**:
    *   Tool calls are displayed clearly and aesthetically within the chat.
    *   A detailed tool call debugging window shows parameters and return data, with automatic formatting.
-   **Built-in Services**: Many common use cases are supported out-of-the-box with built-in utilities, requiring no secondary installation. Visual model capabilities can also be converted into universally usable functions via MCP.

## Privacy and Security Features

Docomoe prioritizes user privacy and data security.

### Screen Projection Hiding

-   To prevent sensitive information from being accidentally displayed during screen sharing or projections, Docomoe offers a screen projection hiding feature. This typically obscures the chat content when active.

### Network Proxies

-   You can configure Docomoe to use network proxies, adding an extra layer of privacy and potentially bypassing network restrictions.

### Data Storage and Encryption

-   Chat data and configuration data have reserved encryption interfaces.
-   Docomoe focuses on local data storage where possible to reduce the risk of information leakage.

## DeepLink Support

Docomoe utilizes rich DeepLink support, which allows:
-   **Initiating Conversations**: Start new chat sessions or interact with specific parts of the application via external links. This enables seamless integration with other applications or workflows.
-   **One-Click MCP Service Installation**: Simplify the setup of MCP services by installing them through a single click on a DeepLink.

---

This user guide should help you get the most out of Docomoe. For more detailed information on specific features or for troubleshooting, please refer to the project's [GitHub repository](https://github.com/ThinkInAIXYZ/docomoe) or the community forums.The `README.md` provided a lot of useful information for creating the user guide. 
