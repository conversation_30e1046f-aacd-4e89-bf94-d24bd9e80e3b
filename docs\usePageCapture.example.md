# usePageCapture Example

## Basic Usage

```typescript
import { usePageCapture } from '@/composables/usePageCapture'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { isCapturing, captureArea, captureAndCopy } = usePageCapture()

// Get app version
const appVersion = await devicePresenter.getAppVersion()

// Define target area for screenshot
const getTargetRect = () => {
  const element = document.querySelector('.target-element')
  if (!element) return null

  const rect = element.getBoundingClientRect()
  return {
    x: Math.round(rect.x),
    y: Math.round(rect.y),
    width: Math.round(rect.width),
    height: Math.round(rect.height)
  }
}

// Execute screenshot and copy to clipboard
const handleCapture = async () => {
  const success = await captureAndCopy({
    container: '.scroll-container',
    getTargetRect,
    watermark: {
      isDark: true,
      version: appVersion,
      texts: {
        brand: 'Docomoe',
        tip: t('common.watermarkTip')
      }
    }
  })

  if (success) {
    console.log('Screenshot successful')
  } else {
    console.error('Screenshot failed')
  }
}
```

## Advanced Usage

```typescript
import { usePageCapture, createCapturePresets } from '@/composables/usePageCapture'

const { captureAndCopy } = usePageCapture()
const { captureFullConversation, captureMessageRange, captureCustomElement } = createCapturePresets()

// Capture full conversation
const captureConversation = async () => {
  const config = captureFullConversation({
    isDark: true,
    version: appVersion,
    texts: {
      brand: 'Docomoe',
      tip: t('common.watermarkTip')
    }
  })

  await captureAndCopy(config)
}

// Capture specified message range
const captureMessages = async (startId: string, endId: string) => {
  const config = captureMessageRange(startId, endId, {
    isDark: true,
    version: appVersion,
    texts: {
      brand: 'Docomoe',
      tip: t('common.watermarkTip')
    }
  })

  await captureAndCopy(config)
}

// Capture custom element
const captureCustomArea = async () => {
  const config = captureCustomElement('.chat-sidebar', '.main-container', {
    isDark: true,
    version: appVersion,
    texts: {
      brand: 'Docomoe',
      tip: t('common.watermarkTip')
    }
  })

  await captureAndCopy(config)
}
```

## Advanced Usage

```typescript
// Custom configuration parameters
const advancedCapture = async () => {
  const result = await captureArea({
    container: '.custom-scroll-container',
    getTargetRect: () => {
      // Custom target area calculation logic
      const elements = document.querySelectorAll('.message-item')
      if (elements.length === 0) return null

      const firstRect = elements[0].getBoundingClientRect()
      const lastRect = elements[elements.length - 1].getBoundingClientRect()

      return {
        x: Math.round(firstRect.x),
        y: Math.round(firstRect.y),
        width: Math.round(firstRect.width),
        height: Math.round(lastRect.bottom - firstRect.top)
      }
    },
    watermark: {
      isDark: false,
      version: '1.0.0',
      texts: {
        brand: 'MyApp',
        tip: 'Custom watermark tip'
      }
    },
    scrollBehavior: 'smooth', // Smooth scroll
    captureDelay: 500,        // Increase delay time
    maxIterations: 50,        // Increase maximum iteration count
    scrollbarOffset: 15,      // Custom scroll bar offset
    containerHeaderOffset: 60 // Custom container header offset
  })

  if (result.success && result.imageData) {
    // Handle screenshot data
    console.log('Screenshot successful, data length:', result.imageData.length)

    // Can be saved to file or perform other operations
    // await saveImageToFile(result.imageData)
  } else {
    console.error('Screenshot failed:', result.error)
  }
}
```

## Usage in Components

```vue
<template>
  <div>
    <button @click="handleCapture" :disabled="isCapturing">
      {{ isCapturing ? 'Screenshotting...' : 'Screenshot' }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { usePageCapture } from '@/composables/usePageCapture'

const { isCapturing, captureAndCopy } = usePageCapture()

const handleCapture = async () => {
  await captureAndCopy({
    container: '.message-list',
    getTargetRect: () => {
      const element = document.querySelector('.target-content')
      if (!element) return null

      const rect = element.getBoundingClientRect()
      return {
        x: Math.round(rect.x),
        y: Math.round(rect.y),
        width: Math.round(rect.width),
        height: Math.round(rect.height)
      }
    }
  })
}
</script>
```

## Configuration Parameters

| Parameter | Type | Default | Description |
|------|------|--------|------|
| `container` | `string \| HTMLElement` | - | Scroll container, CSS selector or DOM element |
| `getTargetRect` | `() => CaptureRect \| null` | - | Function to get target screenshot area |
| `watermark` | `WatermarkConfig` | `undefined` | Watermark configuration |
| `scrollBehavior` | `'auto' \| 'smooth'` | `'auto'` | Scroll behavior |
| `captureDelay` | `number` | `350` | Delay time after each screenshot (ms) |
| `maxIterations` | `number` | `30` | Maximum iteration count |
| `scrollbarOffset` | `number` | `20` | Scroll bar offset to avoid capturing scroll bar |
| `containerHeaderOffset` | `number` | `44` | Space reserved at the top of the container (such as toolbar height) |

## Return Value Description

### CaptureResult

```typescript
interface CaptureResult {
  success: boolean    // Whether successful or not
  imageData?: string  // base64 encoded image data
  error?: string      // Error message
}
```

### Method Description

- `captureArea(config)`: Execute screenshot and return result object
- `captureAndCopy(config)`: Execute screenshot and copy to clipboard, returns success status
- `isCapturing`: Reactive screenshot status, used to display loading status
