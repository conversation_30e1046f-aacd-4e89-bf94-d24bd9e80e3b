# Docomoe DeepLinks Documentation

Docomoe supports external invocation through deeplinks. This documentation introduces the types of deeplinks supported by Docomoe, their parameters, and usage methods.

## Start Chat

Use this deeplink to quickly start a new chat session with optional model selection and initial message.

### URL Format

```
docomoe://start?msg={query}&system={systemPrompt}&model={modelId|modelName}
```

### Parameters

| Parameter | Type   | Required | Description                                                                                          |
| --------- | ------ | -------- | ---------------------------------------------------------------------------------------------------- |
| msg       | string | No       | Initial chat message                                                                                 |
| system    | string | No       | System prompt                                                                                        |
| model     | string | No       | Model ID or name, e.g., "gpt-3.5-turbo", "deepseek-chat"                                             |

### Behavior

1. If not currently on the chat page, it will automatically navigate to the chat page
2. If a model is specified, it will attempt to match and select the corresponding model (exact match first, then fuzzy match)
3. If an initial message is provided, it will be automatically filled in the input box

### Examples

Basic usage, open a conversation with GPT-3.5:

```
docomoe://start?model=gpt-3.5-turbo
```

Specify an initial message:

```
docomoe://start?msg=Help me write an article about artificial intelligence
```

Complete example (specifying model, message, and system prompt):

```
docomoe://start?msg=Help me analyze this code&model=deepseek-coder&system=You are a code analysis expert
```

## Install MCP

Use this deeplink to install Model Control Protocol (MCP) service configuration.

Use this deeplink to install Model Control Protocol (MCP) service configuration.

### URL Format

```
docomoe://mcp/install?code={base64Encode(JSON.stringify(jsonConfig))}
```

### Parameters

| Parameter | Type   | Required | Description                                                                                 |
| --------- | ------ | -------- | ------------------------------------------------------------------------------------------- |
| code      | string | Yes      | JSON string of MCP service configuration (Base64 encoded)                                   |

### Behavior

1. If the MCP feature is not enabled, it will be automatically enabled
2. Automatically navigate to the MCP configuration section of the settings page
3. Open the add server dialog and automatically fill in the configuration data

### Configuration JSON Format

The MCP configuration JSON should contain the following structure:

Minimal JSON Format Example:

### If command is present but url is not, it will be treated as stdio
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "mcp-filesystem-server",
      "args": [
        "/Users/<USER>/Desktop",
      ]
    }
  }
}
```
### If URL is present but command is not, it will be treated as SSE
```json

{
  "mcpServers": {
    "browser-use-mcp-server": {
      "url": "http://localhost:8000/sse"
    }
  }
}
```

Complete JSON Format Example:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "mcp-filesystem-server",
      "args": [
        "/Users/<USER>/Desktop",
      ],
      "env": {},
      "descriptions": "filesystem mcp server",
      "icons": "📁",
      "type" :"stdio",
      "autoApprove": ["all"]
    }
  }
}
```
```json
{
  "mcpServers": {
    "browser-use-mcp-server": {
      "url": "http://localhost:8000/sse",
      "type":"sse",
      "icons": "🏠",
      "autoApprove": ["all"],
    }
  }
}
```

## How to Generate MCPConfig code params

```javascript
import { encode } from 'js-base64';

const config = {
  "mcpServers": {
    "browser-use-mcp-server": {
      "url": "http://localhost:8000/sse"
    }
  }
}
const code =encode(JSON.stringify(config))

```

## Chat Example
```
docomoe://start?msg=%E5%A4%A9%E6%B0%94%E4%B8%8D%E9%94%99&system=%E4%BD%A0%E6%98%AF%E4%B8%80%E4%B8%AA%E9%A2%84%E6%8A%A5%E5%91%98%2C%E8%AF%B7%E4%BD%A0%E7%A4%BC%E8%B2%8C%E8%80%8C%E4%B8%93%E4%B8%9A%E5%9B%9E%E7%AD%94%E7%94%A8%E6%88%B7%E9%97%AE%E9%A2%98&model=deepseek-chat
```

## STDIO Example:

```
docomoe://mcp/install?code=eyJtY3BTZXJ2ZXJzIjp7ImZpbGVzeXN0ZW0iOnsiY29tbWFuZCI6Im1jcC1maWxlc3lzdGVtLXNlcnZlciIsImFyZ3MiOlsiL1VzZXJzL3VzZXJuYW1lL0Rlc2t0b3AiXX19fQ==
```

## SSE Example:

```
docomoe://mcp/install?code=eyJtY3BTZXJ2ZXJzIjp7ImJyb3dzZXItdXNlLW1jcC1zZXJ2ZXIiOnsidXJsIjoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL3NzZSJ9fX0=
```