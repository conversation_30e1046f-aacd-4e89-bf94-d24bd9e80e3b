import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import {
  ListPromptsRequestSchema,
  GetPromptRequestSchema
} from '@modelcontextprotocol/sdk/types.js'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import { presenter } from '@/presenter'
import { eventBus } from '@/eventbus'
import { CONFIG_EVENTS } from '@/events'

interface PromptDefinition {
  name: string
  description: string
  arguments: Array<{
    name: string
    description: string
    required: boolean
  }>
}

export class CustomPromptsServer {
  private server: Server
  private promptsCache: PromptDefinition[] | null = null

  constructor() {
    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/custom-prompts-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          prompts: {}
        }
      }
    )

    // Setup request handlers
    this.setupRequestHandlers()
    // Listen for custom prompts change event
    this.setupEventListeners()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Setup event listeners
  private setupEventListeners(): void {
    eventBus.on(CONFIG_EVENTS.CUSTOM_PROMPTS_CHANGED, () => {
      this.promptsCache = null
    })
  }

  // Get prompts list
  private async getPromptsList(): Promise<PromptDefinition[]> {
    // If cache exists, return it
    if (this.promptsCache !== null) {
      return this.promptsCache
    }

    try {
      const prompts = await presenter.configPresenter.getCustomPrompts()

      if (!prompts || prompts.length === 0) {
        this.promptsCache = []
        return []
      }

      this.promptsCache = prompts.map((prompt) => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.parameters
          ? prompt.parameters.map((param) => ({
              name: param.name,
              description: param.description,
              required: !!param.required
            }))
          : []
      }))

      return this.promptsCache
    } catch (error) {
      this.promptsCache = []
      return []
    }
  }

  // Get prompt content
  private async getPromptContent(name: string, content: string, args?: Record<string, string>) {
    const prompts = await presenter.configPresenter.getCustomPrompts()
    if (!prompts || prompts.length === 0) throw new Error('No prompts found')

    const prompt = prompts.find((p) => p.name === name)
    if (!prompt) throw new Error('Prompt not found')

    let promptContent = prompt.content

    // Replace placeholder parameters
    if (args && prompt.parameters) {
      // Iterate through all parameters and replace content
      for (const param of prompt.parameters) {
        const value = args[param.name] || ''
        promptContent = promptContent.replace(new RegExp(`{{${param.name}}}`, 'g'), value)
      }
    }

    return {
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: `${promptContent}\n\n${content}`
          }
        }
      ]
    }
  }

  // Setup request handlers
  private setupRequestHandlers(): void {
    // Set prompts list handler
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      const prompts = await this.getPromptsList()
      return { prompts }
    })

    // Set prompt get handler
    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      try {
        const { name, arguments: args } = request.params
        const response = await this.getPromptContent(name, '', args as Record<string, string>)
        return {
          messages: response.messages,
          _meta: {}
        }
      } catch (error) {
        return {
          messages: [
            {
              role: 'system',
              content: {
                type: 'text',
                text: `Error: ${error instanceof Error ? error.message : String(error)}`
              }
            }
          ],
          _meta: {}
        }
      }
    })
  }

  // Provide access to server for external calls
  public getServer(): Server {
    return this.server
  }
}
