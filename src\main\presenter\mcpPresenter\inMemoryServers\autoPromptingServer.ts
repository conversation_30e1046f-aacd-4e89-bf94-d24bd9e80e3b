import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { presenter } from '@/presenter'

// --- Type Definitions and Schema (Merged) ---

// Schema for template parameters
const TemplateParameterSchema = z.object({
  name: z.string().describe('Parameter name'),
  description: z.string().describe('Parameter description'),
  required: z.boolean().describe('Whether the parameter is required')
  // type field has been removed, all template parameters are strings
})

// Schema for template definitions
const TemplateDefinitionSchema = z.object({
  name: z.string().describe('Template name'),
  description: z.string().describe('Template description'),
  content: z.string().describe('Template content, including placeholders'),
  parameters: z.array(TemplateParameterSchema).optional().describe('List of template parameters')
})

// Infer TypeScript types from Schema using z.infer
type TemplateDefinition = z.infer<typeof TemplateDefinitionSchema>
// type TemplateParameter = z.infer<typeof TemplateParameterSchema>

// Schema for function arguments to get template parameter information
const GetTemplateParametersArgsSchema = z.object({
  templateName: z.string().describe('Name of the template to get parameters for')
})

// Schema for function arguments to fill a template
const FillTemplateArgsSchema = z.object({
  templateName: z.string().describe('Name of the template to fill'),
  templateArgs: z
    .record(z.string(), z.string())
    .optional()
    .describe('Key-value pairs of parameters required to fill the template'),
  additionalContent: z
    .string()
    .optional()
    .describe('Additional content to add to the end of the prompt')
})

// Convert Zod Schema to JSON Schema
const GetTemplateParametersArgsJsonSchema = zodToJsonSchema(GetTemplateParametersArgsSchema)
const FillTemplateArgsJsonSchema = zodToJsonSchema(FillTemplateArgsSchema)

// --- MCP Server Implementation ---
export class AutoPromptingServer {
  private server: Server

  constructor() {
    this.server = new Server(
      {
        name: 'template-prompt-server',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {} // Only declaring tool capabilities
        }
      }
    )

    this.setupRequestHandlers()
  }

  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  /**
   * Helper function: Get template definition from presenter by template name.
   * Now directly returns TemplateDefinition type since Schema has been adapted.
   * @param name Template name
   * @returns Template definition or undefined
   */

  private async getTemplateDefinition(name: string): Promise<TemplateDefinition | undefined> {
    try {
      // Directly assert the presenter's return type as TemplateDefinition[],
      // since TemplateDefinitionSchema now matches the actual return structure from presenter.
      const templates: TemplateDefinition[] = await presenter.configPresenter.getCustomPrompts()
      return templates.find((t) => t.name === name)
    } catch (error) {
      console.error('Failed to retrieve custom templates:', error)
      return undefined
    }
  }

  // List all available tools (corresponding to ListToolsRequestSchema)
  private listTools() {
    return {
      tools: [
        {
          name: 'list_all_prompt_template_names',
          description: 'Get the list of all available prompt template names.',
          inputSchema: zodToJsonSchema(z.object({})) // No parameters needed
        },
        {
          name: 'get_prompt_template_parameters',
          description:
            'Get the list of parameters and descriptions required for a prompt template by name.',
          inputSchema: GetTemplateParametersArgsJsonSchema
        },
        {
          name: 'fill_prompt_template',
          description:
            'Fill the template content and generate the final prompt based on the prompt template name and parameters.',
          inputSchema: FillTemplateArgsJsonSchema
        }
      ]
    }
  }

  // Handle tool calls (corresponding to CallToolRequestSchema)
  private async handleToolCall(request: z.infer<typeof CallToolRequestSchema>) {
    const { name, arguments: args } = request.params

    if (name === 'list_all_prompt_template_names') {
      // 1. Get all template names
      try {
        const templates: TemplateDefinition[] = await presenter.configPresenter.getCustomPrompts()
        const templateNames = templates.map((t) => t.name)
        return {
          content: [{ type: 'text', text: JSON.stringify(templateNames) }]
        }
      } catch (error) {
        console.error('Failed to retrieve the list of template names:', error)
        throw new Error('Unable to retrieve the list of template names.')
      }
    } else if (name === 'get_prompt_template_parameters') {
      // 2. Get the parameters and other information for the model
      const parsed = GetTemplateParametersArgsSchema.safeParse(args)
      if (!parsed.success) {
        throw new Error(
          `Invalid parameters for get_prompt_template_parameters: ${parsed.error.errors.map((e) => e.message).join(', ')}`
        )
      }

      const { templateName } = parsed.data
      const template = await this.getTemplateDefinition(templateName)

      if (!template) {
        throw new Error(`Template not found: ${templateName}`)
      }

      // Now template.parameters is already the type of TemplateParameterSchema, no additional adaptation needed
      return {
        content: [{ type: 'text', text: JSON.stringify(template.parameters || []) }]
      }
    } else if (name === 'fill_prompt_template') {
      // 3. Fill the template, get the final prompt
      const parsed = FillTemplateArgsSchema.safeParse(args)
      if (!parsed.success) {
        throw new Error(
          `Invalid parameters for fill_prompt_template: ${parsed.error.errors.map((e) => e.message).join(', ')}`
        )
      }

      const { templateName, templateArgs, additionalContent } = parsed.data
      const template = await this.getTemplateDefinition(templateName)

      if (!template) {
        throw new Error(`Template not found: ${templateName}`)
      }

      let filledContent = template.content // Use template content

      // Replace parameter placeholders
      if (templateArgs && template.parameters) {
        for (const param of template.parameters) {
          const value = templateArgs[param.name] || ''
          filledContent = filledContent.replace(new RegExp(`{{${param.name}}}`, 'g'), value)
        }
      }

      // Add additional content
      const finalPrompt = additionalContent
        ? `${filledContent}\n\n${additionalContent}`
        : filledContent

      return {
        content: [{ type: 'text', text: finalPrompt }]
      }
    }

    throw new Error(`Unknown tool: ${name}`)
  }

  // Set all request handlers
  private setupRequestHandlers(): void {
    // Register handler for ListToolsRequestSchema, returning metadata for all tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return this.listTools()
    })

    // Register handler for CallToolRequestSchema to handle tool calls based on tool name
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        return await this.handleToolCall(request)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        return {
          content: [{ type: 'text', text: `Error: ${errorMessage}` }],
          isError: true
        }
      }
    })
  }
}
