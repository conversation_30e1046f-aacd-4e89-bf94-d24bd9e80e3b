# Docomoe Documentation

This directory contains design documents and usage guides for Docomoe.

## User Documentation

- [User Guide](./user-guide.md) - How to install, configure, and use Docomoe.

## Developer Documentation

- [Developer Guide](./developer-guide.md) - Information for developers contributing to or building with Docomoe.

## Design Documents

- [Event System Design](./event-system-design.md) - Design and implementation of the application event system
- [Data Sync Feature](./data-sync-feature.md) - Design and implementation of the data sync feature
- [LLM Provider Interface Design](./llm-provider-interface.md) - Design of the LLM Provider interface

## Design Documents

- [Event System Design](./event-system-design.md) - Design and implementation of the application event system
- [Data Sync Feature](./data-sync-feature.md) - Design and implementation of the data sync feature
- [LLM Provider Interface Design](./llm-provider-interface.md) - Design of the LLM Provider interface

## Build Guides

- [Linux Build Guide](./linux-build-guide.md) - Steps to build Docomoe on Linux
