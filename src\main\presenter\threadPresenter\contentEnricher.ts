import axios from 'axios'
import * as cheerio from 'cheerio'
import { SearchResult } from '../../../shared/presenter'
import { HttpsProxyAgent } from 'https-proxy-agent'
import { proxyConfig } from '@/presenter/proxyConfig'
// import { presenter } from '@/presenter'

/**
 * Content Enrichment tool class, used to handle URL content extraction and enrichment
 */
export class ContentEnricher {
  /**
   * Extract and enrich URL content from text
   * @param text Text containing URLs
   * @returns Enriched URL results array
   */
  static async extractAndEnrichUrls(text: string): Promise<SearchResult[]> {
    // Use a regular expression to match http and https links
    const urlRegex = /(https?:\/\/[^\s]+)/g
    const matches = text.match(urlRegex)

    if (!matches || matches.length === 0) {
      return []
    }

    const results: SearchResult[] = []

    for (const url of matches) {
      const result = await this.enrichUrl(url, results.length + 1)
      results.push(result as SearchResult)
    }

    return results
  }

  /**
   * Enrich the content of a single URL
   * @param url The URL to enrich
   * @param rank The result ranking (optional)
   * @returns Enriched SearchResult object
   */
  static async enrichUrl(url: string, rank: number = 1): Promise<SearchResult> {
    const timeout = 5000 // 5 seconds timeout

    try {
      const proxyUrl = proxyConfig.getProxyUrl()
      // Use axios to get the page content
      const response = await axios.get(url, {
        timeout,
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        },
        httpAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined
      })

      const $ = cheerio.load(response.data)
      // Remove unnecessary elements
      $('script, style, nav, header, footer, iframe, .ad, #ad, .advertisement').remove()

      // Get the page title
      const title = $('title').text().trim() || url

      // Try to get the main content
      const mainContent = this.extractMainContent($)
      // Get the icon
      const icon = this.extractFavicon($, url)

      // Get the page description
      const description =
        $('meta[name="description"]').attr('content') ||
        $('meta[property="og:description"]').attr('content') ||
        ''

      return {
        title,
        url,
        content: mainContent,
        icon,
        description,
        rank
      }
    } catch (error: Error | unknown) {
      console.error(
        `Failed to extract URL content ${url}:`,
        error instanceof Error ? error.message : ''
      )
      // If the extraction fails, only add the URL information
      return {
        title: url,
        url,
        rank,
        description: '',
        icon: ''
      }
    }
  }

  /**
   * Batch enrich search result content
   * @param results The search result array
   * @param limit The limit of the number of results to process (optional)
   * @returns Enriched search result array
   */
  static async enrichResults(results: SearchResult[], limit?: number): Promise<SearchResult[]> {
    const enrichedResults: SearchResult[] = []
    const resultsToProcess = limit ? results.slice(0, limit) : results

    for (const result of resultsToProcess) {
      try {
        const enrichedResult = await this.enrichUrl(result.url, result.rank)
        // Merge the original result and the enriched result
        enrichedResults.push({
          ...result,
          content: enrichedResult.content || result.description || '',
          icon: result.icon || enrichedResult.icon || ''
        })
      } catch (error) {
        console.error(`Error enriching content for ${result.url}:`, error)
        // If the extraction fails, keep the original result
        enrichedResults.push(result)
      }
    }

    return enrichedResults
  }

  /**
   * Extract the main content from HTML
   * @param $ The loaded HTML from cheerio
   * @returns Extracted content text
   */
  private static extractMainContent($: cheerio.CheerioAPI): string {
    // Try to get the main content
    let mainContent = ''
    const possibleSelectors = [
      'article',
      'main',
      '.content',
      '#content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '[role="main"]',
      '.container'
    ]

    for (const selector of possibleSelectors) {
      const element = $(selector)
      if (element.length > 0) {
        mainContent = element.text()
        break
      }
    }
    mainContent = mainContent
      .replace(/[\r\n]+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .trim()

    // If no main content is found, use body
    if (!mainContent) {
      mainContent = $('body').text()
    }
    // Clean up the text content
    mainContent = mainContent
      .replace(/[\r\n]+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .slice(0, 3000)
    return mainContent
  }

  /**
   * Extract the website icon from HTML
   * @param $ The loaded HTML from cheerio
   * @param url The page URL
   * @returns The icon URL
   */
  private static extractFavicon($: cheerio.CheerioAPI, url: string): string {
    // Try to get the website icon
    let icon = $('link[rel="icon"]').attr('href') || $('link[rel="shortcut icon"]').attr('href')

    // If a relative path icon is found, convert it to an absolute path
    if (icon && !icon.startsWith('http')) {
      const urlObj = new URL(url)
      icon = icon.startsWith('/')
        ? `${urlObj.protocol}//${urlObj.host}${icon}`
        : `${urlObj.protocol}//${urlObj.host}/${icon}`
    }

    // If no icon is found, use the default favicon.ico
    if (!icon) {
      const urlObj = new URL(url)
      icon = `${urlObj.protocol}//${urlObj.host}/favicon.ico`
    }

    return icon
  }

  /**
   * Enrich the user message with the extracted URL content
   * @param userText The original user message
   * @param urlResults The extracted URL content results
   * @returns Enriched user message
   */
  static enrichUserMessageWithUrlContent(userText: string, urlResults: SearchResult[]): string {
    if (urlResults.length === 0) {
      return userText
    }

    let enrichedContent = `---
  The following URL content is extracted from the user's message:
  <url-content>
  \n
  `
    for (let i = 0; i < urlResults.length; i++) {
      const result = urlResults[i]
      if (result.content) {
        enrichedContent += `<url-content-item><url>${result.url}</url>\n<content>${result.content}</content>\n</url-content-item>`
      }
    }
    enrichedContent += `</url-content>`

    return enrichedContent
  }

  /**
   * Convert HTML to concise Markdown format
   * Keep links and structured data, reduce data size
   * @param html HTML content
   * @param baseUrl The base URL, used to parse relative paths
   * @returns Converted Markdown content
   */
  static convertHtmlToMarkdown(html: string, baseUrl: string): string {
    const $ = cheerio.load(html)

    // Remove unnecessary elements
    $('script, style, nav, header, footer, iframe, .ad, #ad, .advertisement').remove()

    let markdown = ''

    // Extract possible search results links and text from the page
    const searchResults: { title: string; url: string; text: string }[] = []

    // 1. Find obvious search result links
    $('a').each((_, element) => {
      const $el = $(element)
      const href = $el.attr('href') || ''
      const text = $el.text().trim()

      // Skip empty links and obvious navigation links
      if (!href || href === '#' || text.length < 3 || href.includes('javascript:')) {
        return
      }

      // Convert to absolute URL
      let url = href
      try {
        url = href.startsWith('http') ? href : new URL(href, baseUrl).toString()
      } catch (error) {
        // If the URL construction fails, use the original href
        console.error('Failed to build URL:', error)
      }

      // Get the text around as a description
      const parent = $el.parent()
      let description = ''

      // Try to find a descriptive text in the parent or ancestor elements
      if (parent && parent.children().length > 1) {
        // Clone the parent element and remove all links, keeping only text content
        const parentClone = parent.clone()
        parentClone.find('a').remove()
        description = parentClone.text().trim()
      }

      if (!description) {
        // Try to find the next paragraph element
        const nextParagraph = $el.next('p, div, span')
        if (nextParagraph.length) {
          description = nextParagraph.text().trim()
        }
      }

      // 清理描述文本
      description = description.replace(/\s+/g, ' ').trim().substring(0, 200) // 限制描述长度

      searchResults.push({
        title: text,
        url,
        text: description
      })
    })

    // 2. Convert search results to Markdown format
    searchResults.forEach((result, index) => {
      markdown += `## Result ${index + 1}\n`
      markdown += `### [${result.title}](${result.url})\n`
      markdown += `- URL: ${result.url}\n`
      if (result.text) {
        markdown += `- Description: ${result.text}\n`
      }
      markdown += '\n'
    })

    // 3. If no structured results are extracted, extract the basic HTML structure
    if (searchResults.length === 0) {
      // Extract all visible text blocks and retain the basic structure
      $('h1, h2, h3, h4, h5, h6, p, div').each((_, element) => {
        const $el = $(element)
        // 跳过空白或很短的文本块
        const text = $el.text().trim()
        if (text.length < 5) return

        // 根据元素类型添加标记
        const tagName = $el.prop('tagName')?.toLowerCase() || ''
        if (tagName.startsWith('h')) {
          const level = parseInt(tagName.substring(1))
          markdown += `${'#'.repeat(level)} ${text}\n\n`
        } else {
          markdown += `${text}\n\n`
        }
      })

      // 再次提取所有链接
      $('a').each((_, element) => {
        const $el = $(element)
        const href = $el.attr('href') || ''
        const text = $el.text().trim()

        if (href && href !== '#' && text.length > 0) {
          let url = href
          try {
            url = href.startsWith('http') ? href : new URL(href, baseUrl).toString()
          } catch (error) {
            // If the URL construction fails, use the original href
          }
          markdown += `- [${text}](${url})\n`
        }
      })
    }

    // 4. Finally, add all image references
    $('img').each((_, element) => {
      const $el = $(element)
      const src = $el.attr('src') || ''
      const alt = $el.attr('alt') || 'Image'

      if (src) {
        let imageUrl = src
        try {
          imageUrl = src.startsWith('http') ? src : new URL(src, baseUrl).toString()
        } catch (error) {
          // If the URL construction fails, use the original src
        }
        markdown += `![${alt}](${imageUrl})\n`
      }
    })

    return markdown
  }
}
