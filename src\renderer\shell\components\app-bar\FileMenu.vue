<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button
        variant="ghost"
        class="flex-shrink-0 text-xs font-medium px-2 h-6 bg-transparent rounded-md flex items-center justify-center hover:bg-zinc-500/20 mr-1"
      >
        <Icon icon="lucide:menu" class="w-4 h-4" />
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent align="start" class="w-56">
      <DropdownMenuLabel>File</DropdownMenuLabel>
      <DropdownMenuSeparator />

      <DropdownMenuItem @click="handleOpenFile">
        <Icon icon="lucide:file" class="w-4 h-4 mr-2" />
        Open File
        <DropdownMenuShortcut>Ctrl+O</DropdownMenuShortcut>
      </DropdownMenuItem>

      <DropdownMenuItem @click="handleOpenFolder">
        <Icon icon="lucide:folder-open" class="w-4 h-4 mr-2" />
        Open Folder
        <DropdownMenuShortcut>Ctrl+Shift+O</DropdownMenuShortcut>
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      <DropdownMenuSub>
        <DropdownMenuSubTrigger>
          <Icon icon="lucide:clock" class="w-4 h-4 mr-2" />
          Recent Files
        </DropdownMenuSubTrigger>
        <DropdownMenuSubContent>
          <DropdownMenuItem disabled>
            <span class="text-muted-foreground">No recent files</span>
          </DropdownMenuItem>
        </DropdownMenuSubContent>
      </DropdownMenuSub>

      <DropdownMenuSeparator />

      <DropdownMenuLabel>Workspace</DropdownMenuLabel>

      <DropdownMenuItem @click="handleOpenWorkspace">
        <Icon icon="lucide:folder-tree" class="w-4 h-4 mr-2" />
        Open as Workspace
      </DropdownMenuItem>

      <DropdownMenuSub>
        <DropdownMenuSubTrigger>
          <Icon icon="lucide:history" class="w-4 h-4 mr-2" />
          Recent Workspaces
        </DropdownMenuSubTrigger>
        <DropdownMenuSubContent>
          <DropdownMenuItem disabled>
            <span class="text-muted-foreground">No recent workspaces</span>
          </DropdownMenuItem>
        </DropdownMenuSubContent>
      </DropdownMenuSub>

      <DropdownMenuSeparator />

      <DropdownMenuItem @click="handleShowInExplorer">
        <Icon icon="lucide:external-link" class="w-4 h-4 mr-2" />
        Show in File Explorer
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

// Placeholder handlers for now - just UI
const handleOpenFile = () => {
  console.log('Open File clicked')
  // TODO: Implement file picker dialog
}

const handleOpenFolder = () => {
  console.log('Open Folder clicked')
  // TODO: Implement folder picker dialog
}

const handleOpenWorkspace = () => {
  console.log('Open as Workspace clicked')
  // TODO: Implement workspace selection
}

const handleShowInExplorer = () => {
  console.log('Show in File Explorer clicked')
  // TODO: Implement show current directory in system file explorer
}
</script>
