---
description: 
globs: src/main/presenter/llmProviderPresenter/index.ts,src/main/presenter/llmProviderPresenter/baseProvider.ts
alwaysApply: false
---
# LLM Agent Loop and Provider Architecture

This document describes the architecture for handling LLM streaming completion, particularly focusing on the Agent loop that involves tool calls.

## Core Principles

1.  **Separation of Concerns:**
    *   `src/main/presenter/llmProviderPresenter/index.ts`: Manage overall Agent loop, conversation history, execute tools via `McpPresenter`, and communicate with the front end via `eventBus`.
    *   `src/main/presenter/llmProviderPresenter/providers/*.ts`: Each Provider file is responsible for interacting with a specific LLM API, handling request/response formats specific to the Provider, converting tool definitions, managing native and non-native tool call mechanisms (Prompt wrapping), and standardizing the output stream to a common event format.

2.  **Standardized Stream Events:** Provider implementation (`coreStream` method) uses the standardized interface `yield` events to decouple the main loop from the details of the Provider.

3.  **Single-Pass Streaming in Providers:** The core streaming method (`coreStream`) in each Provider should be a single-pass API call for each round of the conversation. It should not contain the logic for multiple-round tool calls.

## Architecture Details

### `llmProviderPresenter/index.ts` (`startStreamCompletion`)

*   **Agent Loop:** Contains the main `while` loop that manages the conversation flow, including multi-round LLM calls and tool usage as needed.
*   **State Management:** Maintain `conversationMessages` history during loop iterations.
*   **Provider Interaction:** Calls the `provider.coreStream()` method in each loop iteration.
*   **Event Handling:**
    *   Listens for standardized events `yield` by `coreStream`.
    *   Buffers text content (`currentContent`).
    *   Handles `tool_call_start/chunk/end` events:
        *   Collects complete tool call details (id, name, arguments).
        *   Calls `presenter.mcpPresenter.callTool` to execute the tool.
        *   Sends a `STREAM_EVENTS.RESPONSE` event with tool call status (`tool_call: 'start' | 'end' | 'error'`).
    *   Handles `reasoning` events and sends them via `STREAM_EVENTS.RESPONSE`.
    *   Handles `text` events and sends them via `STREAM_EVENTS.RESPONSE`.
    *   Handles `image_data` events and sends them via `STREAM_EVENTS.RESPONSE`.
    *   Handles `usage` events and aggregates them.
    *   Handles `stop` events:
        *   If `stop_reason: 'tool_use'`, adds buffered assistant messages and prepares for the next loop iteration.
        *   Otherwise, adds final assistant messages and breaks out of the loop.
*   **Loop Control:** Uses `needContinueConversation` and `toolCallCount` (compared to `MAX_TOOL_CALLS`) to manage the loop.
*   **Front-end Communication:** Sends standardized `STREAM_EVENTS` (`RESPONSE`, `END`, `ERROR`) via `eventBus`.

### Provider Implementation (`src/main/presenter/llmProviderPresenter/providers/*.ts`)

*   **`coreStream(messages, modelId, temperature, maxTokens)` Method:**
    *   **Input:** Receives current conversation messages (formatted according to Provider's needs, possibly containing results from previous tool calls) and generation parameters.
    *   **Tool Handling:**
        *   **Native Support:** If the Provider/model supports native function calls, converts MCP tools to the Provider's format (`convertToProviderTools`) and includes them in the API request.
        *   **No Native Support:** If native FC is not supported, prepare messages using Prompt wrapping (`prepareFunctionCallPrompt`) before making the API call.
    *   **API Call:** Makes a *single* streaming API call to the LLM Provider.
    *   **Stream Handling:** Iterates over native stream data blocks from the Provider.
    *   **Event Normalization:** Parses specific data blocks from the Provider and `yield` events that conform to the **Standardized Stream Event Interface**.
        *   Parses text, reasoning (`<think>` or native), tool calls (native or `<function_call>`), usage, errors, stop reasons, and image data.
    *   **Output:** Asynchronously `yield` `StandardizedStreamEvent` objects.
*   **Auxiliary Methods:** Contains auxiliary functions specific to the Provider, such as `formatMessages`, `convertToProviderTools`, `parseFunctionCalls`, `prepareFunctionCallPrompt`, etc.

### Standardized Stream Event Interface (`LLMCoreStreamEvent`)

```typescript
// Suggested to define in src/main/presenter/llmProviderPresenter/streamEvents.ts
interface LLMCoreStreamEvent {
  type: 'text' | 'reasoning' | 'tool_call_start' | 'tool_call_chunk' | 'tool_call_end' | 'error' | 'usage' | 'stop' | 'image_data';
  content?: string; // Used for type 'text'
  reasoning_content?: string; // Used for type 'reasoning'
  tool_call_id?: string; // Used for tool_call_* types
  tool_call_name?: string; // Used for tool_call_start
  tool_call_arguments_chunk?: string; // Used for tool_call_chunk (streaming parameters)
  tool_call_arguments_complete?: string; // Used for tool_call_end (optional, if available at once)
  error_message?: string; // Used for type 'error'
  usage?: { // Used for type 'usage'
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  stop_reason?: 'tool_use' | 'max_tokens' | 'stop_sequence' | 'error' | 'complete'; // Used for type 'stop'
  image_data?: { // Used for type 'image_data'
    data: string; // Base64 encoded image data
    mimeType: string;
  };
}
```

## Advantages

*   **Reduced code duplication:** Agent loop logic is centralized in `index.ts`.
*   **Improved maintainability:** Provider implementation is simpler, focused on API interactions and event normalization.
*   **Easier to add new providers:** Adding a new provider only requires implementing the `coreStream` method according to the standardized interface, without the need to copy complex Agent loops.
*   **Consistent behavior:** Ensure that tool handling, reasoning content parsing, and event sending are consistent across all providers.
