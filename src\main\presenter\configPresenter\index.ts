import { eventBus } from '@/eventbus'
import {
  IConfigPresenter,
  LLM_PROVIDER,
  MODEL_META,
  ModelConfig,
  RENDERER_MODEL_META,
  MCPServerConfig
} from '@shared/presenter'
import { SearchEngineTemplate } from '@shared/chat'
import ElectronStore from 'electron-store'
import { DEFAULT_PROVIDERS } from './providers'
import path from 'path'
import { app, nativeTheme, shell } from 'electron'
import fs from 'fs'
import { CONFIG_EVENTS, SYSTEM_EVENTS } from '@/events'
import { McpConfHelper, SYSTEM_INMEM_MCP_SERVERS } from './mcpConfHelper'
import { presenter } from '@/presenter'
import { compare } from 'compare-versions'
import { defaultShortcutKey, ShortcutKeySetting } from './shortcutKeySettings'
import { defaultModelsSettings } from './modelDefaultSettings'
import { getProviderSpecificModelConfig } from './providerModelSettings'

// Define interface for application settings
interface IAppSettings {
  // Define your configuration items here, for example:
  language: string
  providers: LLM_PROVIDER[]
  closeToQuit: boolean // Whether to quit the app when the close button is clicked
  appVersion?: string // For version checking and data migration
  proxyMode?: string // Proxy mode: system, none, custom
  customProxyUrl?: string // Custom proxy URL
  customShortKey?: ShortcutKeySetting // Custom shortcut key
  artifactsEffectEnabled?: boolean // Whether artifacts animation effects are enabled
  searchPreviewEnabled?: boolean // Whether search preview is enabled
  contentProtectionEnabled?: boolean // Whether content protection is enabled
  syncEnabled?: boolean // Whether sync is enabled
  syncFolderPath?: string // Sync folder path
  lastSyncTime?: number // Last sync time
  customSearchEngines?: string // Custom search engines JSON string
  loggingEnabled?: boolean // Whether logging is enabled
  default_system_prompt?: string // Default system prompt
  [key: string]: unknown // Allow arbitrary keys, use unknown type instead of any
}

// Create interface for model storage
interface IModelStore {
  models: MODEL_META[]
  custom_models: MODEL_META[]
}

// Add prompts-related type definitions
interface Prompt {
  id: string
  name: string
  description: string
  content: string
  parameters?: Array<{
    name: string
    description: string
    required: boolean
  }>
}

const defaultProviders = DEFAULT_PROVIDERS.map((provider) => ({
  id: provider.id,
  name: provider.name,
  apiType: provider.apiType,
  apiKey: provider.apiKey,
  baseUrl: provider.baseUrl,
  enable: provider.enable,
  websites: provider.websites
}))

// Define storeKey constant
const PROVIDERS_STORE_KEY = 'providers'

const PROVIDER_MODELS_DIR = 'provider_models'
// Model status key prefix
const MODEL_STATUS_KEY_PREFIX = 'model_status_'

export class ConfigPresenter implements IConfigPresenter {
  private store: ElectronStore<IAppSettings>
  private providersModelStores: Map<string, ElectronStore<IModelStore>> = new Map()
  private customPromptsStore: ElectronStore<{ prompts: Prompt[] }>
  private userDataPath: string
  private currentAppVersion: string
  private mcpConfHelper: McpConfHelper // Use MCP configuration helper

  constructor() {
    this.userDataPath = app.getPath('userData')
    this.currentAppVersion = app.getVersion()
    // Initialize application settings store
    this.store = new ElectronStore<IAppSettings>({
      name: 'app-settings',
      defaults: {
        language: 'en-US',
        providers: defaultProviders,
        closeToQuit: false,
        proxyMode: 'system',
        customProxyUrl: '',
        customShortKey: defaultShortcutKey,
        artifactsEffectEnabled: true,
        searchPreviewEnabled: true,
        contentProtectionEnabled: false,
        syncEnabled: false,
        syncFolderPath: path.join(this.userDataPath, 'sync'),
        lastSyncTime: 0,
        loggingEnabled: false,
        default_system_prompt: '',
        appVersion: this.currentAppVersion
      }
    })

    this.initTheme()

    // Initialize custom prompts store
    this.customPromptsStore = new ElectronStore<{ prompts: Prompt[] }>({
      name: 'custom_prompts',
      defaults: {
        prompts: []
      }
    })

    // Initialize MCP configuration helper
    this.mcpConfHelper = new McpConfHelper()

    // Initialize provider models directory
    this.initProviderModelsDir()

    // If the app version has been updated, update appVersion
    if (this.store.get('appVersion') !== this.currentAppVersion) {
      const oldVersion = this.store.get('appVersion')
      this.store.set('appVersion', this.currentAppVersion)
      // Migrate data
      this.migrateModelData(oldVersion)
      this.mcpConfHelper.onUpgrade(oldVersion)
    }

    const existingProviders = this.getSetting<LLM_PROVIDER[]>(PROVIDERS_STORE_KEY) || []
    const newProviders = defaultProviders.filter(
      (defaultProvider) =>
        !existingProviders.some((existingProvider) => existingProvider.id === defaultProvider.id)
    )

    if (newProviders.length > 0) {
      this.setProviders([...existingProviders, ...newProviders])
    }
  }

  private initProviderModelsDir(): void {
    const modelsDir = path.join(this.userDataPath, PROVIDER_MODELS_DIR)
    if (!fs.existsSync(modelsDir)) {
      fs.mkdirSync(modelsDir, { recursive: true })
    }
  }

  private getProviderModelStore(providerId: string): ElectronStore<IModelStore> {
    if (!this.providersModelStores.has(providerId)) {
      const store = new ElectronStore<IModelStore>({
        name: `models_${providerId}`,
        cwd: path.join(this.userDataPath, PROVIDER_MODELS_DIR),
        defaults: {
          models: [],
          custom_models: []
        }
      })
      this.providersModelStores.set(providerId, store)
    }
    return this.providersModelStores.get(providerId)!
  }

  private migrateModelData(oldVersion: string | undefined): void {
    // Before version 0.0.10, model data was stored in app-settings.json
    if (oldVersion && compare(oldVersion, '0.0.10', '<')) {
      // Migrate old model data
      const providers = this.getProviders()

      for (const provider of providers) {
        // Check and fix baseUrl for ollama
        if (provider.id === 'ollama' && provider.baseUrl) {
          if (provider.baseUrl.endsWith('/v1')) {
            provider.baseUrl = provider.baseUrl.replace(/\/v1$/, '')
            // Save the modified provider
            this.setProviderById('ollama', provider)
          }
        }

        // Migrate provider models
        const oldProviderModelsKey = `${provider.id}_models`
        const oldModels =
          this.getSetting<(MODEL_META & { enabled: boolean })[]>(oldProviderModelsKey)

        if (oldModels && oldModels.length > 0) {
          const store = this.getProviderModelStore(provider.id)
          // Traverse old models, save enabled status
          oldModels.forEach((model) => {
            if (model.enabled) {
              this.setModelStatus(provider.id, model.id, true)
            }
            // @ts-ignore - Delete enabled property to store status separately
            delete model.enabled
          })
          // Save model list to new storage
          store.set('models', oldModels)
          // Clear old storage
          this.store.delete(oldProviderModelsKey)
        }

        // Migrate custom models
        const oldCustomModelsKey = `custom_models_${provider.id}`
        const oldCustomModels =
          this.getSetting<(MODEL_META & { enabled: boolean })[]>(oldCustomModelsKey)

        if (oldCustomModels && oldCustomModels.length > 0) {
          const store = this.getProviderModelStore(provider.id)
          // Traverse old custom models, save enabled status
          oldCustomModels.forEach((model) => {
            if (model.enabled) {
              this.setModelStatus(provider.id, model.id, true)
            }
            // @ts-ignore - Delete enabled property to store status separately
            delete model.enabled
          })
          // Save custom model list to new storage
          store.set('custom_models', oldCustomModels)
          // Clear old storage
          this.store.delete(oldCustomModelsKey)
        }
      }
    }

    // Before version 0.0.17, qwenlm provider needs to be removed
    if (oldVersion && compare(oldVersion, '0.0.17', '<')) {
      // Get all current providers
      const providers = this.getProviders()

      // Filter out qwenlm provider
      const filteredProviders = providers.filter((provider) => provider.id !== 'qwenlm')

      // If the number of filtered providers is different, there is a removal operation, and the updated provider list needs to be saved
      if (filteredProviders.length !== providers.length) {
        this.setProviders(filteredProviders)
      }
    }
  }

  getSetting<T>(key: string): T | undefined {
    try {
      return this.store.get(key) as T
    } catch (error) {
      console.error(`[Config] Failed to get setting ${key}:`, error)
      return undefined
    }
  }

  setSetting<T>(key: string, value: T): void {
    try {
      this.store.set(key, value)
      // Trigger setting change event
      eventBus.emit(CONFIG_EVENTS.SETTING_CHANGED, key, value)
    } catch (error) {
      console.error(`[Config] Failed to set setting ${key}:`, error)
    }
  }

  getProviders(): LLM_PROVIDER[] {
    const providers = this.getSetting<LLM_PROVIDER[]>(PROVIDERS_STORE_KEY)
    if (Array.isArray(providers) && providers.length > 0) {
      return providers
    } else {
      this.setSetting(PROVIDERS_STORE_KEY, defaultProviders)
      return defaultProviders
    }
  }

  setProviders(providers: LLM_PROVIDER[]): void {
    this.setSetting<LLM_PROVIDER[]>(PROVIDERS_STORE_KEY, providers)
    // Trigger new event
    eventBus.emit(CONFIG_EVENTS.PROVIDER_CHANGED)
  }

  getProviderById(id: string): LLM_PROVIDER | undefined {
    const providers = this.getProviders()
    return providers.find((provider) => provider.id === id)
  }

  setProviderById(id: string, provider: LLM_PROVIDER): void {
    const providers = this.getProviders()
    const index = providers.findIndex((p) => p.id === id)
    if (index !== -1) {
      providers[index] = provider
      this.setProviders(providers)
    } else {
      console.error(`[Config] Provider ${id} not found`)
    }
  }

  // Construct the key for storing model status
  private getModelStatusKey(providerId: string, modelId: string): string {
    // Replace dots in modelId with hyphens
    const formattedModelId = modelId.replace(/\./g, '-')
    return `${MODEL_STATUS_KEY_PREFIX}${providerId}_${formattedModelId}`
  }

  // Get model enabled status
  getModelStatus(providerId: string, modelId: string): boolean {
    const statusKey = this.getModelStatusKey(providerId, modelId)
    const status = this.getSetting<boolean>(statusKey)
    // If the status is not a boolean, return true
    return typeof status === 'boolean' ? status : true
  }

  // Set model enabled status
  setModelStatus(providerId: string, modelId: string, enabled: boolean): void {
    const statusKey = this.getModelStatusKey(providerId, modelId)
    this.setSetting(statusKey, enabled)
    // Emit model status change event
    eventBus.emit(CONFIG_EVENTS.MODEL_STATUS_CHANGED, providerId, modelId, enabled)
  }

  // Enable model
  enableModel(providerId: string, modelId: string): void {
    this.setModelStatus(providerId, modelId, true)
  }

  // Disable model
  disableModel(providerId: string, modelId: string): void {
    this.setModelStatus(providerId, modelId, false)
  }

  // Batch set model status
  batchSetModelStatus(providerId: string, modelStatusMap: Record<string, boolean>): void {
    for (const [modelId, enabled] of Object.entries(modelStatusMap)) {
      this.setModelStatus(providerId, modelId, enabled)
    }
  }

  getProviderModels(providerId: string): MODEL_META[] {
    const store = this.getProviderModelStore(providerId)
    let models = store.get('models') || []

    models = models.map((model) => {
      const config = this.getModelConfig(model.id, providerId)
      if (config) {
        model.maxTokens = config.maxTokens
        model.contextLength = config.contextLength
        // If the model already has these properties, retain them, otherwise use the values from the config or default to false
        model.vision = model.vision !== undefined ? model.vision : config.vision || false
        model.functionCall =
          model.functionCall !== undefined ? model.functionCall : config.functionCall || false
        model.reasoning =
          model.reasoning !== undefined ? model.reasoning : config.reasoning || false
      } else {
        // Ensure the model has these properties, if not, default to false
        model.vision = model.vision || false
        model.functionCall = model.functionCall || false
        model.reasoning = model.reasoning || false
      }
      return model
    })
    return models
  }

  getModelDefaultConfig(modelId: string, providerId?: string): ModelConfig {
    const model = this.getModelConfig(modelId, providerId)
    if (model) {
      return model
    }
    return {
      maxTokens: 4096,
      contextLength: 4096,
      temperature: 0.7,
      vision: false,
      functionCall: false,
      reasoning: false
    }
  }

  setProviderModels(providerId: string, models: MODEL_META[]): void {
    const store = this.getProviderModelStore(providerId)
    store.set('models', models)
  }

  getEnabledProviders(): LLM_PROVIDER[] {
    const providers = this.getProviders()
    return providers.filter((provider) => provider.enable)
  }

  getAllEnabledModels(): Promise<{ providerId: string; models: RENDERER_MODEL_META[] }[]> {
    const enabledProviders = this.getEnabledProviders()
    return Promise.all(
      enabledProviders.map(async (provider) => {
        const providerId = provider.id
        const allModels = [
          ...this.getProviderModels(providerId),
          ...this.getCustomModels(providerId)
        ]

        // Filter enabled models based on separately stored status
        const enabledModels = allModels
          .filter((model) => this.getModelStatus(providerId, model.id))
          .map((model) => ({
            ...model,
            enabled: true,
            // Ensure ability properties are copied
            vision: model.vision || false,
            functionCall: model.functionCall || false,
            reasoning: model.reasoning || false
          }))

        return {
          providerId,
          models: enabledModels
        }
      })
    )
  }

  getCustomModels(providerId: string): MODEL_META[] {
    const store = this.getProviderModelStore(providerId)
    let customModels = store.get('custom_models') || []

    // Ensure custom models also have ability properties
    customModels = customModels.map((model) => {
      // If the model already has these properties, retain them, otherwise default to false
      model.vision = model.vision !== undefined ? model.vision : false
      model.functionCall = model.functionCall !== undefined ? model.functionCall : false
      model.reasoning = model.reasoning !== undefined ? model.reasoning : false
      return model
    })

    return customModels
  }

  setCustomModels(providerId: string, models: MODEL_META[]): void {
    const store = this.getProviderModelStore(providerId)
    store.set('custom_models', models)
  }

  addCustomModel(providerId: string, model: MODEL_META): void {
    const models = this.getCustomModels(providerId)
    const existingIndex = models.findIndex((m) => m.id === model.id)

    // Create a copy of the model without the enabled property
    const modelWithoutStatus: MODEL_META = { ...model }
    // @ts-ignore - Delete enabled property to store status separately
    delete modelWithoutStatus.enabled

    if (existingIndex !== -1) {
      models[existingIndex] = modelWithoutStatus as MODEL_META
    } else {
      models.push(modelWithoutStatus as MODEL_META)
    }

    this.setCustomModels(providerId, models)
    // Set model status separately
    this.setModelStatus(providerId, model.id, true)
    // Trigger model list change event
    eventBus.emit(CONFIG_EVENTS.MODEL_LIST_CHANGED, providerId)
  }

  removeCustomModel(providerId: string, modelId: string): void {
    const models = this.getCustomModels(providerId)
    const filteredModels = models.filter((model) => model.id !== modelId)
    this.setCustomModels(providerId, filteredModels)

    // Remove model status
    const statusKey = this.getModelStatusKey(providerId, modelId)
    this.store.delete(statusKey)

    // Trigger model list change event
    eventBus.emit(CONFIG_EVENTS.MODEL_LIST_CHANGED, providerId)
  }

  updateCustomModel(providerId: string, modelId: string, updates: Partial<MODEL_META>): void {
    const models = this.getCustomModels(providerId)
    const index = models.findIndex((model) => model.id === modelId)

    if (index !== -1) {
      Object.assign(models[index], updates)
      this.setCustomModels(providerId, models)
      eventBus.emit(CONFIG_EVENTS.MODEL_LIST_CHANGED, providerId)
    }
  }

  getCloseToQuit(): boolean {
    return this.getSetting<boolean>('closeToQuit') ?? false
  }

  setCloseToQuit(value: boolean): void {
    this.setSetting('closeToQuit', value)
  }

  // Get the current language of the app, considering system language settings
  getLanguage(): string {
    const language = this.getSetting<string>('language') || 'system'

    if (language !== 'system') {
      return language
    }

    return this.getSystemLanguage()
  }

  // Set app language
  setLanguage(language: string): void {
    this.setSetting('language', language)
    // Trigger language change event
    eventBus.emit(CONFIG_EVENTS.LANGUAGE_CHANGED, language)
  }

  // Get system language and match supported language list
  private getSystemLanguage(): string {
    const systemLang = app.getLocale()
    const supportedLanguages = [
      'zh-CN',
      'zh-TW',
      'en-US',
      'zh-HK',
      'ko-KR',
      'ru-RU',
      'ja-JP',
      'fr-FR',
      'en-GB',
      'en-NZ',
      'mi-NZ',
      'es-ES',
      'de-DE'
    ]

    // Full match
    if (supportedLanguages.includes(systemLang)) {
      return systemLang
    }

    // Partial match (only match language code)
    const langCode = systemLang.split('-')[0]
    const matchedLang = supportedLanguages.find((lang) => lang.startsWith(langCode))
    if (matchedLang) {
      return matchedLang
    }

    // Default to English
    return 'en-US'
  }

  public getDefaultProviders(): LLM_PROVIDER[] {
    return DEFAULT_PROVIDERS
  }

  // Get proxy mode
  getProxyMode(): string {
    return this.getSetting<string>('proxyMode') || 'system'
  }

  // Set proxy mode
  setProxyMode(mode: string): void {
    this.setSetting('proxyMode', mode)
    eventBus.emit(CONFIG_EVENTS.PROXY_MODE_CHANGED, mode)
  }

  // Get custom proxy address
  getCustomProxyUrl(): string {
    return this.getSetting<string>('customProxyUrl') || ''
  }

  // Set custom proxy URL
  setCustomProxyUrl(url: string): void {
    this.setSetting('customProxyUrl', url)
    eventBus.emit(CONFIG_EVENTS.CUSTOM_PROXY_URL_CHANGED, url)
  }

  getArtifactsEffectEnabled(): boolean {
    const value = this.getSetting<boolean>('artifactsEffectEnabled')
    console.log('getArtifactsEffectEnabled original value:', value, 'type:', typeof value)
    // Only use default value true when value is undefined or null
    // Note: false is a valid boolean value, should be preserved instead of replaced with default value
    return value === undefined || value === null ? true : value
  }

  setArtifactsEffectEnabled(enabled: boolean): void {
    console.log('ConfigPresenter.setArtifactsEffectEnabled:', enabled, typeof enabled)

    // Ensure enabled is a boolean
    const boolValue = Boolean(enabled)

    this.setSetting('artifactsEffectEnabled', boolValue)
    eventBus.emit(CONFIG_EVENTS.ARTIFACTS_EFFECT_CHANGED, boolValue)
  }

  // Get sync enabled status
  getSyncEnabled(): boolean {
    return this.getSetting<boolean>('syncEnabled') || false
  }

  // Get logging folder path
  getLoggingFolderPath(): string {
    return path.join(this.userDataPath, 'logs')
  }

  // Open logging folder
  async openLoggingFolder(): Promise<void> {
    const loggingFolderPath = this.getLoggingFolderPath()

    // If the folder does not exist, create it first
    if (!fs.existsSync(loggingFolderPath)) {
      fs.mkdirSync(loggingFolderPath, { recursive: true })
    }

    // Open folder
    await shell.openPath(loggingFolderPath)
  }

  // Set sync enabled status
  setSyncEnabled(enabled: boolean): void {
    console.log('setSyncEnabled', enabled)
    this.setSetting('syncEnabled', enabled)
    eventBus.emit(CONFIG_EVENTS.SYNC_SETTINGS_CHANGED, { enabled })
  }

  // Get sync folder path
  getSyncFolderPath(): string {
    return (
      this.getSetting<string>('syncFolderPath') || path.join(app.getPath('home'), 'docomoeSync')
    )
  }

  // Set sync folder path
  setSyncFolderPath(folderPath: string): void {
    this.setSetting('syncFolderPath', folderPath)
    eventBus.emit(CONFIG_EVENTS.SYNC_SETTINGS_CHANGED, { folderPath })
  }

  // Get last sync time
  getLastSyncTime(): number {
    return this.getSetting<number>('lastSyncTime') || 0
  }

  // Set last sync time
  setLastSyncTime(time: number): void {
    this.setSetting('lastSyncTime', time)
  }

  // Get custom search engines
  async getCustomSearchEngines(): Promise<SearchEngineTemplate[]> {
    try {
      const customEnginesJson = this.store.get('customSearchEngines')
      if (customEnginesJson) {
        return JSON.parse(customEnginesJson as string)
      }
      return []
    } catch (error) {
      console.error('Failed to get custom search engines:', error)
      return []
    }
  }

  // Set custom search engines
  async setCustomSearchEngines(engines: SearchEngineTemplate[]): Promise<void> {
    try {
      this.store.set('customSearchEngines', JSON.stringify(engines))
      // Send event to notify search engine update
      eventBus.emit(CONFIG_EVENTS.SEARCH_ENGINES_UPDATED, engines)
    } catch (error) {
      console.error('Failed to set custom search engines:', error)
      throw error
    }
  }

  // Get search preview enabled status
  getSearchPreviewEnabled(): Promise<boolean> {
    const value = this.getSetting<boolean>('searchPreviewEnabled')
    // Default to false if value is undefined or null
    return Promise.resolve(value === undefined || value === null ? false : value)
  }

  // Set search preview enabled status
  setSearchPreviewEnabled(enabled: boolean): void {
    console.log('ConfigPresenter.setSearchPreviewEnabled:', enabled, typeof enabled)

    // Ensure enabled is a boolean
    const boolValue = Boolean(enabled)

    this.setSetting('searchPreviewEnabled', boolValue)
  }

  // Get content protection enabled status
  getContentProtectionEnabled(): boolean {
    const value = this.getSetting<boolean>('contentProtectionEnabled')
    // Default to false if value is undefined or null
    return value === undefined || value === null ? false : value
  }

  // Set content protection enabled status
  setContentProtectionEnabled(enabled: boolean): void {
    this.setSetting('contentProtectionEnabled', enabled)
    eventBus.emit(CONFIG_EVENTS.CONTENT_PROTECTION_CHANGED, enabled)
  }

  getLoggingEnabled(): boolean {
    return this.getSetting<boolean>('loggingEnabled') ?? false
  }

  setLoggingEnabled(enabled: boolean): void {
    this.setSetting('loggingEnabled', enabled)
    setTimeout(() => {
      presenter.devicePresenter.restartApp()
    }, 1000)
  }

  // ===================== MCP configuration related methods =====================

  // Get MCP server configuration
  async getMcpServers(): Promise<Record<string, MCPServerConfig>> {
    const servers = await this.mcpConfHelper.getMcpServers()

    // Check if there are custom prompts, if so, add custom-prompts-server
    try {
      const customPrompts = await this.getCustomPrompts()
      if (customPrompts && customPrompts.length > 0) {
        const customPromptsServerName = 'docomoe-inmemory/custom-prompts-server'
        const systemServers = SYSTEM_INMEM_MCP_SERVERS[customPromptsServerName]

        if (systemServers && !servers[customPromptsServerName]) {
          servers[customPromptsServerName] = systemServers
          servers[customPromptsServerName].disable = false
          servers[customPromptsServerName].autoApprove = ['all']
        }
      }
    } catch {
      // Error checking custom prompts
    }

    return servers
  }

  // Set MCP server configuration
  async setMcpServers(servers: Record<string, MCPServerConfig>): Promise<void> {
    return this.mcpConfHelper.setMcpServers(servers)
  }

  // Get default MCP servers
  getMcpDefaultServers(): Promise<string[]> {
    return this.mcpConfHelper.getMcpDefaultServers()
  }

  // Add default MCP server
  async addMcpDefaultServer(serverName: string): Promise<void> {
    return this.mcpConfHelper.addMcpDefaultServer(serverName)
  }

  async removeMcpDefaultServer(serverName: string): Promise<void> {
    return this.mcpConfHelper.removeMcpDefaultServer(serverName)
  }

  async toggleMcpDefaultServer(serverName: string): Promise<void> {
    return this.mcpConfHelper.toggleMcpDefaultServer(serverName)
  }

  // Get MCP enabled status
  getMcpEnabled(): Promise<boolean> {
    return this.mcpConfHelper.getMcpEnabled()
  }

  // Set MCP enabled status
  async setMcpEnabled(enabled: boolean): Promise<void> {
    return this.mcpConfHelper.setMcpEnabled(enabled)
  }

  // Add MCP server
  async addMcpServer(name: string, config: MCPServerConfig): Promise<boolean> {
    return this.mcpConfHelper.addMcpServer(name, config)
  }

  // Remove MCP server
  async removeMcpServer(name: string): Promise<void> {
    return this.mcpConfHelper.removeMcpServer(name)
  }

  // Update MCP server configuration
  async updateMcpServer(name: string, config: Partial<MCPServerConfig>): Promise<void> {
    await this.mcpConfHelper.updateMcpServer(name, config)
  }

  // Provide getMcpConfHelper method to get MCP configuration helper
  getMcpConfHelper(): McpConfHelper {
    return this.mcpConfHelper
  }

  /**
   * Get the recommended configuration for a specific provider and model
   * @param modelId Model ID
   * @param providerId Optional provider ID, if provided, look for specific configuration first
   * @returns ModelConfig Model configuration
   */
  getModelConfig(modelId: string, providerId?: string): ModelConfig {
    // If providerId is provided, try to find specific configuration first
    if (providerId) {
      const providerConfig = getProviderSpecificModelConfig(providerId, modelId)
      if (providerConfig) {
        // console.log('providerConfig Matched', providerId, modelId)
        return providerConfig
      }
    }

    // If no specific configuration is found, or providerId is not provided, look for generic configuration
    // Convert modelId to lowercase for case-insensitive matching
    const lowerModelId = modelId.toLowerCase()

    // Check if any of the matching conditions are met
    for (const config of defaultModelsSettings) {
      if (config.match.some((matchStr) => lowerModelId.includes(matchStr.toLowerCase()))) {
        return {
          maxTokens: config.maxTokens,
          contextLength: config.contextLength,
          temperature: config.temperature,
          vision: config.vision,
          functionCall: config.functionCall || false,
          reasoning: config.reasoning || false
        }
      }
    }

    // If no matching configuration is found, return default safe configuration
    return {
      maxTokens: 4096,
      contextLength: 8192,
      temperature: 0.6,
      vision: false,
      functionCall: false,
      reasoning: false
    }
  }

  getNotificationsEnabled(): boolean {
    const value = this.getSetting<boolean>('notificationsEnabled')
    if (value === undefined) {
      return true
    } else {
      return value
    }
  }

  setNotificationsEnabled(enabled: boolean): void {
    this.setSetting('notificationsEnabled', enabled)
  }

  async initTheme() {
    const theme = this.getSetting<string>('appTheme')
    if (theme) {
      nativeTheme.themeSource = theme as 'dark' | 'light'
    }
    // Listen for system theme changes
    nativeTheme.on('updated', () => {
      // Only notify the renderer process when the theme is set to system
      if (nativeTheme.themeSource === 'system') {
        eventBus.emit(SYSTEM_EVENTS.SYSTEM_THEME_UPDATED, nativeTheme.shouldUseDarkColors)
      }
    })
  }

  async toggleTheme(theme: 'dark' | 'light' | 'system'): Promise<boolean> {
    nativeTheme.themeSource = theme
    this.setSetting('appTheme', theme)
    return nativeTheme.shouldUseDarkColors
  }

  async getTheme(): Promise<string> {
    return this.getSetting<string>('appTheme') || 'system'
  }

  async getSystemTheme(): Promise<'dark' | 'light'> {
    return nativeTheme.shouldUseDarkColors ? 'dark' : 'light'
  }

  // Get all custom prompts
  async getCustomPrompts(): Promise<Prompt[]> {
    try {
      return this.customPromptsStore.get('prompts') || []
    } catch {
      return []
    }
  }

  // Save custom prompts
  async setCustomPrompts(prompts: Prompt[]): Promise<void> {
    await this.customPromptsStore.set('prompts', prompts)
    // Emit custom prompt change event
    eventBus.emit(CONFIG_EVENTS.CUSTOM_PROMPTS_CHANGED)
    // Notify MCP system to check and start/stop custom prompt server
    eventBus.emit(CONFIG_EVENTS.CUSTOM_PROMPTS_SERVER_CHECK_REQUIRED)
  }

  // Add a single prompt
  async addCustomPrompt(prompt: Prompt): Promise<void> {
    const prompts = await this.getCustomPrompts()
    prompts.push(prompt)
    await this.setCustomPrompts(prompts)
    // The event will be triggered in setCustomPrompts
  }

  // Update a single prompt
  async updateCustomPrompt(promptId: string, updates: Partial<Prompt>): Promise<void> {
    const prompts = await this.getCustomPrompts()
    const index = prompts.findIndex((p) => p.id === promptId)
    if (index !== -1) {
      prompts[index] = { ...prompts[index], ...updates }
      await this.setCustomPrompts(prompts)
      // The event will be triggered in setCustomPrompts
    }
  }

  // Delete a single prompt
  async deleteCustomPrompt(promptId: string): Promise<void> {
    const prompts = await this.getCustomPrompts()
    const filteredPrompts = prompts.filter((p) => p.id !== promptId)
    await this.setCustomPrompts(filteredPrompts)
    // The event will be triggered in setCustomPrompts
  }

  // Get default system prompt
  async getDefaultSystemPrompt(): Promise<string> {
    return this.getSetting<string>('default_system_prompt') || ''
  }

  // Set default system prompt
  async setDefaultSystemPrompt(prompt: string): Promise<void> {
    this.setSetting('default_system_prompt', prompt)
  }
  
  // 获取默认快捷键
  getDefaultShortcutKey(): ShortcutKeySetting {
    return {
      ...defaultShortcutKey
    }
  }

  // 获取快捷键
  getShortcutKey(): ShortcutKeySetting {
    return (
      this.getSetting<ShortcutKeySetting>('shortcutKey') || {
        ...defaultShortcutKey
      }
    )
  }

  // 设置快捷键
  setShortcutKey(customShortcutKey: ShortcutKeySetting) {
    this.setSetting('shortcutKey', customShortcutKey)
  }

  // 重置快捷键
  resetShortcutKeys() {
    this.setSetting('shortcutKey', {...defaultShortcutKey})
  }
}

// Export configuration-related content for other components to use
export { defaultModelsSettings } from './modelDefaultSettings'
export { providerModelSettings } from './providerModelSettings'
export { defaultShortcutKey } from './shortcutKeySettings'