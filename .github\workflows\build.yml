name: Build Application

on:
  workflow_dispatch:
    inputs:
      platform:
        description: 'Select build platform'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - windows
          - linux

jobs:
  build-windows:
    if: github.event.inputs.platform == 'all' || contains(github.event.inputs.platform, 'windows')
    runs-on: windows-latest
    strategy:
      matrix:
        arch: [x64, arm64]
        include:
          - arch: x64
            platform: win-x64
          - arch: arm64
            platform: win-arm64
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Install dependencies
        run: pnpm install
        env:
          npm_config_platform: win32
          npm_config_arch: ${{ matrix.arch }}
      - name: Install Node Runtime
        run: pnpm run installRuntime:win:${{ matrix.arch }}
      - name: Install Sharp
        run: pnpm install sharp --build-from-source
      - name: Build Windows
        run: pnpm run build:win:${{ matrix.arch }}
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: docomoe-${{ matrix.platform }}
          path: |
            dist/*
            !dist/win-unpacked
            !dist/win-arm64-unpacked

  build-linux:
    if: github.event.inputs.platform == 'all' || contains(github.event.inputs.platform, 'linux')
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        arch: [x64]
        include:
          - arch: x64
            platform: linux-x64
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Install dependencies
        run: pnpm install
        env:
          npm_config_platform: linux
          npm_config_arch: ${{ matrix.arch }}
      - name: Install Node Runtime
        run: pnpm run installRuntime:linux:${{ matrix.arch }}
      - name: Install Sharp
        run: pnpm install --cpu=wasm32 sharp
      - name: Build Linux
        run: pnpm run build:linux:${{ matrix.arch }}
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: docomoe-${{ matrix.platform }}
          path: |
            dist/*
            !dist/linux-unpacked
