name: Create Release

on:
  workflow_dispatch:
    inputs:
      workflow_id:
        description: 'Build workflow run ID to use for artifacts'
        required: true
        type: string
      prerelease:
        description: 'Is this a prerelease?'
        required: true
        type: boolean
        default: false

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts from workflow
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow_conclusion: success
          run_id: ${{ github.event.inputs.workflow_id }}
          path: artifacts

      - name: List downloaded artifacts
        run: find artifacts -type f | sort

      - name: Get version number
        id: get_version
        run: |
          VERSION_FILE=$(find artifacts/docomoe-linux-x64 -name "Docomoe-*.tar.gz" | head -n 1)
          if [ -n "$VERSION_FILE" ]; then
            VERSION=$(echo $VERSION_FILE | grep -o 'Docomoe-[0-9]\+\.[0-9]\+\.[0-9]\+' | sed 's/Docomoe-//')
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "Found version: $VERSION"
          else
            echo "Error: Docomoe tar.gz file not found"
            exit 1
          fi

      - name: Prepare release assets
        run: |
          mkdir -p release_assets

          # Process Windows x64 artifacts
          if [ -d "artifacts/docomoe-win-x64" ]; then
            cp artifacts/docomoe-win-x64/*.exe release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-win-x64/*.msi release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-win-x64/*.zip release_assets/ 2>/dev/null || true
          fi

          # Process Windows arm64 artifacts
          if [ -d "artifacts/docomoe-win-arm64" ]; then
            cp artifacts/docomoe-win-arm64/*.exe release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-win-arm64/*.msi release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-win-arm64/*.zip release_assets/ 2>/dev/null || true
          fi

          # Process Linux x64 artifacts
          if [ -d "artifacts/docomoe-linux-x64" ]; then
            cp artifacts/docomoe-linux-x64/*.AppImage release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-linux-x64/*.deb release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-linux-x64/*.rpm release_assets/ 2>/dev/null || true
            cp artifacts/docomoe-linux-x64/*.tar.gz release_assets/ 2>/dev/null || true
          fi

          ls -la release_assets/

      - name: Create Draft Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: workflow-${{ github.event.inputs.workflow_id }}
          name: Docomoe V${{ steps.get_version.outputs.version }}
          draft: true
          prerelease: ${{ github.event.inputs.prerelease }}
          files: |
            release_assets/*
          body: |
            🚀 Docomoe ${{ steps.get_version.outputs.version }} Official Release | Redefining Your AI Conversation Experience!

            📦 **Build Information**
            - Workflow ID: `${{ github.event.inputs.workflow_id }}`
            - Version: `${{ steps.get_version.outputs.version }}`
            - Prerelease: `${{ github.event.inputs.prerelease }}`
            - Build Time: `${{ github.run_id }}`

            🔥 Why Choose Docomoe?

            ✅ **Business Friendly**: Open source based on original [Apache License 2.0](https://github.com/Calmren/docomoe/blob/main/LICENSE), with no additional constraints beyond the license, oriented towards open source.
            ✅ **Ready to Use**: Minimal configuration, start your intelligent conversation journey instantly.
            ✅ **Ultimate Flexibility**: Freely switch models, customize model sources, meeting your diverse conversation and exploration needs.
            ✅ **Superior Experience**: LaTeX formula rendering, code highlighting, Markdown support, model conversations have never been smoother.
            ✅ **Continuous Evolution**: We listen to user feedback, constantly iterate and update to bring you an even better AI conversation experience.

            📥 Experience the Future Now

            💬 Feedback Welcome: Share your valuable suggestions, join our VIP user community, and help shape the future of Docomoe with us!
            <img width="400px" src="https://github.com/user-attachments/assets/2ebc21e8-3eef-4a11-b3ab-de28e8f9d9c0"/>
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
