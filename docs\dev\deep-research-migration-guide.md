# Deep Research Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from the monolithic `deepResearchServer.ts` to the new modular architecture while maintaining backward compatibility and ensuring smooth transition.

## Migration Timeline

### Phase 1: Advanced Crawler Server (Week 1)
- Extract crawler functionality
- Create standalone server
- Parallel testing with existing system

### Phase 2: Content Analysis Server (Week 2)
- Extract content processing logic
- Implement multiple extraction strategies
- Integration testing

### Phase 3: Research Template Server (Week 3)
- Build template engine
- Integrate with customPromptsServer
- Create default templates

### Phase 4: Deprecation & Cleanup (Week 4)
- Update workflows to use new servers
- Deprecate old server
- Performance optimization

## Detailed Migration Steps

### Phase 1: Advanced Crawler Server

#### Step 1.1: Extract Crawler Logic

**From**: `deepResearchServer.ts` lines 254-522 (WebCrawler class)
**To**: `advancedCrawlerServer.ts`

**Key Changes**:
```typescript
// OLD: Embedded in DeepResearchServer
class WebCrawler {
  constructor(private params: CrawlParams) {}
  // ... crawler logic
}

// NEW: Standalone MCP Server
export class AdvancedCrawlerServer {
  private server: Server
  
  constructor(env?: Record<string, unknown>) {
    this.server = new Server({
      name: 'docomoe-inmemory/advanced-crawler-server',
      version: '1.0.0'
    }, {
      capabilities: { tools: {} }
    })
  }
}
```

#### Step 1.2: Update Tool Interface

**OLD Interface** (embedded in deep_research_tool):
```typescript
// Crawler params were part of larger schema
crawl_max_depth: z.number().optional().default(1)
crawl_max_breadth: z.number().optional().default(10)
// ... 15+ crawler-specific parameters
```

**NEW Interface** (dedicated tools):
```typescript
// Focused crawler tools
{
  name: 'crawl_website',
  description: 'Crawl website with advanced filtering',
  inputSchema: zodToJsonSchema(CrawlWebsiteArgsSchema)
},
{
  name: 'batch_crawl_urls', 
  description: 'Crawl multiple URLs concurrently',
  inputSchema: zodToJsonSchema(BatchCrawlArgsSchema)
}
```

#### Step 1.3: Add to Builder

```typescript
// src/main/presenter/mcpPresenter/inMemoryServers/builder.ts
import { AdvancedCrawlerServer } from './advancedCrawlerServer'

export function getInMemoryServer(serverName: string, args: string[], env?: Record<string, unknown>) {
  switch (serverName) {
    // ... existing cases
    case 'advancedCrawler':
      return new AdvancedCrawlerServer(env)
    case 'docomoe-inmemory/advanced-crawler-server':
      return new AdvancedCrawlerServer(env)
    // ... rest
  }
}
```

### Phase 2: Content Analysis Server

#### Step 2.1: Extract Content Processing

**From**: `deepResearchServer.ts` lines 365-421 (extractMainContent, cleanText methods)
**To**: `contentAnalysisServer.ts`

**Enhancement Strategy**:
```typescript
// OLD: Single extraction strategy
private extractMainContent($: cheerio.CheerioAPI): string {
  // Basic semantic + heuristic approach
}

// NEW: Multiple strategies
interface ContentExtractionStrategy {
  extract(html: string, options: ExtractionOptions): ExtractedContent
}

class SemanticExtractor implements ContentExtractionStrategy {
  extract(html: string, options: ExtractionOptions): ExtractedContent {
    // Focus on semantic HTML5 elements
  }
}

class HeuristicExtractor implements ContentExtractionStrategy {
  extract(html: string, options: ExtractionOptions): ExtractedContent {
    // Content density and pattern analysis
  }
}
```

#### Step 2.2: Enhance Content Analysis

**New Capabilities**:
- Metadata extraction (title, description, keywords, author)
- Citation and reference detection
- Content quality scoring
- Language detection
- Reading time estimation

### Phase 3: Research Template Server

#### Step 3.1: Template System Design

**Integration with customPromptsServer**:
```typescript
// Leverage existing template infrastructure
import { presenter } from '@/presenter'

class ResearchTemplateEngine {
  async getTemplate(name: string): Promise<TemplateDefinition | undefined> {
    return presenter.customPromptsPresenter?.getTemplateByName?.(name)
  }
  
  async executeTemplate(templateName: string, params: Record<string, any>): Promise<ResearchResult> {
    const template = await this.getTemplate(templateName)
    if (!template) throw new Error(`Template not found: ${templateName}`)
    
    // Execute research steps
    const results = await this.executeResearchSteps(template.steps, params)
    
    // Generate final output using template
    return this.generateOutput(template.outputTemplate, results, params)
  }
}
```

#### Step 3.2: Default Research Templates

**Create research-specific templates**:
```typescript
const RESEARCH_TEMPLATES = [
  {
    name: 'academic_research',
    description: 'Comprehensive academic research workflow',
    content: `
# Academic Research: {{query}}

## Research Methodology
1. Multi-source web search
2. Deep content crawling
3. Citation analysis
4. Content quality assessment

## Search Strategy
- Primary search: {{search_results.primary.count}} results
- Secondary search: {{search_results.secondary.count}} results
- Crawled pages: {{crawl_results.total_pages}}

## Key Findings
{{#each findings}}
- **{{title}}** ({{source}})
  {{summary}}
{{/each}}

## Sources and Citations
{{#each citations}}
- {{citation}}
{{/each}}
    `,
    parameters: [
      { name: 'query', type: 'string', required: true },
      { name: 'depth', type: 'number', default: 2 },
      { name: 'academic_focus', type: 'boolean', default: true }
    ]
  }
]
```

### Phase 4: Workflow Migration

#### Step 4.1: Update Existing Workflows

**OLD Workflow** (single tool call):
```typescript
// Single monolithic call
const result = await mcpClient.callTool('deep_research_tool', {
  query: 'AI safety research',
  search_depth: 'advanced',
  max_search_results: 10,
  crawl_max_depth: 2,
  crawl_max_breadth: 10,
  include_search_images: false
  // ... 20+ more parameters
})
```

**NEW Workflow** (composable tools):
```typescript
// Template-driven approach
const result = await mcpClient.callTool('apply_research_template', {
  templateName: 'academic_research',
  query: 'AI safety research',
  parameters: {
    depth: 2,
    academic_focus: true
  }
})

// OR manual composition
const searchResults = await mcpClient.callTool('bocha_web_search', {
  query: 'AI safety research',
  count: 10
})

const crawlResults = await mcpClient.callTool('crawl_website', {
  url: searchResults.topResult.url,
  maxDepth: 2,
  selectDomains: ['edu', 'org']
})

const analysis = await mcpClient.callTool('analyze_content_structure', {
  url: crawlResults.pages[0].url,
  extractCitations: true,
  extractMetadata: true
})
```

## Backward Compatibility Strategy

### Transition Period (4 weeks)

#### Week 1-2: Parallel Operation
- Keep `deepResearchServer.ts` active
- Deploy new servers alongside
- A/B testing with selected workflows

#### Week 3: Gradual Migration
- Update 50% of workflows to use new servers
- Monitor performance and reliability
- Fix any integration issues

#### Week 4: Full Migration
- Update remaining workflows
- Deprecate `deepResearchServer.ts`
- Remove old server from default configuration

### Compatibility Wrapper

**Create a compatibility layer** for existing integrations:
```typescript
// src/main/presenter/mcpPresenter/inMemoryServers/deepResearchCompatServer.ts
export class DeepResearchCompatServer {
  constructor(
    private crawlerServer: AdvancedCrawlerServer,
    private analysisServer: ContentAnalysisServer,
    private templateServer: ResearchTemplateServer
  ) {}
  
  async handleLegacyRequest(args: LegacyDeepResearchArgs): Promise<LegacyResponse> {
    // Map old parameters to new tool calls
    const searchResults = await this.bochaSearch(args.query, args.max_search_results)
    const crawlResults = await this.crawlerServer.crawl({
      urls: searchResults.urls,
      maxDepth: args.crawl_max_depth,
      maxBreadth: args.crawl_max_breadth
      // ... map other parameters
    })
    
    // Return in legacy format
    return this.formatLegacyResponse(searchResults, crawlResults)
  }
}
```

## Testing Strategy

### Phase 1 Testing
- Unit tests for crawler functionality
- Performance comparison with old crawler
- Memory usage analysis
- Rate limiting effectiveness

### Phase 2 Testing
- Content extraction accuracy tests
- Strategy comparison (semantic vs heuristic vs hybrid)
- Batch processing performance
- Error handling scenarios

### Phase 3 Testing
- Template execution engine
- Parameter injection and validation
- Integration with customPromptsServer
- Multi-step workflow reliability

### Integration Testing
- End-to-end research workflows
- Cross-server communication
- Error propagation and handling
- Performance under concurrent load

## Rollback Plan

### If Issues Arise
1. **Immediate**: Switch traffic back to `deepResearchServer.ts`
2. **Short-term**: Fix issues in new servers
3. **Medium-term**: Gradual re-migration with fixes
4. **Long-term**: Complete transition with lessons learned

### Monitoring Points
- Response times for each server
- Error rates and types
- Memory usage patterns
- User satisfaction metrics

## Success Metrics

### Performance Metrics
- **Response Time**: ≤ current performance
- **Memory Usage**: ≤ 80% of current usage
- **Error Rate**: ≤ 2% of requests
- **Throughput**: ≥ current capacity

### Quality Metrics
- **Content Accuracy**: ≥ 95% relevant content
- **Template Flexibility**: Support for 5+ research types
- **Tool Reusability**: Each tool used in multiple contexts
- **Developer Satisfaction**: Easier maintenance and extension

This migration guide ensures a smooth transition from the monolithic deep research server to the new modular architecture while maintaining system reliability and user experience.
