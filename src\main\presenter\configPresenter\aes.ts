import crypto from 'crypto'

const ALGORITHM = 'aes-256-cbc'
const KEY_LENGTH = 32 // 256 bits
const IV_LENGTH = 16 // 128 bits
const SALT_LENGTH = 64
const ITERATIONS = 10000
const DIGEST = 'sha512'

export class AESHelper {
  /**
   * Generate a random salt
   * @returns Salt value in hexadecimal format
   */
  static generateSalt(): string {
    return crypto.randomBytes(SALT_LENGTH).toString('hex')
  }

  /**
   * Derive a key from a password and salt
   * @param password The original password
   * @param salt The salt value
   * @returns The derived key as a Buffer
   */
  static deriveKey(password: string, salt: string): Buffer {
    return crypto.pbkdf2Sync(password, salt, ITERATIONS, KEY_LENGTH, DIGEST)
  }

  /**
   * Encrypt a string using AES-256-CBC
   * @param plainText The plain text to encrypt
   * @param key The encryption key (Buffer format)
   * @param iv The initialization vector (optional)
   * @returns An object containing the encrypted text and IV
   */
  static encrypt(
    plainText: string,
    key: <PERSON>uffer,
    iv?: Buffer
  ): {
    cipherText: string
    iv: string
  } {
    try {
      const usedIv = iv || crypto.randomBytes(IV_LENGTH)
      const cipher = crypto.createCipheriv(ALGORITHM, key, usedIv)

      let encrypted = cipher.update(plainText, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      return {
        cipherText: encrypted,
        iv: usedIv.toString('hex')
      }
    } catch (error) {
      throw new Error(
        `Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Decrypt a string using AES-256-CBC
   * @param cipherText The encrypted text (hex format)
   * @param key The decryption key (Buffer format)
   * @param iv The initialization vector (hex format)
   * @returns The decrypted text
   */
  static decrypt(cipherText: string, key: Buffer, iv: string): string {
    try {
      const ivBuffer = Buffer.from(iv, 'hex')
      const decipher = crypto.createDecipheriv(ALGORITHM, key, ivBuffer)

      let decrypted = decipher.update(cipherText, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      throw new Error(
        `Decryption failed: ${error instanceof Error ? error.message : 'The ciphertext may have been tampered with or the key is incorrect'}`
      )
    }
  }

  /**
   * Generate a random initialization vector (IV) in hexadecimal format
   * @returns The IV as a string in hexadecimal format
   */
  static generateIV(): string {
    return crypto.randomBytes(IV_LENGTH).toString('hex')
  }
}
