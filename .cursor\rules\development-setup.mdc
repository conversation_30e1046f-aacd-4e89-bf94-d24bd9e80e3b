---
description:
globs:
alwaysApply: false
---
# Development Environment Setup Guide

## Environment Requirements
- Node.js 18+
- pnpm 10+
- Git

## Development Tools
- VS Code (recommended)
- Vue DevTools
- Electron DevTools

## Development Environment Setup Steps

1. Install dependencies:
```bash
pnpm install
```

2. Start in development mode:
```bash
pnpm run dev
```

3. Build the application:
```bash
pnpm run build
```

## Development Guidelines

1. Code Style
- Use ESLint for code checks
- Use Prettier for code formatting
- Follow the rules in [.eslintrc.cjs](mdc:.eslintrc.cjs)
- Follow the formatting rules in [.prettierrc.yaml](mdc:.prettierrc.yaml)

2. Git Commit Guidelines
- Follow the commit guidelines in [CONTRIBUTING.md](mdc:CONTRIBUTING.md)
- Use semantic commit messages

3. Testing Guidelines
- Write unit tests
- Run tests: `pnpm run test`

4. Documentation Guidelines
- Update related documentation
- Keep the README file up to date
