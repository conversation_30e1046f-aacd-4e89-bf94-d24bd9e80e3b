import { app, protocol } from 'electron'
import { electronApp, optimizer } from '@electron-toolkit/utils'
import { presenter } from './presenter'
import { ProxyMode, proxyConfig } from './presenter/proxyConfig'
import path from 'path'
import fs from 'fs'
import { eventBus } from './eventbus'
import { WINDOW_EVENTS, TRAY_EVENTS } from './events'
import { setLoggingEnabled } from '@shared/logger'
import { TrayPresenter } from './presenter/trayPresenter'

app.commandLine.appendSwitch('autoplay-policy', 'no-user-gesture-required')
app.commandLine.appendSwitch('webrtc-max-cpu-consumption-percentage', '100')
app.commandLine.appendSwitch('js-flags', '--max-old-space-size=4096')
app.commandLine.appendSwitch('ignore-certificate-errors')

if (process.platform == 'win32') {
  // app.commandLine.appendSwitch('in-process-gpu')
  // app.commandLine.appendSwitch('wm-window-animations-disabled')
}
if (process.platform === 'darwin') {
  app.commandLine.appendSwitch('disable-features', 'DesktopCaptureMacV2,IOSurfaceCapturer')
}

// Initialize DeepLink handling
presenter.deeplinkPresenter.init()

// Initialize tray
const trayPresenter = new TrayPresenter()

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.calmren.docomoe')
  // Read log settings from config
  const loggingEnabled = presenter.configPresenter.getLoggingEnabled()
  setLoggingEnabled(loggingEnabled)

  console.log('app ready')

  // Initialize tray
  trayPresenter.init()

  // Read proxy settings from config and initialize
  const proxyMode = presenter.configPresenter.getProxyMode() as ProxyMode
  const customProxyUrl = presenter.configPresenter.getCustomProxyUrl()
  proxyConfig.initFromConfig(proxyMode as ProxyMode, customProxyUrl)

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    const allWindows = presenter.windowPresenter.getAllWindows()
    if (allWindows.length === 0) {
      presenter.windowPresenter.createShellWindow({
        initialTab: {
          url: 'local://chat'
        }
      })
    } else {
      allWindows[0].show()
    }
  })

  // Create main window
  presenter.windowPresenter.createShellWindow({
    initialTab: {
      url: 'local://chat'
    }
  })
  presenter.shortcutPresenter.registerShortcuts()

  // Listen to show window event
  eventBus.on(TRAY_EVENTS.SHOW_HIDDEN_WINDOW, (trayClick: boolean) => {
    if (presenter.windowPresenter.windows.size === 0) {
      presenter.windowPresenter.createShellWindow({
        initialTab: {
          url: 'local://chat'
        }
      })
    } else {
      presenter.windowPresenter.mainWindow?.isVisible() && !trayClick
        ? presenter.windowPresenter.mainWindow?.hide()
        : presenter.windowPresenter.mainWindow?.show()
    }
  })
  // Listen to application focus event
  app.on('browser-window-focus', () => {
    presenter.shortcutPresenter.registerShortcuts()
    eventBus.emit(WINDOW_EVENTS.APP_FOCUS)
  })

  // Listen to application loss of focus event
  app.on('browser-window-blur', () => {
    // Check if all windows have lost focus
    const allWindows = presenter.windowPresenter.getAllWindows()
    const isAnyWindowFocused = allWindows.some((win) => !win.isDestroyed() && win.isFocused())

    if (!isAnyWindowFocused) {
      presenter.shortcutPresenter.unregisterShortcuts()
      eventBus.emit(WINDOW_EVENTS.APP_BLUR)
    }
  })

  protocol.handle('deepcdn', (request) => {
    try {
      const filePath = request.url.slice('deepcdn://'.length)
      const fullPath = path.join(app.getAppPath(), 'resources', 'cdn', filePath)
      // Determine MIME type based on file extension
      let mimeType = 'application/octet-stream'
      if (filePath.endsWith('.js')) {
        mimeType = 'text/javascript'
      } else if (filePath.endsWith('.css')) {
        mimeType = 'text/css'
      }

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        return new Response(`File not found: ${filePath}`, {
          status: 404,
          headers: { 'Content-Type': 'text/plain' }
        })
      }

      // Read file and return response
      const fileContent = fs.readFileSync(fullPath)
      return new Response(fileContent, {
        headers: { 'Content-Type': mimeType }
      })
    } catch (error: unknown) {
      console.error('Error handling deepcdn request:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      return new Response(`Server error: ${errorMessage}`, {
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      })
    }
  })

  // Register imgcache protocol to handle image cache
  protocol.handle('imgcache', (request) => {
    try {
      const filePath = request.url.slice('imgcache://'.length)
      const fullPath = path.join(app.getPath('userData'), 'images', filePath)

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        return new Response(`Image not found: ${filePath}`, {
          status: 404,
          headers: { 'Content-Type': 'text/plain' }
        })
      }

      // Determine MIME type based on file extension
      let mimeType = 'image/jpeg' // Default MIME type
      if (filePath.endsWith('.png')) {
        mimeType = 'image/png'
      } else if (filePath.endsWith('.gif')) {
        mimeType = 'image/gif'
      } else if (filePath.endsWith('.webp')) {
        mimeType = 'image/webp'
      } else if (filePath.endsWith('.svg')) {
        mimeType = 'image/svg+xml'
      }

      // Read file and return response
      const fileContent = fs.readFileSync(fullPath)
      return new Response(fileContent, {
        headers: { 'Content-Type': mimeType }
      })
    } catch (error: unknown) {
      console.error('Error handling imgcache request:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      return new Response(`Server error: ${errorMessage}`, {
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      })
    }
  })
})
// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  presenter.destroy()
  trayPresenter.destroy()
})

app.on('before-quit', () => {
  presenter.destroy()
  trayPresenter.destroy()
})
