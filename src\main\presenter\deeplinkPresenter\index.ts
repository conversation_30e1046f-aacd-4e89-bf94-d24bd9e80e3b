import { app } from 'electron'
import { presenter } from '@/presenter'
import { IDeeplinkPresenter, MCPServerConfig } from '@shared/presenter'
import path from 'path'
import { DEEPLINK_EVENTS, MCP_EVENTS, WINDOW_EVENTS } from '@/events'
import { eventBus } from '@/eventbus'

interface MCPInstallConfig {
  mcpServers: Record<
    string,
    {
      command?: string
      args?: string[]
      env?: Record<string, string> | string
      descriptions?: string
      icons?: string
      autoApprove?: string[]
      disable?: boolean
      url?: string
      type?: 'sse' | 'stdio' | 'http'
    }
  >
}

/**
 * DeepLink handler class
 * Responsible for handling docomoe:// protocol links
 * docomoe://start Launch app and go to default new session interface
 * docomoe://start?msg=你好 Launch app and go to new session interface, with default message
 * docomoe://start?msg=你好&model=deepseek-chat Launch app and go to new session interface, with default message, model first performs a complete match, selects the first one that matches. If there is no match, it performs a fuzzy match, only the first one that contains this field is returned, if none, ignore the default
 * docomoe://mcp/install?json=base64JSONData Install mcp via json data
 */
export class DeeplinkPresenter implements IDeeplinkPresenter {
  private startupUrl: string | null = null
  private pendingMcpInstallUrl: string | null = null

  init(): void {
    // Register protocol handler
    if (process.defaultApp) {
      if (process.argv.length >= 2) {
        app.setAsDefaultProtocolClient('docomoe', process.execPath, [path.resolve(process.argv[1])])
      }
    } else {
      app.setAsDefaultProtocolClient('docomoe')
    }

    // Handle macOS protocol call
    app.on('open-url', (event, url) => {
      event.preventDefault()
      if (!app.isReady()) {
        console.log('App not ready yet, saving URL:', url)
        this.startupUrl = url
      } else {
        console.log('App is ready, checking URL:', url)
        this.processDeepLink(url)
      }
    })

    // Listen for window content loaded event
    eventBus.once(WINDOW_EVENTS.FIRST_CONTENT_LOADED, () => {
      console.log('Window content loaded. Processing DeepLink if exists.')
      if (this.startupUrl) {
        console.log('Processing startup URL:', this.startupUrl)
        this.processDeepLink(this.startupUrl)
        this.startupUrl = null
      }
    })

    // Listen for MCP initialization completion event
    eventBus.on(MCP_EVENTS.INITIALIZED, () => {
      console.log('MCP initialized. Processing pending MCP install if exists.')
      if (this.pendingMcpInstallUrl) {
        console.log('Processing pending MCP install URL:', this.pendingMcpInstallUrl)
        this.handleDeepLink(this.pendingMcpInstallUrl)
        this.pendingMcpInstallUrl = null
      }
    })

    // Handle Windows protocol call
    const gotTheLock = app.requestSingleInstanceLock()
    if (!gotTheLock) {
      app.quit()
    } else {
      app.on('second-instance', (_event, commandLine) => {
        // User attempts to run a second instance, we should focus on our window
        if (presenter.windowPresenter.mainWindow) {
          if (presenter.windowPresenter.mainWindow.isMinimized()) {
            presenter.windowPresenter.mainWindow.restore()
          }
          presenter.windowPresenter.mainWindow.show()
          presenter.windowPresenter.mainWindow.focus()
        }
        if (process.platform === 'win32') {
          // On Windows, command line arguments contain protocol URL
          const deepLinkUrl = commandLine.find((arg) => arg.startsWith('docomoe://'))
          if (deepLinkUrl) {
            if (!app.isReady()) {
              console.log('Windows: App not ready yet, saving URL:', deepLinkUrl)
              this.startupUrl = deepLinkUrl
            } else {
              console.log('Windows: App is ready, checking URL:', deepLinkUrl)
              this.processDeepLink(deepLinkUrl)
            }
          }
        }
      })
    }
  }

  // New: method to handle DeepLink, determine how to handle based on URL type and system status
  private processDeepLink(url: string): void {
    try {
      const urlObj = new URL(url)
      const command = urlObj.hostname
      const subCommand = urlObj.pathname.slice(1)

      // If MCP installation command, wait for MCP initialization completion
      if (command === 'mcp' && subCommand === 'install') {
        if (!presenter.mcpPresenter.isReady()) {
          console.log('MCP not ready yet, saving MCP install URL for later')
          this.pendingMcpInstallUrl = url
          return
        }
      }

      // Other types of DeepLink or MCP initialized completion, directly handle
      this.handleDeepLink(url)
    } catch (error) {
      console.error('Error processing DeepLink:', error)
    }
  }

  async handleDeepLink(url: string): Promise<void> {
    console.log('Received DeepLink:', url)

    try {
      const urlObj = new URL(url)

      if (urlObj.protocol !== 'docomoe:') {
        console.error('Unsupported protocol:', urlObj.protocol)
        return
      }

      // Get command from hostname
      const command = urlObj.hostname

      // Handle different commands
      if (command === 'start') {
        await this.handleStart(urlObj.searchParams)
      } else if (command === 'mcp') {
        // Handle mcp/install command
        const subCommand = urlObj.pathname.slice(1) // Remove leading slash
        if (subCommand === 'install') {
          await this.handleMcpInstall(urlObj.searchParams)
        } else {
          console.warn('Unknown MCP subcommand:', subCommand)
        }
      } else {
        console.warn('Unknown DeepLink command:', command)
      }
    } catch (error) {
      console.error('Error processing DeepLink:', error)
    }
  }

  async handleStart(params: URLSearchParams): Promise<void> {
    console.log('Processing start command, parameters:', Object.fromEntries(params.entries()))

    let msg = params.get('msg')
    if (!msg) {
      return
    }
    msg = decodeURIComponent(msg)
    // If model parameter, try to set
    let modelId = params.get('model')
    if (modelId && modelId.trim() !== '') {
      modelId = decodeURIComponent(modelId)
    }
    let systemPrompt = params.get('system')
    if (systemPrompt && systemPrompt.trim() !== '') {
      systemPrompt = decodeURIComponent(systemPrompt)
    } else {
      systemPrompt = ''
    }
    // If user added yolo=1 or yolo=true, automatically send message
    const yolo = params.get('yolo')
    const autoSend = yolo && yolo.trim() !== ''
    console.log('msg:', msg)
    console.log('modelId:', modelId)
    console.log('systemPrompt:', systemPrompt)
    console.log('autoSend:', autoSend)
    eventBus.emit(DEEPLINK_EVENTS.START, { msg, modelId, systemPrompt, autoSend })
  }

  async handleMcpInstall(params: URLSearchParams): Promise<void> {
    console.log('Processing mcp/install command, parameters:', Object.fromEntries(params.entries()))

    // Get JSON data
    const jsonBase64 = params.get('code')
    if (!jsonBase64) {
      console.error("Missing 'code' parameter")
      return
    }

    try {
      // Decode Base64 and parse JSON
      const jsonString = Buffer.from(jsonBase64, 'base64').toString('utf-8')

      const mcpConfig = JSON.parse(jsonString) as MCPInstallConfig

      // Check MCP configuration validity
      if (!mcpConfig || !mcpConfig.mcpServers) {
        console.error('Invalid MCP configuration: missing mcpServers field')
        return
      }

      // Traverse and install all MCP servers
      for (const [serverName, serverConfig] of Object.entries(mcpConfig.mcpServers)) {
        let determinedType: 'sse' | 'stdio' | null = null
        const determinedCommand: string | undefined = serverConfig.command
        const determinedUrl: string | undefined = serverConfig.url

        // 1. Check explicit type
        if (serverConfig.type) {
          if (serverConfig.type === 'stdio' || serverConfig.type === 'sse') {
            determinedType = serverConfig.type
            // Validate required fields based on explicit type
            if (determinedType === 'stdio' && !determinedCommand) {
              console.error(
                `Server ${serverName} is type 'stdio' but missing required 'command' field`
              )
              continue
            }
            if (determinedType === 'sse' && !determinedUrl) {
              console.error(`Server ${serverName} is type 'sse' but missing required 'url' field`)
              continue
            }
          } else {
            console.error(
              `Server ${serverName} provided invalid 'type' value: ${serverConfig.type}, should be 'stdio' or 'sse'`
            )
            continue
          }
        } else {
          // 2. Infer type if not provided
          const hasCommand = !!determinedCommand && determinedCommand.trim() !== ''
          const hasUrl = !!determinedUrl && determinedUrl.trim() !== ''

          if (hasCommand && hasUrl) {
            console.error(
              `Server ${serverName} provides both 'command' and 'url' fields, but 'type' is not specified. Please explicitly set 'type' to 'stdio' or 'sse'.`
            )
            continue
          } else if (hasCommand) {
            determinedType = 'stdio'
          } else if (hasUrl) {
            determinedType = 'sse'
          } else {
            console.error(
              `Server ${serverName} must provide either 'command' (for stdio) or 'url' (for sse) field`
            )
            continue
          }
        }

        // Safeguard check (should not be reached if logic is correct)
        if (!determinedType) {
          console.error(`Cannot determine server ${serverName} type ('stdio' or 'sse')`)
          continue
        }

        // Set default values based on determined type
        const defaultConfig: Partial<MCPServerConfig> = {
          env: {},
          descriptions: `${serverName} MCP Service`,
          icons: determinedType === 'stdio' ? '🔌' : '🌐', // Different default icons
          autoApprove: ['all'],
          disable: false,
          args: [],
          baseUrl: '',
          command: '',
          type: determinedType
        }

        // Merge configuration
        const finalConfig: MCPServerConfig = {
          env: {
            ...(typeof defaultConfig.env === 'string'
              ? JSON.parse(defaultConfig.env)
              : defaultConfig.env),
            ...(typeof serverConfig.env === 'string'
              ? JSON.parse(serverConfig.env)
              : serverConfig.env)
          },
          // env: { ...defaultConfig.env, ...serverConfig.env },
          descriptions: serverConfig.descriptions || defaultConfig.descriptions!,
          icons: serverConfig.icons || defaultConfig.icons!,
          autoApprove: serverConfig.autoApprove || defaultConfig.autoApprove!,
          disable: serverConfig.disable ?? defaultConfig.disable!,
          args: serverConfig.args || defaultConfig.args!,
          type: determinedType, // Use the determined type
          // Set command or baseUrl based on type, prioritizing provided values
          command: determinedType === 'stdio' ? determinedCommand! : defaultConfig.command!,
          baseUrl: determinedType === 'sse' ? determinedUrl! : defaultConfig.baseUrl!
        }

        // Install MCP server
        console.log(
          `Preparing to install MCP server: ${serverName} (type: ${determinedType})`,
          finalConfig
        )
        const resultServerConfig = {
          mcpServers: {
            [serverName]: finalConfig
          }
        }
        // If the server is specified as the default server in the configuration, add it to the default server list
        eventBus.emit(DEEPLINK_EVENTS.MCP_INSTALL, {
          mcpConfig: JSON.stringify(resultServerConfig)
        })
      }
      console.log('All MCP servers processing completed')
    } catch (error) {
      console.error('Error parsing or processing MCP configuration:', error)
    }
  }
}
