/**
 * Event system constant definitions
 *
 * Categorize event names by functional domain and follow a unified naming convention:
 * - Use colons to separate domain and specific event
 * - Use lowercase with hyphenation for multiple words
 *
 * It seems to duplicate renderer/events.ts, but it's actually different as it only includes events from main to renderer and main to main
 */

// Configuration related events
export const CONFIG_EVENTS = {
  PROVIDER_CHANGED: 'config:provider-changed', // Replace provider-setting-changed
  SYSTEM_CHANGED: 'config:system-changed',
  MODEL_LIST_CHANGED: 'config:model-list-changed', // Replace provider-models-updated（ConfigPresenter）
  MODEL_STATUS_CHANGED: 'config:model-status-changed', // Replace model-status-changed（ConfigPresenter）
  SETTING_CHANGED: 'config:setting-changed', // Replace setting-changed（ConfigPresenter）
  PROXY_MODE_CHANGED: 'config:proxy-mode-changed',
  CUSTOM_PROXY_URL_CHANGED: 'config:custom-proxy-url-changed',
  ARTIFACTS_EFFECT_CHANGED: 'config:artifacts-effect-changed',
  SYNC_SETTINGS_CHANGED: 'config:sync-settings-changed',
  SEARCH_ENGINES_UPDATED: 'config:search-engines-updated',
  CONTENT_PROTECTION_CHANGED: 'config:content-protection-changed',
  PROXY_RESOLVED: 'config:proxy-resolved',
  LANGUAGE_CHANGED: 'config:language-changed',
  CUSTOM_PROMPTS_CHANGED: 'config:custom-prompts-changed', // New: Custom prompts change event
  CUSTOM_PROMPTS_SERVER_CHECK_REQUIRED: 'config:custom-prompts-server-check-required' // New: Need to check custom prompts server event
}

// Conversation-related events
export const CONVERSATION_EVENTS = {
  CREATED: 'conversation:created',
  ACTIVATED: 'conversation:activated', // Replace conversation-activated
  DEACTIVATED: 'conversation:deactivated', // Replace active-conversation-cleared
  MESSAGE_EDITED: 'conversation:message-edited' // Replace message-edited
}

// Communication-related events
export const STREAM_EVENTS = {
  RESPONSE: 'stream:response', // Replace stream-response
  END: 'stream:end', // Replace stream-end
  ERROR: 'stream:error' // Replace stream-error
}

// System-related events
export const SYSTEM_EVENTS = {
  SYSTEM_THEME_UPDATED: 'system:theme-updated'
}

// Application update-related events
export const UPDATE_EVENTS = {
  STATUS_CHANGED: 'update:status-changed', // Replace update-status-changed
  ERROR: 'update:error', // Replace update-error
  PROGRESS: 'update:progress', // Download progress
  WILL_RESTART: 'update:will-restart' // Ready to restart
}

// Window-related events
export const WINDOW_EVENTS = {
  READY_TO_SHOW: 'window:ready-to-show', // Replace main-window-ready-to-show
  FORCE_QUIT_APP: 'window:force-quit-app', // Replace force-quit-app
  APP_FOCUS: 'app:focus',
  APP_BLUR: 'app:blur',
  WINDOW_MAXIMIZED: 'window:maximized',
  WINDOW_UNMAXIMIZED: 'window:unmaximized',
  WINDOW_RESIZED: 'window:resized',
  WINDOW_RESIZE: 'window:resize',
  WINDOW_CLOSE: 'window:close',
  WINDOW_CREATED: 'window:created',
  WINDOW_FOCUSED: 'window:focused',
  WINDOW_BLURRED: 'window:blurred',
  WINDOW_ENTER_FULL_SCREEN: 'window:enter-full-screen',
  WINDOW_LEAVE_FULL_SCREEN: 'window:leave-full-screen',
  WINDOW_CLOSED: 'window:closed',
  FIRST_CONTENT_LOADED: 'window:first-content-loaded' // New: First content loaded event
}

// ollama-related events
export const OLLAMA_EVENTS = {
  PULL_MODEL_PROGRESS: 'ollama:pull-model-progress'
}

// MCP-related events
export const MCP_EVENTS = {
  SERVER_STARTED: 'mcp:server-started',
  SERVER_STOPPED: 'mcp:server-stopped',
  CONFIG_CHANGED: 'mcp:config-changed',
  TOOL_CALL_RESULT: 'mcp:tool-call-result',
  SERVER_STATUS_CHANGED: 'mcp:server-status-changed',
  CLIENT_LIST_UPDATED: 'mcp:client-list-updated',
  INITIALIZED: 'mcp:initialized' // New: MCP initialization completed event
}

// Sync-related events
export const SYNC_EVENTS = {
  BACKUP_STARTED: 'sync:backup-started',
  BACKUP_COMPLETED: 'sync:backup-completed',
  BACKUP_ERROR: 'sync:backup-error',
  IMPORT_STARTED: 'sync:import-started',
  IMPORT_COMPLETED: 'sync:import-completed',
  IMPORT_ERROR: 'sync:import-error',
  DATA_CHANGED: 'sync:data-changed'
}

// DeepLink-related events
export const DEEPLINK_EVENTS = {
  PROTOCOL_RECEIVED: 'deeplink:protocol-received',
  START: 'deeplink:start',
  MCP_INSTALL: 'deeplink:mcp-install'
}

// Global notification related events
export const NOTIFICATION_EVENTS = {
  SHOW_ERROR: 'notification:show-error', // Show error notification
  SYS_NOTIFY_CLICKED: 'notification:sys-notify-clicked' // System notification click event
}

export const SHORTCUT_EVENTS = {
  ZOOM_IN: 'shortcut:zoom-in',
  ZOOM_OUT: 'shortcut:zoom-out',
  ZOOM_RESUME: 'shortcut:zoom-resume',
  CREATE_NEW_WINDOW: 'shortcut:create-new-window',
  CREATE_NEW_CONVERSATION: 'shortcut:create-new-conversation',
  CREATE_NEW_TAB: 'shortcut:create-new-tab',
  CLOSE_CURRENT_TAB: 'shortcut:close-current-tab',
  GO_SETTINGS: 'shortcut:go-settings',
  CLEAN_CHAT_HISTORY: 'shortcut:clean-chat-history',
  SWITCH_TO_NEXT_TAB: 'shortcut:switch-to-next-tab',
  SWITCH_TO_PREVIOUS_TAB: 'shortcut:switch-to-previous-tab',
  SWITCH_TO_SPECIFIC_TAB: 'shortcut:switch-to-specific-tab',
  SWITCH_TO_LAST_TAB: 'shortcut:switch-to-last-tab'
}

// Tab-related events
export const TAB_EVENTS = {
  TITLE_UPDATED: 'tab:title-updated', // Tab title update
  CONTENT_UPDATED: 'tab:content-updated', // Tab content update
  STATE_CHANGED: 'tab:state-changed', // Tab state change
  VISIBILITY_CHANGED: 'tab:visibility-changed' // Tab visibility change
}

// Tray-related events
export const TRAY_EVENTS = {
  SHOW_HIDDEN_WINDOW: 'tray:show-hidden-window'// Show window from tray
}
