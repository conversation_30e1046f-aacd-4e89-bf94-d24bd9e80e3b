import { ModelConfig } from '@shared/presenter'

// Define the model matching rules and configuration interface for each provider, consistent with modelDefaultSettings style
export interface ProviderModelSetting {
  id: string // Model ID
  name: string // Model name
  match: string[] // Array of strings used to match model ID
  maxTokens: number // Maximum number of generated tokens
  contextLength: number // Context length
  temperature?: number // Temperature parameter
  vision?: boolean // Whether to support vision
  functionCall?: boolean // Whether to support function call
  reasoning?: boolean // Whether to support reasoning ability
}

// Create a mapping object for each provider, use models array to wrap model configuration
export const providerModelSettings: Record<string, { models: ProviderModelSetting[] }> = {
  // OpenAI provider specific model configuration
  openai: {
    models: []
  },

  // Fireworks AI (Doubao) provider specific model configuration
  doubao: {
    models: []
  },

  // Anthropic provider specific model configuration
  anthropic: {
    models: [
      {
        id: 'claude-opus-4',
        name: '<PERSON> Opus 4',
        temperature: 0.7,
        maxTokens: 32000,
        contextLength: 204800,
        match: ['claude-opus-4', 'claude-opus-4-20250514'],
        vision: true,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'claude-sonnet-4',
        name: '<PERSON> Sonnet 4',
        temperature: 0.7,
        maxTokens: 64000,
        contextLength: 204800,
        match: ['claude-sonnet-4', 'claude-sonnet-4-20250514'],
        vision: true,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'claude-3-7-sonnet',
        name: 'Claude 3.7 Sonnet',
        temperature: 1,
        maxTokens: 64000,
        contextLength: 204800,
        match: ['claude-3-7-sonnet', 'claude-3.7-sonnet'],
        vision: true,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'claude-3-5-sonnet',
        name: 'Claude 3.5 Sonnet',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 204800,
        match: ['claude-3-5-sonnet', 'claude-3.5-sonnet'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        temperature: 0.7,
        maxTokens: 4096,
        contextLength: 204800,
        match: ['claude-3-opus', 'claude-3.opus'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'claude-3-haiku',
        name: 'Claude 3 Haiku',
        temperature: 0.7,
        maxTokens: 4096,
        contextLength: 204800,
        match: ['claude-3-haiku', 'claude-3.haiku', 'claude-3-5-haiku', 'claude-3.5-haiku'],
        vision: true,
        functionCall: true,
        reasoning: false
      }
    ]
  },

  // Gemini provider specific model configuration
  gemini: {
    models: [
      {
        id: 'models/gemini-2.5-flash-preview-04-17',
        name: 'Gemini 2.5 Flash Preview',
        temperature: 0.7,
        maxTokens: 65536,
        contextLength: 1048576,
        match: ['models/gemini-2.5-flash-preview-04-17', 'gemini-2.5-flash-preview-04-17'],
        vision: true,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'gemini-2.5-pro-preview-05-06',
        name: 'Gemini 2.5 Pro Preview 05-06',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 2048576,
        match: ['gemini-2.5-pro-preview'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-2.5-pro-exp-03-25',
        name: 'Gemini 2.5 Pro Exp 03-25',
        temperature: 0.7,
        maxTokens: 65536,
        contextLength: 2048576,
        match: ['gemini-2.5-pro-exp-03-25'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-2.0-flash-exp-image-generation',
        name: 'Gemini 2.0 Flash Exp Image Generation',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 1048576,
        match: ['gemini-2.0-flash-exp-image-generation'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-2.0-pro-exp-02-05',
        name: 'Gemini 2.0 Pro Exp 02-05',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 2048576,
        match: ['gemini-2.0-pro-exp-02-05'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-2.0-flash',
        name: 'Gemini 2.0 Flash',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 1048576,
        match: ['gemini-2.0-flash'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 1048576,
        match: ['gemini-1.5-flash'],
        vision: true,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 2097152,
        match: ['gemini-1.5-pro'],
        vision: true,
        functionCall: true,
        reasoning: false
      }
    ]
  },

  // Huawei Cloud Hunyuan provider specific model configuration
  hunyuan: {
    models: []
  },

  // DeepSeek provider specific model configuration
  deepseek: {
    models: [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek chat',
        temperature: 1,
        maxTokens: 8192,
        contextLength: 65536,
        match: ['deepseek-chat'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'deepseek-reasoner',
        name: 'DeepSeek Reasoner',
        temperature: 1,
        maxTokens: 32768,
        contextLength: 65536,
        match: ['deepseek-reasoner'],
        vision: false,
        functionCall: true,
        reasoning: true
      }
    ]
  },

  // MiniMax provider specific model configuration
  minimax: {
    models: []
  },

  // Zhipu AI provider specific model configuration
  zhipu: {
    models: []
  },

  // Moonshot provider specific model configuration
  moonshot: {
    models: []
  },

  // Ollama provider specific model configuration
  ollama: {
    models: [
      {
        id: 'qwen3:0.6b',
        name: 'Qwen3 0.6B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:0.6b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:1.7b',
        name: 'Qwen3 1.7B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:1.7b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:4b',
        name: 'Qwen3 4B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:4b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:8b',
        name: 'Qwen3 8B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:8b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:14b',
        name: 'Qwen3 14B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:14b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:32b',
        name: 'Qwen3 32B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:32b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:30b-a3b',
        name: 'Qwen3 30B A3B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:30b-a3b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'qwen3:235b-a22b',
        name: 'Qwen3 235B A22B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3:235b-a22b'],
        vision: false,
        functionCall: true,
        reasoning: true
      }
    ]
  },

  // Qiniu Cloud provider specific model configuration
  qiniu: {
    models: []
  },

  // Silicon Flow provider specific model configuration
  silicon: {
    models: [
      {
        id: 'Qwen/Qwen3-235B-A22B',
        name: 'Qwen/Qwen3-235B-A22B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 100_000,
        match: ['qwen3-235b-a22b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'Qwen/Qwen3-30B-A3B',
        name: 'Qwen/Qwen3-30B-A3B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 100_000,
        match: ['qwen3-30b-a3b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'Qwen/Qwen3-32B',
        name: 'Qwen/Qwen3-32B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 100_000,
        match: ['qwen3-32b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'Qwen/Qwen3-14B',
        name: 'Qwen/Qwen3-14B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 100_000,
        match: ['qwen3-14b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'Qwen/Qwen3-8B',
        name: 'Qwen/Qwen3-8B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 100_000,
        match: ['qwen3-8b'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'Pro/deepseek-ai/DeepSeek-V3',
        name: 'DeepSeek V3 Pro',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['pro/deepseek-ai/deepseek-v3'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'Pro/deepseek-ai/DeepSeek-R1',
        name: 'DeepSeek R1 Pro',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['pro/deepseek-ai/deepseek-r1'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek-ai/DeepSeek-V3',
        name: 'DeepSeek V3',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['deepseek-ai/deepseek-v3'],
        vision: false,
        functionCall: false,
        reasoning: false
      },
      {
        id: 'deepseek-ai/DeepSeek-R1',
        name: 'DeepSeek R1',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['deepseek-ai/deepseek-r1'],
        vision: false,
        functionCall: false,
        reasoning: true
      }
    ]
  },

  // Fireworks provider specific model configuration
  fireworks: {
    models: []
  },

  // PPIO provider specific model configuration
  ppio: {
    models: [
      {
        id: 'deepseek/deepseek-r1-0528',
        name: 'DeepSeek R1 0528',
        temperature: 0.6,
        maxTokens: 16000,
        contextLength: 128000,
        match: ['deepseek-r1-0528'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-qwen-32b',
        name: 'DeepSeek R1 Distill Qwen 32B',
        temperature: 0.7,
        maxTokens: 4000,
        contextLength: 32768,
        match: ['deepseek-r1-distill-qwen-32b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-qwen-14b',
        name: 'DeepSeek R1 Distill Qwen 14B',
        temperature: 0.7,
        maxTokens: 4000,
        contextLength: 32768,
        match: ['deepseek-r1-distill-qwen-14b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-qwen-7b',
        name: 'DeepSeek R1 Distill Qwen 7B',
        temperature: 0.7,
        maxTokens: 4000,
        contextLength: 32768,
        match: ['deepseek-r1-distill-qwen-7b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-qwen-1.5b',
        name: 'DeepSeek R1 Distill Qwen 1.5B',
        temperature: 0.6,
        maxTokens: 4000,
        contextLength: 65536,
        match: ['deepseek-r1-distill-qwen-1.5b', 'deepseek-r1-distill-qwen-1-5b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-llama-8b',
        name: 'DeepSeek R1 Distill Llama 8B',
        temperature: 0.6,
        maxTokens: 4000,
        contextLength: 65536,
        match: ['deepseek-r1-distill-llama-8b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-r1-distill-llama-70b',
        name: 'DeepSeek R1 Distill Llama 70B',
        temperature: 0.6,
        maxTokens: 4000,
        contextLength: 65536,
        match: ['deepseek-r1-distill-llama-70b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'qwen/qwen3-235b-a22b-fp8',
        name: 'Qwen/Qwen3-235B-A22B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3-235b-a22b-fp8', 'qwen3-235b-a22b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'qwen/qwen3-30b-a3b-fp8',
        name: 'Qwen/Qwen3-30B-A3B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3-30b-a3b-fp8', 'qwen3-30b-a3b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'qwen/qwen3-30b-a3b-fp8',
        name: 'Qwen/Qwen3-30B-A3B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3-30b-a3b-fp8', 'qwen3-30b-a3b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'qwen/qwen3-32b-fp8',
        name: 'Qwen/Qwen3-32B',
        temperature: 0.6,
        maxTokens: 8192,
        contextLength: 40960,
        match: ['qwen3-32b-fp8', 'qwen3-32b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-prover-v2-671b',
        name: 'Deepseek Prover V2 671B',
        temperature: 0.3,
        maxTokens: 10000,
        contextLength: 150000,
        match: ['deepseek-prover-v2-671b'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-v3-0324',
        name: 'DeepSeek Chat v3 0324',
        temperature: 0.6,
        maxTokens: 10000,
        contextLength: 110_000,
        match: ['deepseek-chat-v3-0324'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'deepseek/deepseek-r1-turbo',
        name: 'DeepSeek R1 Turbo',
        temperature: 0.6,
        maxTokens: 10000,
        contextLength: 50000,
        match: ['deepseek-r1-turbo'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-v3-turbo',
        name: 'DeepSeek V3 Turbo',
        temperature: 0.6,
        maxTokens: 10000,
        contextLength: 50000,
        match: ['deepseek-v3-turbo'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'deepseek/deepseek-v3/community',
        name: 'DeepSeek V3 Community',
        temperature: 0.6,
        maxTokens: 3200,
        contextLength: 62000,
        match: ['deepseek-v3/community'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'deepseek/deepseek-r1/community',
        name: 'DeepSeek R1 Community',
        temperature: 0.6,
        maxTokens: 3200,
        contextLength: 62000,
        match: ['deepseek-r1/community'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'deepseek/deepseek-v3',
        name: 'DeepSeek V3',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['deepseek-v3'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'deepseek-r1',
        name: 'DeepSeek R1',
        temperature: 0.6,
        maxTokens: 7000,
        contextLength: 62000,
        match: ['deepseek-r1'],
        vision: false,
        functionCall: true,
        reasoning: true
      }
    ]
  },

  // GitHub provider specific model configuration
  github: {
    models: []
  },

  // Aliyun provider specific model configuration, note matching order, only max/plus/turbo
  dashscope: {
    models: [
      {
        id: 'qwen-turbo-latest',
        name: 'Qwen Turbo Latest',
        temperature: 0.7,
        contextLength: 1000000,
        maxTokens: 16384,
        match: ['qwen-turbo-latest'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo-2024-11-01',
        name: 'Qwen Turbo 2024 11 01',
        temperature: 0.7,
        contextLength: 1000000,
        maxTokens: 8192,
        match: ['qwen-turbo-1101'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo-2024-09-19',
        name: 'Qwen Turbo 2024 09 19',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-turbo-0919'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo-2024-06-24',
        name: 'Qwen Turbo 2024 06 24',
        temperature: 0.7,
        contextLength: 8000,
        maxTokens: 2000,
        match: ['qwen-turbo-0624'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo-2025-04-28',
        name: 'Qwen Turbo 2025 04 28',
        temperature: 0.7,
        contextLength: 1000000,
        maxTokens: 16384,
        match: ['qwen-turbo-0428'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo-2025-02-11',
        name: 'Qwen Turbo 2025 02 11',
        temperature: 0.7,
        contextLength: 1000000,
        maxTokens: 8192,
        match: ['qwen-turbo-0211'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-turbo',
        name: 'Qwen Turbo',
        temperature: 0.7,
        contextLength: 1000000,
        maxTokens: 8192,
        match: ['qwen-turbo'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-latest',
        name: 'Qwen Plus Latest',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 16384,
        match: ['qwen-plus-latest'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-12-20',
        name: 'Qwen Plus 2024 12 20',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-1220'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-11-27',
        name: 'Qwen Plus 2024 11 27',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-1127'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-11-25',
        name: 'Qwen Plus 2024 11 25',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-1125'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-09-19',
        name: 'Qwen Plus 2024 09 19',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-0919'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-08-06',
        name: 'Qwen Plus 2024 08 06',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-0806'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2024-07-23',
        name: 'Qwen Plus 2024 07 23',
        temperature: 0.7,
        contextLength: 32000,
        maxTokens: 8000,
        match: ['qwen-plus-0723'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2025-04-28',
        name: 'Qwen Plus 2025 04 28',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 16384,
        match: ['qwen-plus-0428'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2025-01-25',
        name: 'Qwen Plus 2025 01 25',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-0125'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus-2025-01-12',
        name: 'Qwen Plus 2025 01 12',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus-0112'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-plus',
        name: 'Qwen Plus',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-plus'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max-latest',
        name: 'Qwen Max Latest',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-max-latest'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max-2024-09-19',
        name: 'Qwen Max 2024 09 19',
        temperature: 0.7,
        contextLength: 32768,
        maxTokens: 8192,
        match: ['qwen-max-0919'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max-2024-04-28',
        name: 'Qwen Max 2024 04 28',
        temperature: 0.7,
        contextLength: 8000,
        maxTokens: 2000,
        match: ['qwen-max-0428'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max-2024-04-03',
        name: 'Qwen Max 2024 04 03',
        temperature: 0.7,
        contextLength: 8000,
        maxTokens: 2000,
        match: ['qwen-max-0403'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max-2025-01-25',
        name: 'Qwen Max 2025 01 25',
        temperature: 0.7,
        contextLength: 131072,
        maxTokens: 8192,
        match: ['qwen-max-0125'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'qwen-max',
        name: 'Qwen Max',
        temperature: 0.7,
        contextLength: 32768,
        maxTokens: 8192,
        match: ['qwen-max'],
        vision: false,
        functionCall: true,
        reasoning: false
      }
    ]
  },

  // OpenRouter provider specific model configuration
  openrouter: {
    models: [
      {
        id: 'deepseek-r1-0528:free',
        name: 'DeepSeek R1-0528:free',
        temperature: 0.6,
        maxTokens: 65536,
        contextLength: 131072,
        match: ['deepseek/deepseek-r1-0528:free', 'deepseek/deepseek-r1-0528-qwen3-8b:free'],
        vision: false,
        functionCall: false,
        reasoning: true
      },
      {
        //has issues with function call support, avoid using
        id: 'deepseek-chat-v3-0324:free',
        name: 'DeepSeek v3-0324:free',
        temperature: 0.6,
        maxTokens: 65536,
        contextLength: 131072,
        match: ['deepseek/deepseek-chat-v3-0324:free', 'deepseek/deepseek-chat:free'],
        vision: false,
        functionCall: false,
        reasoning: false
      }
    ]
  },

  // Grok provider specific model configuration
  grok: {
    models: [
      {
        id: 'grok-3-mini-fast-beta',
        name: 'Grok 3 Mini Fast Beta',
        temperature: 1,
        contextLength: 120000,
        maxTokens: 100_000,
        match: ['grok-3-mini-fast', 'grok-3-mini-fast-latest', 'grok-3-mini-fast-beta'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'grok-3-mini-beta',
        name: 'Grok 3 Mini Beta',
        temperature: 1,
        contextLength: 120000,
        maxTokens: 100_000,
        match: ['grok-3-mini', 'grok-3-mini-latest', 'grok-3-mini-beta'],
        vision: false,
        functionCall: true,
        reasoning: true
      },
      {
        id: 'grok-3-fast-beta',
        name: 'Grok 3 Fast Beta',
        temperature: 0.7,
        contextLength: 120000,
        maxTokens: 100_000,
        match: ['grok-3-fast', 'grok-3-fast-latest', 'grok-3-fast-beta'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'grok-2-vision-1212',
        name: 'Grok 2 Vision 1212',
        temperature: 0.7,
        contextLength: 32000,
        maxTokens: 32000,
        match: ['grok-2-vision', 'grok-2-vision-latest', 'grok-2-vision-1212'],
        vision: true,
        functionCall: false,
        reasoning: false
      },
      {
        id: 'grok-2-image-1212',
        name: 'Grok 2 Image 1212',
        temperature: 0.7,
        contextLength: 130_000,
        maxTokens: 100_000,
        match: ['grok-2-image', 'grok-2-image-latest', 'grok-2-image-1212'],
        vision: true,
        functionCall: false,
        reasoning: false
      },
      {
        id: 'grok-3-beta',
        name: 'Grok 3 Beta',
        temperature: 0.7,
        contextLength: 120000,
        maxTokens: 100_000,
        match: ['grok-3', 'grok-3-latest', 'grok-3-beta'],
        vision: false,
        functionCall: true,
        reasoning: false
      },
      {
        id: 'grok-2-1212',
        name: 'Grok 2 1212',
        contextLength: 120000,
        temperature: 0.7,
        maxTokens: 100_000,
        match: ['grok-2', 'grok-2-latest', 'grok-2-1212'],
        vision: false,
        functionCall: true,
        reasoning: false
      }
    ]
  },

  // Azure OpenAI provider specific model configuration
  'azure-openai': {
    models: []
  },

  // LM Studio provider specific model configuration
  lmstudio: {
    models: []
  }
}

/**
 * Get the specific model configuration for a provider by provider ID and model ID
 * @param providerId Provider ID
 * @param modelId Model ID
 * @returns ModelConfig | undefined If a configuration is found, return it, otherwise return undefined
 */
export function getProviderSpecificModelConfig(
  providerId: string,
  modelId: string
): ModelConfig | undefined {
  // Convert modelId to lowercase for case-insensitive matching
  const lowerModelId = modelId.toLowerCase()

  // Check if the provider has specific configuration settings
  const providerSetting = providerModelSettings[providerId]
  if (!providerSetting || !providerSetting.models) {
    return undefined
  }

  // Iterate through the provider's model array to find a matching model configuration
  for (const config of providerSetting.models) {
    // Check if any of the matching conditions are met
    if (config.match.some((matchStr) => lowerModelId.includes(matchStr.toLowerCase()))) {
      return {
        maxTokens: config.maxTokens,
        contextLength: config.contextLength,
        temperature: config.temperature || 0.7,
        vision: config.vision || false,
        functionCall: config.functionCall || false,
        reasoning: config.reasoning || false
      }
    }
  }

  // If no matching configuration is found, return undefined
  return undefined
}
