# Docomoe Linux Build Guide

## Overview

This document describes how to build Docomoe applications using Docker in a low version glibc environment. This build method is mainly used to ensure that the generated binary files can run on most Linux systems.

## Technical Implementation

### Build Environment

- Base Image: node:22-slim (based on Debian, provides a stable build environment)
- Node.js Version: 22.x
- Build Tools: npm (configured to use the Taobao mirror source)

## Usage Instructions

### Build Steps

1. Ensure that Docker is installed and running
2. Run the following commands to build:

```bash
# Build Docker Image
docker build -t docomoe-builder -f Dockerfile.build.linux .

# Build
docker run --rm -v $(pwd):/app/dist docomoe-builder
```

### Build Artifacts

After the build is complete, the following files will be generated in the project's `dist` directory:
- Linux Executable File
- Related Dependencies and Resource Files
- AppImage or deb Package (depending on the build configuration)

### Notes

1. The build process may take a long time, please be patient
2. Ensure that there is enough disk space (at least 10GB of available space is recommended)
3. npm is configured to use the Taobao mirror source, which can significantly improve the download speed of dependency packages
4. If you need to change the npm mirror source, you can modify the relevant configuration in the Dockerfile
5. The build artifacts will inherit the glibc version of the base image, ensuring good compatibility

## Frequently Asked Questions

1. **Build Failure**
   - Check network connection
   - Confirm that there is enough disk space
   - View Docker logs for detailed error messages

2. **Dependency Issues**
   - May need to manually install some system-level dependencies.
   - Ensure that node-gyp is correctly installed.
   - If you encounter network issues, check the mirror source configuration.

3. **Permission Issues**
   - Ensure that Docker has enough permissions to access the project directory.
   - Check the ownership of the generated files.

## Technical Support

If you encounter problems during the build, please:
1. View the build logs.
2. Check the system requirements.
3. Search for related issues in the project Issue.
4. If the problem persists, please create a new Issue and provide detailed error information.