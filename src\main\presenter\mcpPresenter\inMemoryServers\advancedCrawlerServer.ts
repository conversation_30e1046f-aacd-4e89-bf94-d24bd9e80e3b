import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import axios from 'axios'
import * as cheerio from 'cheerio'
import { HttpsProxyAgent } from 'https-proxy-agent'
import { proxyConfig } from '@/presenter/proxyConfig'
import robotsParser from 'robots-parser'

// Schema definitions for crawler tools
const CrawlWebsiteArgsSchema = z.object({
  url: z.string().url().describe('The URL to crawl'),
  maxDepth: z.number().min(1).max(5).default(1).describe('Maximum crawl depth (1-5)'),
  maxBreadth: z.number().min(1).max(20).default(10).describe('Maximum links per page level (1-20)'),
  limit: z.number().min(1).max(100).default(20).describe('Total pages to crawl (1-100)'),
  selectDomains: z
    .array(z.string())
    .optional()
    .default([])
    .describe('Regex patterns for allowed domains'),
  excludeDomains: z
    .array(z.string())
    .optional()
    .default([])
    .describe('Regex patterns for excluded domains'),
  selectPaths: z
    .array(z.string())
    .optional()
    .default([])
    .describe('Regex patterns for allowed paths'),
  excludePaths: z
    .array(z.string())
    .optional()
    .default([])
    .describe('Regex patterns for excluded paths'),
  allowExternal: z.boolean().default(false).describe('Allow crawling external domains'),
  includeImages: z.boolean().default(false).describe('Extract image URLs'),
  extractDepth: z
    .enum(['basic', 'advanced', 'research'])
    .default('basic')
    .describe('Content extraction depth'),
  timeout: z.number().default(30).describe('Request timeout in seconds'),
  respectRobots: z.boolean().default(true).describe('Respect robots.txt directives'),
  rateLimit: z.number().min(1).max(10).default(3).describe('Requests per second (1-10)'),
  researchMode: z
    .boolean()
    .default(false)
    .describe('Enable research-focused extraction and scoring'),
  minContentQuality: z
    .number()
    .min(0)
    .max(1)
    .default(0.3)
    .describe('Minimum content quality score (0-1)'),
  enablePIIDetection: z
    .boolean()
    .default(true)
    .describe('Enable PII detection and privacy risk assessment'),
  piiSanitization: z
    .enum(['none', 'redact', 'hash', 'remove'])
    .default('redact')
    .describe('PII sanitization method'),
  maxPIIRisk: z
    .enum(['low', 'medium', 'high', 'critical'])
    .default('high')
    .describe('Maximum acceptable PII risk level'),
  enableAuditLogging: z.boolean().default(true).describe('Enable comprehensive audit logging')
})

const BatchCrawlArgsSchema = z.object({
  urls: z.array(z.string().url()).describe('Array of URLs to crawl'),
  crawlParams: CrawlWebsiteArgsSchema.omit({ url: true }).describe('Crawl parameters'),
  concurrency: z.number().min(1).max(5).default(3).describe('Concurrent crawl limit')
})

const GetAuditLogsArgsSchema = z.object({
  includeSessionSummary: z.boolean().default(true).describe('Include session summary statistics'),
  filterByEvent: z
    .string()
    .optional()
    .describe('Filter logs by event type (e.g., "pii_detected", "crawl_error")'),
  filterByRiskLevel: z
    .enum(['low', 'medium', 'high', 'critical'])
    .optional()
    .describe('Filter by PII risk level'),
  limit: z
    .number()
    .min(1)
    .max(1000)
    .default(100)
    .describe('Maximum number of log entries to return')
})

// Enhanced interfaces
interface CrawlResult {
  url: string
  title: string
  content: string
  images: string[]
  links: string[]
  metadata: {
    depth: number
    timestamp: string
    contentLength: number
    language?: string
    lastModified?: string
    contentQuality?: number
    citations?: string[]
    keywords?: string[]
    readingTime?: number
  }
  errors: string[]
}

interface CrawlParams {
  maxDepth: number
  maxBreadth: number
  limit: number
  selectPaths: string[]
  selectDomains: string[]
  excludePaths: string[]
  excludeDomains: string[]
  allowExternal: boolean
  includeImages: boolean
  extractDepth: 'basic' | 'advanced' | 'research'
  timeout: number
  respectRobots: boolean
  rateLimit: number
  researchMode: boolean
  minContentQuality: number
  enablePIIDetection: boolean
  piiSanitization: 'none' | 'redact' | 'hash' | 'remove'
  maxPIIRisk: 'low' | 'medium' | 'high' | 'critical'
  enableAuditLogging: boolean
}

// Enhanced rate limiter with domain-specific limits and exponential backoff
class RateLimiter {
  private requests: number[] = []
  private domainRequests: Map<string, number[]> = new Map()
  private readonly maxRequests: number
  private readonly timeWindow: number

  constructor(maxRequests: number = 3, timeWindowMs: number = 1000) {
    this.maxRequests = maxRequests
    this.timeWindow = timeWindowMs
  }

  async waitForSlot(domain?: string): Promise<void> {
    const now = Date.now()

    // Global rate limiting
    this.requests = this.requests.filter((time) => now - time < this.timeWindow)

    // Domain-specific rate limiting
    if (domain) {
      const domainReqs = this.domainRequests.get(domain) || []
      const filteredDomainReqs = domainReqs.filter((time) => now - time < this.timeWindow)
      this.domainRequests.set(domain, filteredDomainReqs)

      if (filteredDomainReqs.length >= this.maxRequests) {
        const oldestRequest = Math.min(...filteredDomainReqs)
        const waitTime = this.timeWindow - (now - oldestRequest)
        if (waitTime > 0) {
          await new Promise((resolve) => setTimeout(resolve, waitTime))
        }
      }
      filteredDomainReqs.push(now)
    }

    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests)
      const waitTime = this.timeWindow - (now - oldestRequest)
      if (waitTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, waitTime))
      }
    }

    this.requests.push(now)
  }

  async waitWithBackoff(url: string, attempt: number = 0): Promise<void> {
    const domain = new URL(url).hostname
    await this.waitForSlot(domain)

    if (attempt > 0) {
      // Exponential backoff: 1s, 2s, 4s, 8s...
      const backoffTime = Math.min(1000 * Math.pow(2, attempt - 1), 30000)
      await new Promise((resolve) => setTimeout(resolve, backoffTime))
    }
  }
}

// Memory manager class
class MemoryManager {
  private readonly maxMemoryMB: number
  private currentMemoryMB: number = 0

  constructor(maxMemoryMB: number = 100) {
    this.maxMemoryMB = maxMemoryMB
  }

  canAllocate(sizeMB: number): boolean {
    return this.currentMemoryMB + sizeMB <= this.maxMemoryMB
  }

  allocate(sizeMB: number): void {
    this.currentMemoryMB += sizeMB
  }

  deallocate(sizeMB: number): void {
    this.currentMemoryMB = Math.max(0, this.currentMemoryMB - sizeMB)
  }

  getUsagePercentage(): number {
    return (this.currentMemoryMB / this.maxMemoryMB) * 100
  }

  reset(): void {
    this.currentMemoryMB = 0
  }
}

// Robots.txt checker with caching
class RobotsChecker {
  private cache: Map<string, { robots: any; timestamp: number; ttl: number }> = new Map()
  private readonly cacheTTL = 3600000 // 1 hour

  async canCrawl(url: string, userAgent: string = '*'): Promise<boolean> {
    try {
      const urlObj = new URL(url)
      const domain = urlObj.hostname
      const robotsUrl = `${urlObj.protocol}//${domain}/robots.txt`

      // Check cache first
      const cached = this.cache.get(domain)
      if (cached && Date.now() - cached.timestamp < cached.ttl) {
        return cached.robots.isAllowed(url, userAgent)
      }

      // Fetch robots.txt
      const response = await axios.get(robotsUrl, {
        timeout: 5000,
        headers: {
          'User-Agent': 'DocoMoe Research Crawler (+https://docomoe.com/crawler)'
        }
      })

      const robots = robotsParser(robotsUrl, response.data)

      // Cache the result
      this.cache.set(domain, {
        robots,
        timestamp: Date.now(),
        ttl: this.cacheTTL
      })

      return robots.isAllowed(url, userAgent) ?? true
    } catch (error) {
      // If robots.txt is not accessible, assume crawling is allowed
      console.warn(`Could not fetch robots.txt for ${url}:`, error)
      return true
    }
  }
}

// PII Detection and Sanitization
class PIIDetector {
  private static readonly patterns = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b/g,
    ssn: /\b(?:\d{3}-?\d{2}-?\d{4})\b/g,
    creditCard: /\b(?:\d{4}[-\s]?){3}\d{4}\b/g,
    ipAddress: /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
    // Common personal name patterns (basic heuristic)
    personalName: /\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s[A-Z][a-z]+)?\b/g,
    // Address patterns (US format)
    address:
      /\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b/gi,
    // Date of birth patterns
    dateOfBirth: /\b(?:0[1-9]|1[0-2])[-/](?:0[1-9]|[12]\d|3[01])[-/](?:19|20)\d{2}\b/g
  }

  static detectPII(content: string): { type: string; matches: string[]; count: number }[] {
    const detections: { type: string; matches: string[]; count: number }[] = []

    for (const [type, pattern] of Object.entries(this.patterns)) {
      const matches = content.match(pattern) || []
      if (matches.length > 0) {
        detections.push({
          type,
          matches: [...new Set(matches)], // Deduplicate
          count: matches.length
        })
      }
    }

    return detections
  }

  static sanitizeContent(
    content: string,
    sanitizationLevel: 'redact' | 'hash' | 'remove' = 'redact'
  ): {
    sanitizedContent: string
    piiDetected: { type: string; matches: string[]; count: number }[]
  } {
    const piiDetected = this.detectPII(content)
    let sanitizedContent = content

    for (const [type, pattern] of Object.entries(this.patterns)) {
      switch (sanitizationLevel) {
        case 'redact':
          sanitizedContent = sanitizedContent.replace(pattern, `[${type.toUpperCase()}_REDACTED]`)
          break
        case 'hash':
          sanitizedContent = sanitizedContent.replace(
            pattern,
            (match) => `[${type.toUpperCase()}_${this.simpleHash(match)}]`
          )
          break
        case 'remove':
          sanitizedContent = sanitizedContent.replace(pattern, '')
          break
      }
    }

    return { sanitizedContent, piiDetected }
  }

  private static simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).substring(0, 8)
  }

  static assessPrivacyRisk(piiDetected: { type: string; matches: string[]; count: number }[]): {
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
    score: number
    concerns: string[]
  } {
    let score = 0
    const concerns: string[] = []

    const riskWeights = {
      email: 2,
      phone: 3,
      ssn: 10,
      creditCard: 10,
      ipAddress: 1,
      personalName: 1,
      address: 4,
      dateOfBirth: 5
    }

    for (const detection of piiDetected) {
      const weight = riskWeights[detection.type as keyof typeof riskWeights] || 1
      score += detection.count * weight

      if (detection.count > 0) {
        concerns.push(`${detection.count} ${detection.type} instance(s) detected`)
      }
    }

    let riskLevel: 'low' | 'medium' | 'high' | 'critical'
    if (score >= 20) riskLevel = 'critical'
    else if (score >= 10) riskLevel = 'high'
    else if (score >= 5) riskLevel = 'medium'
    else riskLevel = 'low'

    return { riskLevel, score, concerns }
  }
}

// Audit Logger for crawler activities
class CrawlerAuditLogger {
  private static logs: Array<{
    timestamp: string
    sessionId: string
    event: string
    url?: string
    domain?: string
    piiRisk?: string
    contentQuality?: number
    robotsCompliant?: boolean
    errorType?: string
    metadata?: Record<string, any>
  }> = []

  private static sessionId = this.generateSessionId()

  private static generateSessionId(): string {
    return `crawl_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  }

  static logCrawlStart(url: string, params: CrawlParams): void {
    this.log('crawl_start', {
      url,
      domain: new URL(url).hostname,
      metadata: {
        maxDepth: params.maxDepth,
        maxBreadth: params.maxBreadth,
        extractDepth: params.extractDepth,
        researchMode: params.researchMode,
        respectRobots: params.respectRobots
      }
    })
  }

  static logPageCrawl(
    result: CrawlResult,
    piiRisk?: { riskLevel: string; score: number; concerns: string[] }
  ): void {
    this.log('page_crawled', {
      url: result.url,
      domain: new URL(result.url).hostname,
      piiRisk: piiRisk?.riskLevel,
      contentQuality: result.metadata.contentQuality,
      robotsCompliant:
        result.errors.length === 0 || !result.errors.some((e) => e.includes('robots.txt')),
      metadata: {
        contentLength: result.metadata.contentLength,
        depth: result.metadata.depth,
        language: result.metadata.language,
        piiScore: piiRisk?.score,
        piiConcerns: piiRisk?.concerns,
        hasErrors: result.errors.length > 0,
        errorCount: result.errors.length
      }
    })
  }

  static logError(url: string, errorType: string, errorMessage: string): void {
    this.log('crawl_error', {
      url,
      domain: url ? new URL(url).hostname : undefined,
      errorType,
      metadata: { errorMessage }
    })
  }

  static logRobotsViolation(url: string): void {
    this.log('robots_violation', {
      url,
      domain: new URL(url).hostname,
      robotsCompliant: false
    })
  }

  static logPIIDetection(
    url: string,
    piiDetected: { type: string; matches: string[]; count: number }[],
    riskAssessment: { riskLevel: string; score: number }
  ): void {
    this.log('pii_detected', {
      url,
      domain: new URL(url).hostname,
      piiRisk: riskAssessment.riskLevel,
      metadata: {
        piiTypes: piiDetected.map((p) => p.type),
        totalPIIInstances: piiDetected.reduce((sum, p) => sum + p.count, 0),
        riskScore: riskAssessment.score,
        detections: piiDetected
      }
    })
  }

  static logCrawlComplete(summary: any): void {
    this.log('crawl_complete', {
      metadata: {
        totalPages: summary.totalPages,
        totalContent: summary.totalContent,
        averageContentLength: summary.averageContentLength,
        errors: summary.errors,
        crawlDepth: summary.crawlDepth
      }
    })
  }

  private static log(
    event: string,
    data: Partial<{
      url: string
      domain: string
      piiRisk: string
      contentQuality: number
      robotsCompliant: boolean
      errorType: string
      metadata: Record<string, any>
    }>
  ): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
      event,
      ...data
    }

    this.logs.push(logEntry)

    // Console logging for development (replace with your in-house logging)
    console.log(`[CrawlerAudit] ${event}:`, logEntry)

    // TODO: Integrate with your in-house logging system here
    // Example: YourLoggingService.log('crawler_audit', logEntry)
  }

  static getAuditLogs(): typeof CrawlerAuditLogger.logs {
    return [...this.logs]
  }

  static clearLogs(): void {
    this.logs = []
    this.sessionId = this.generateSessionId()
  }

  static getSessionSummary(): {
    sessionId: string
    totalEvents: number
    crawlEvents: number
    errorEvents: number
    piiEvents: number
    robotsViolations: number
    highRiskPages: number
  } {
    const crawlEvents = this.logs.filter((log) => log.event === 'page_crawled').length
    const errorEvents = this.logs.filter((log) => log.event === 'crawl_error').length
    const piiEvents = this.logs.filter((log) => log.event === 'pii_detected').length
    const robotsViolations = this.logs.filter((log) => log.event === 'robots_violation').length
    const highRiskPages = this.logs.filter(
      (log) => log.piiRisk === 'high' || log.piiRisk === 'critical'
    ).length

    return {
      sessionId: this.sessionId,
      totalEvents: this.logs.length,
      crawlEvents,
      errorEvents,
      piiEvents,
      robotsViolations,
      highRiskPages
    }
  }
}

// Content quality scorer for research purposes
class ContentQualityScorer {
  static scoreContent(content: string, title: string, url: string): number {
    let score = 0
    const contentLength = content.length

    // Length scoring (0-0.3)
    if (contentLength > 500) score += 0.1
    if (contentLength > 1500) score += 0.1
    if (contentLength > 3000) score += 0.1

    // Academic/research indicators (0-0.4)
    const academicKeywords = [
      'research',
      'study',
      'analysis',
      'methodology',
      'findings',
      'conclusion',
      'abstract',
      'introduction',
      'literature',
      'references',
      'citation',
      'peer-reviewed',
      'journal',
      'university',
      'institute',
      'academic'
    ]

    const academicMatches = academicKeywords.filter(
      (keyword) => content.toLowerCase().includes(keyword) || title.toLowerCase().includes(keyword)
    ).length

    score += Math.min(academicMatches * 0.05, 0.4)

    // Domain authority (0-0.2)
    const authorityDomains = ['.edu', '.org', '.gov', 'wikipedia.org', 'scholar.google']
    if (authorityDomains.some((domain) => url.includes(domain))) {
      score += 0.2
    }

    // Structure indicators (0-0.1)
    if (content.includes('Abstract:') || content.includes('Introduction:')) score += 0.05
    if (content.includes('References') || content.includes('Bibliography')) score += 0.05

    return Math.min(score, 1.0)
  }

  static extractCitations(content: string): string[] {
    const citations: string[] = []

    // DOI pattern
    const doiPattern = /10\.\d{4,}\/[^\s]+/g
    const dois = content.match(doiPattern) || []
    citations.push(...dois.map((doi: string) => `DOI: ${doi}`))

    // URL pattern in parentheses (common citation format)
    const urlPattern = /\(https?:\/\/[^\s)]+\)/g
    const urls = content.match(urlPattern) || []
    citations.push(...urls.map((url: string) => url.slice(1, -1)))

    return [...new Set(citations)].slice(0, 10) // Deduplicate and limit
  }

  static extractKeywords(content: string, title: string): string[] {
    const text = `${title} ${content}`.toLowerCase()
    const words = text.match(/\b[a-z]{4,}\b/g) || []

    // Count word frequency
    const wordCount = new Map<string, number>()
    words.forEach((word) => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1)
    })

    // Filter common words and sort by frequency
    const commonWords = new Set([
      'this',
      'that',
      'with',
      'have',
      'will',
      'from',
      'they',
      'been',
      'said',
      'each',
      'which',
      'their',
      'time',
      'more',
      'very',
      'when',
      'come',
      'here',
      'just',
      'like',
      'long',
      'make',
      'many',
      'over',
      'such',
      'take',
      'than',
      'them',
      'well',
      'were'
    ])

    return Array.from(wordCount.entries())
      .filter(([word, count]) => count > 1 && !commonWords.has(word))
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word)
  }

  static estimateReadingTime(content: string): number {
    const wordsPerMinute = 200
    const wordCount = content.split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  }
}

// Advanced web crawler class
class AdvancedWebCrawler {
  private visitedUrls = new Set<string>()
  private crawledCount = 0
  private readonly userAgent = 'DocoMoe Research Crawler/1.0 (+https://docomoe.com/crawler)'
  private rateLimiter: RateLimiter
  private memoryManager: MemoryManager
  private robotsChecker: RobotsChecker

  constructor(private params: CrawlParams) {
    this.rateLimiter = new RateLimiter(params.rateLimit, 1000)
    this.memoryManager = new MemoryManager(100) // 100MB limit
    this.robotsChecker = new RobotsChecker()
  }

  async crawl(startUrl: string): Promise<CrawlResult[]> {
    this.visitedUrls.clear()
    this.crawledCount = 0
    this.memoryManager.reset()

    // Log crawl start
    if (this.params.enableAuditLogging) {
      CrawlerAuditLogger.logCrawlStart(startUrl, this.params)
    }

    const results: CrawlResult[] = []
    const urlQueue: Array<{ url: string; depth: number }> = [{ url: startUrl, depth: 0 }]

    while (urlQueue.length > 0 && this.crawledCount < this.params.limit) {
      const batch = urlQueue.splice(0, Math.min(this.params.maxBreadth, urlQueue.length))

      // Concurrently handle current batch of URLs with rate limiting and retry logic
      const batchPromises = batch.map(async ({ url, depth }) => {
        return this.crawlWithRetry(url, depth, 3).catch((error) => {
          console.error(`Failed to crawl ${url} after retries:`, error.message)
          return {
            url,
            title: '',
            content: '',
            images: [],
            links: [],
            metadata: {
              depth,
              timestamp: new Date().toISOString(),
              contentLength: 0
            },
            errors: [error.message]
          } as CrawlResult
        })
      })

      const batchResults = await Promise.all(batchPromises)

      for (const result of batchResults) {
        if (result && result.content) {
          results.push(result)
          this.crawledCount++

          // If not yet reached maximum depth, extract new links and add to queue
          if (
            result.metadata.depth < this.params.maxDepth &&
            this.crawledCount < this.params.limit
          ) {
            const newUrls = this.extractValidLinks(result.links, result.metadata.depth + 1)
            urlQueue.push(...newUrls.slice(0, this.params.maxBreadth))
          }
        }
      }
    }

    return results
  }

  private async crawlWithRetry(
    url: string,
    depth: number,
    maxRetries: number = 3
  ): Promise<CrawlResult | null> {
    let lastError: Error | null = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.crawlSinglePage(url, depth, attempt)
        return result
      } catch (error) {
        lastError = error as Error
        console.warn(
          `Attempt ${attempt + 1}/${maxRetries + 1} failed for ${url}:`,
          lastError.message
        )

        // Don't retry on certain errors
        if (
          lastError.message.includes('robots.txt') ||
          lastError.message.includes('Memory limit') ||
          lastError.message.includes('404') ||
          lastError.message.includes('403')
        ) {
          throw lastError
        }

        // Wait before retry (exponential backoff handled in rateLimiter)
        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)))
        }
      }
    }

    throw lastError || new Error('Max retries exceeded')
  }

  private async crawlSinglePage(
    url: string,
    depth: number,
    attempt: number = 0
  ): Promise<CrawlResult | null> {
    if (this.visitedUrls.has(url) || this.crawledCount >= this.params.limit) {
      return null
    }

    // Check robots.txt if enabled
    if (this.params.respectRobots) {
      const canCrawl = await this.robotsChecker.canCrawl(url, this.userAgent)
      if (!canCrawl) {
        console.log(`Robots.txt disallows crawling: ${url}`)
        return {
          url,
          title: '',
          content: '',
          images: [],
          links: [],
          metadata: {
            depth,
            timestamp: new Date().toISOString(),
            contentLength: 0
          },
          errors: ['Blocked by robots.txt']
        }
      }
    }

    this.visitedUrls.add(url)

    try {
      // Check memory before proceeding
      if (!this.memoryManager.canAllocate(5)) {
        // Estimate 5MB per page
        throw new Error('Memory limit exceeded')
      }

      // Apply rate limiting with backoff
      await this.rateLimiter.waitWithBackoff(url, attempt)

      const proxyUrl = proxyConfig.getProxyUrl()
      const response = await axios.get(url, {
        timeout: this.params.timeout * 1000,
        headers: {
          'User-Agent': this.userAgent,
          Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          Connection: 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        httpAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
        httpsAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
        maxRedirects: 5
      })

      this.memoryManager.allocate(5) // Track memory usage

      const $ = cheerio.load(response.data)

      // Extract title
      const title =
        $('title').text().trim() ||
        $('meta[property="og:title"]').attr('content') ||
        $('h1').first().text().trim() ||
        url

      // Remove unnecessary elements
      $(
        'script, style, nav, header, footer, iframe, .ad, #ad, .advertisement, .sidebar, .menu'
      ).remove()

      // Extract main content
      let content = this.extractContent(response.data, this.params.extractDepth)

      // PII Detection and Sanitization
      let piiDetected: { type: string; matches: string[]; count: number }[] = []
      let piiRisk:
        | { riskLevel: 'low' | 'medium' | 'high' | 'critical'; score: number; concerns: string[] }
        | undefined

      if (this.params.enablePIIDetection) {
        // First detect PII without sanitization
        piiDetected = PIIDetector.detectPII(content)
        piiRisk = PIIDetector.assessPrivacyRisk(piiDetected)

        // Apply sanitization if not 'none'
        if (this.params.piiSanitization !== 'none') {
          const piiResult = PIIDetector.sanitizeContent(
            content,
            this.params.piiSanitization as 'redact' | 'hash' | 'remove'
          )
          content = piiResult.sanitizedContent
        }

        // Log PII detection if found
        if (this.params.enableAuditLogging && piiDetected.length > 0) {
          CrawlerAuditLogger.logPIIDetection(url, piiDetected, piiRisk)
        }

        // Check if PII risk exceeds acceptable level
        const riskLevels = ['low', 'medium', 'high', 'critical']
        const maxRiskIndex = riskLevels.indexOf(this.params.maxPIIRisk)
        const currentRiskIndex = riskLevels.indexOf(piiRisk.riskLevel)

        if (currentRiskIndex > maxRiskIndex) {
          this.memoryManager.deallocate(5)
          if (this.params.enableAuditLogging) {
            CrawlerAuditLogger.logError(
              url,
              'pii_risk_exceeded',
              `PII risk ${piiRisk.riskLevel} exceeds maximum ${this.params.maxPIIRisk}`
            )
          }
          return {
            url,
            title,
            content: '',
            images: [],
            links: [],
            metadata: {
              depth,
              timestamp: new Date().toISOString(),
              contentLength: 0
            },
            errors: [
              `PII risk level ${piiRisk.riskLevel} exceeds maximum acceptable level ${this.params.maxPIIRisk}`
            ]
          }
        }
      }

      // Calculate content quality score if in research mode
      const contentQuality = this.params.researchMode
        ? ContentQualityScorer.scoreContent(content, title, url)
        : undefined

      // Skip low-quality content if threshold is set
      if (
        this.params.researchMode &&
        contentQuality !== undefined &&
        contentQuality < this.params.minContentQuality
      ) {
        this.memoryManager.deallocate(5)
        return {
          url,
          title,
          content: '',
          images: [],
          links: [],
          metadata: {
            depth,
            timestamp: new Date().toISOString(),
            contentLength: 0,
            contentQuality
          },
          errors: [
            `Content quality score ${contentQuality.toFixed(2)} below threshold ${this.params.minContentQuality}`
          ]
        }
      }

      // Extract metadata
      const language =
        $('html').attr('lang') ||
        $('meta[http-equiv="content-language"]').attr('content') ||
        undefined

      const lastModified = response.headers['last-modified'] || undefined

      // Research-specific extractions
      const citations = this.params.researchMode
        ? ContentQualityScorer.extractCitations(content)
        : undefined
      const keywords = this.params.researchMode
        ? ContentQualityScorer.extractKeywords(content, title)
        : undefined
      const readingTime = this.params.researchMode
        ? ContentQualityScorer.estimateReadingTime(content)
        : undefined

      // Extract images (if needed)
      const images: string[] = []
      if (this.params.includeImages) {
        $('img').each((_, element) => {
          const src = $(element).attr('src') || $(element).attr('data-src')
          if (src) {
            const absoluteUrl = this.resolveUrl(src, url)
            if (absoluteUrl) images.push(absoluteUrl)
          }
        })
      }

      // Extract links
      const links: string[] = []
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href')
        if (
          href &&
          !href.startsWith('#') &&
          !href.startsWith('javascript:') &&
          !href.startsWith('mailto:')
        ) {
          const absoluteUrl = this.resolveUrl(href, url)
          if (absoluteUrl && this.isValidUrl(absoluteUrl)) {
            links.push(absoluteUrl)
          }
        }
      })

      this.memoryManager.deallocate(5) // Release memory

      const result: CrawlResult = {
        url,
        title,
        content,
        images: [...new Set(images)].slice(0, 20), // Deduplicate and limit
        links: [...new Set(links)].slice(0, 50), // Deduplicate and limit
        metadata: {
          depth,
          timestamp: new Date().toISOString(),
          contentLength: content.length,
          language,
          lastModified,
          contentQuality,
          citations,
          keywords,
          readingTime
        },
        errors: []
      }

      // Log successful page crawl
      if (this.params.enableAuditLogging) {
        CrawlerAuditLogger.logPageCrawl(result, piiRisk)
      }

      return result
    } catch (error: unknown) {
      this.memoryManager.deallocate(5) // Release memory on error
      const err = error as Error
      console.error(`Failed to crawl page ${url}:`, err.message)
      return {
        url,
        title: '',
        content: '',
        images: [],
        links: [],
        metadata: {
          depth,
          timestamp: new Date().toISOString(),
          contentLength: 0
        },
        errors: [err.message]
      }
    }
  }

  private extractContent(html: string, strategy: 'basic' | 'advanced' | 'research'): string {
    const $ = cheerio.load(html)

    // Remove unnecessary elements
    $(
      'script, style, nav, header, footer, iframe, .ad, #ad, .advertisement, .sidebar, .menu, .comments'
    ).remove()

    if (strategy === 'research') {
      return this.extractResearchContent($)
    } else if (strategy === 'advanced') {
      return this.extractAdvancedContent($)
    } else {
      return this.extractBasicContent($)
    }
  }

  private extractBasicContent($: cheerio.CheerioAPI): string {
    // Try multiple strategies to extract main content
    const strategies = [
      // 1. Try semantic tags
      () => {
        const article = $('article').first()
        if (article.length) return article.text()
        return null
      },
      // 2. Try main tag
      () => {
        const main = $('main').first()
        if (main.length) return main.text()
        return null
      },
      // 3. Try common content class names
      () => {
        const selectors = [
          '.content',
          '#content',
          '.post-content',
          '.article-content',
          '.entry-content',
          '.container'
        ]
        for (const selector of selectors) {
          const element = $(selector).first()
          if (element.length && element.text().trim().length > 200) {
            return element.text()
          }
        }
        return null
      },
      // 4. Use body as last resort
      () => {
        return $('body').text()
      }
    ]

    for (const strategy of strategies) {
      const content = strategy()
      if (content && content.trim().length > 50) {
        return this.cleanText(content)
      }
    }

    return ''
  }

  private extractAdvancedContent($: cheerio.CheerioAPI): string {
    // Advanced content extraction with better heuristics
    const contentCandidates: Array<{ element: cheerio.Cheerio<any>; score: number }> = []

    // Score different elements based on content indicators
    $('div, article, section, main').each((_, element) => {
      const $el = $(element)
      const text = $el.text().trim()

      if (text.length < 100) return // Skip short content

      let score = 0

      // Positive indicators
      if ($el.is('article, main')) score += 50
      if ($el.hasClass('content') || $el.hasClass('post') || $el.hasClass('article')) score += 30
      if ($el.find('p').length > 2) score += 20
      if (text.length > 500) score += 10
      if ($el.find('h1, h2, h3').length > 0) score += 15

      // Negative indicators
      if ($el.hasClass('sidebar') || $el.hasClass('menu') || $el.hasClass('nav')) score -= 30
      if ($el.find('a').length > $el.find('p').length) score -= 10
      if ($el.hasClass('ad') || $el.hasClass('advertisement')) score -= 50

      contentCandidates.push({ element: $el, score })
    })

    // Sort by score and get the best candidate
    contentCandidates.sort((a, b) => b.score - a.score)

    if (contentCandidates.length > 0) {
      const bestCandidate = contentCandidates[0]
      return this.cleanText(bestCandidate.element.text())
    }

    // Fallback to basic extraction
    return this.extractBasicContent($)
  }

  private extractResearchContent($: cheerio.CheerioAPI): string {
    // Research-focused content extraction prioritizing academic and structured content
    const contentSections: string[] = []

    // Extract abstract if present
    const abstract = $(
      'section[class*="abstract"], div[class*="abstract"], .abstract, #abstract'
    ).first()
    if (abstract.length) {
      contentSections.push(`Abstract: ${this.cleanText(abstract.text())}`)
    }

    // Extract introduction
    const intro = $(
      'section[class*="introduction"], div[class*="introduction"], .introduction, #introduction'
    ).first()
    if (intro.length) {
      contentSections.push(`Introduction: ${this.cleanText(intro.text())}`)
    }

    // Extract main content using advanced method
    const mainContent = this.extractAdvancedContent($)
    if (mainContent) {
      contentSections.push(mainContent)
    }

    // Extract conclusion if present
    const conclusion = $(
      'section[class*="conclusion"], div[class*="conclusion"], .conclusion, #conclusion'
    ).first()
    if (conclusion.length) {
      contentSections.push(`Conclusion: ${this.cleanText(conclusion.text())}`)
    }

    // Extract references section
    const references = $(
      'section[class*="reference"], div[class*="reference"], .references, #references, .bibliography, #bibliography'
    ).first()
    if (references.length) {
      const refText = this.cleanText(references.text())
      if (refText.length > 50) {
        contentSections.push(`References: ${refText.slice(0, 1000)}...`) // Limit references
      }
    }

    return contentSections.join('\n\n') || this.extractAdvancedContent($)
  }

  private cleanText(text: string): string {
    return text
      .replace(/[\r\n]+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .slice(0, 8000) // Limit content length to 8000 characters
  }

  private resolveUrl(href: string, baseUrl: string): string | null {
    try {
      if (href.startsWith('http')) {
        return href
      }
      return new URL(href, baseUrl).toString()
    } catch {
      return null
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)

      // Basic URL format check
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false
      }

      // Check domain filtering
      if (!this.params.allowExternal) {
        // If external links are not allowed, check domain restrictions
        if (this.params.selectDomains.length > 0) {
          const matchesDomain = this.params.selectDomains.some((pattern) => {
            try {
              return new RegExp(pattern).test(urlObj.hostname)
            } catch {
              return urlObj.hostname.includes(pattern)
            }
          })
          if (!matchesDomain) return false
        }
      }

      // Check excluded domains
      if (this.params.excludeDomains.length > 0) {
        const isExcluded = this.params.excludeDomains.some((pattern) => {
          try {
            return new RegExp(pattern).test(urlObj.hostname)
          } catch {
            return urlObj.hostname.includes(pattern)
          }
        })
        if (isExcluded) return false
      }

      // Check path filtering
      if (this.params.selectPaths.length > 0) {
        const matchesPath = this.params.selectPaths.some((pattern) => {
          try {
            return new RegExp(pattern).test(urlObj.pathname)
          } catch {
            return urlObj.pathname.includes(pattern)
          }
        })
        if (!matchesPath) return false
      }

      // Check excluded paths
      if (this.params.excludePaths.length > 0) {
        const isExcluded = this.params.excludePaths.some((pattern) => {
          try {
            return new RegExp(pattern).test(urlObj.pathname)
          } catch {
            return urlObj.pathname.includes(pattern)
          }
        })
        if (isExcluded) return false
      }

      // Exclude common non-content files
      const excludeExtensions = [
        '.pdf',
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.svg',
        '.ico',
        '.zip',
        '.rar',
        '.exe',
        '.dmg',
        '.mp4',
        '.mp3',
        '.avi',
        '.css',
        '.js',
        '.xml',
        '.json'
      ]
      if (excludeExtensions.some((ext) => urlObj.pathname.toLowerCase().endsWith(ext))) {
        return false
      }

      return true
    } catch {
      return false
    }
  }

  private extractValidLinks(links: string[], depth: number): Array<{ url: string; depth: number }> {
    return links
      .filter((link) => !this.visitedUrls.has(link) && this.isValidUrl(link))
      .slice(0, this.params.maxBreadth)
      .map((url) => ({ url, depth }))
  }
}

// Advanced Crawler Server
export class AdvancedCrawlerServer {
  private server: Server

  constructor() {
    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/advanced-crawler-server',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set request handler
    this.setupRequestHandlers()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Set request handler
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'crawl_website',
            description:
              'Advanced ethical web crawler with robots.txt compliance, content quality scoring, and research-focused extraction. ' +
              'Features: rate limiting, retry logic, memory management, citation extraction, and structured academic content parsing. ' +
              'Supports basic, advanced, and research extraction modes for different use cases. ' +
              'Includes PII detection, sanitization, and comprehensive audit logging for ethical compliance.',
            inputSchema: zodToJsonSchema(CrawlWebsiteArgsSchema)
          },
          {
            name: 'batch_crawl_urls',
            description:
              'Crawl multiple URLs concurrently with shared crawl parameters and ethical controls. ' +
              'Includes automatic retry logic, domain-specific rate limiting, and content quality filtering. ' +
              'Ideal for research workflows requiring high-quality, structured content from multiple sources. ' +
              'Features PII detection and audit logging for compliance monitoring.',
            inputSchema: zodToJsonSchema(BatchCrawlArgsSchema)
          },
          {
            name: 'get_audit_logs',
            description:
              'Retrieve comprehensive audit logs from crawler sessions including PII detection events, ' +
              'robots.txt compliance, error tracking, and session statistics. ' +
              'Essential for monitoring ethical compliance, debugging issues, and analyzing crawler behavior patterns.',
            inputSchema: zodToJsonSchema(GetAuditLogsArgsSchema)
          }
        ]
      }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        const { name, arguments: args } = request.params

        switch (name) {
          case 'crawl_website':
            return await this.handleCrawlWebsite(args)
          case 'batch_crawl_urls':
            return await this.handleBatchCrawl(args)
          case 'get_audit_logs':
            return await this.handleGetAuditLogs(args)
          default:
            throw new Error(`Unknown tool: ${name}`)
        }
      } catch (error) {
        console.error('Error calling tool:', error)
        const errorMessage =
          error instanceof Error
            ? error.message
            : typeof error === 'string'
              ? error
              : 'An unknown error occurred'

        return {
          content: [{ type: 'text', text: `Error: ${errorMessage}` }],
          isError: true
        }
      }
    })
  }

  private async handleCrawlWebsite(args: unknown) {
    const parsed = CrawlWebsiteArgsSchema.safeParse(args)
    if (!parsed.success) {
      throw new Error(`Invalid crawl parameters: ${parsed.error}`)
    }

    const crawlArgs = parsed.data

    try {
      console.log(`Starting website crawl for: ${crawlArgs.url}`)

      const crawlParams: CrawlParams = {
        maxDepth: crawlArgs.maxDepth,
        maxBreadth: crawlArgs.maxBreadth,
        limit: crawlArgs.limit,
        selectPaths: crawlArgs.selectPaths || [],
        selectDomains: crawlArgs.selectDomains || [],
        excludePaths: crawlArgs.excludePaths || [],
        excludeDomains: crawlArgs.excludeDomains || [],
        allowExternal: crawlArgs.allowExternal,
        includeImages: crawlArgs.includeImages,
        extractDepth: crawlArgs.extractDepth,
        timeout: crawlArgs.timeout,
        respectRobots: crawlArgs.respectRobots,
        rateLimit: crawlArgs.rateLimit,
        researchMode: crawlArgs.researchMode,
        minContentQuality: crawlArgs.minContentQuality,
        enablePIIDetection: crawlArgs.enablePIIDetection,
        piiSanitization: crawlArgs.piiSanitization,
        maxPIIRisk: crawlArgs.maxPIIRisk,
        enableAuditLogging: crawlArgs.enableAuditLogging
      }

      const crawler = new AdvancedWebCrawler(crawlParams)
      const results = await crawler.crawl(crawlArgs.url)

      const summary = {
        totalPages: results.length,
        totalContent: results.reduce((sum, result) => sum + result.metadata.contentLength, 0),
        averageContentLength:
          results.length > 0
            ? Math.round(
                results.reduce((sum, result) => sum + result.metadata.contentLength, 0) /
                  results.length
              )
            : 0,
        errors: results.filter((result) => result.errors.length > 0).length,
        crawlDepth: Math.max(...results.map((result) => result.metadata.depth), 0),
        timestamp: new Date().toISOString()
      }

      const outputData = {
        summary,
        results,
        crawlParams: {
          startUrl: crawlArgs.url,
          maxDepth: crawlArgs.maxDepth,
          maxBreadth: crawlArgs.maxBreadth,
          limit: crawlArgs.limit,
          extractDepth: crawlArgs.extractDepth
        }
      }

      console.log(`Website crawl completed: ${results.length} pages crawled`)

      // Log crawl completion
      if (crawlParams.enableAuditLogging) {
        CrawlerAuditLogger.logCrawlComplete(summary)
      }

      return {
        content: [{ type: 'text', text: JSON.stringify(outputData, null, 2) }]
      }
    } catch (error: unknown) {
      const err = error as Error
      console.error('[AdvancedCrawler Error]', err.message)

      const errorOutput = {
        error: err.message,
        startUrl: crawlArgs.url,
        timestamp: new Date().toISOString()
      }

      return {
        content: [{ type: 'text', text: JSON.stringify(errorOutput, null, 2) }],
        isError: true
      }
    }
  }

  private async handleBatchCrawl(args: unknown) {
    const parsed = BatchCrawlArgsSchema.safeParse(args)
    if (!parsed.success) {
      throw new Error(`Invalid batch crawl parameters: ${parsed.error}`)
    }

    const batchArgs = parsed.data

    try {
      console.log(`Starting batch crawl for ${batchArgs.urls.length} URLs`)

      const crawlParams: CrawlParams = {
        maxDepth: batchArgs.crawlParams.maxDepth || 1,
        maxBreadth: batchArgs.crawlParams.maxBreadth || 10,
        limit: batchArgs.crawlParams.limit || 20,
        selectPaths: batchArgs.crawlParams.selectPaths || [],
        selectDomains: batchArgs.crawlParams.selectDomains || [],
        excludePaths: batchArgs.crawlParams.excludePaths || [],
        excludeDomains: batchArgs.crawlParams.excludeDomains || [],
        allowExternal: batchArgs.crawlParams.allowExternal || false,
        includeImages: batchArgs.crawlParams.includeImages || false,
        extractDepth: batchArgs.crawlParams.extractDepth || 'basic',
        timeout: batchArgs.crawlParams.timeout || 30,
        respectRobots: batchArgs.crawlParams.respectRobots ?? true,
        rateLimit: batchArgs.crawlParams.rateLimit || 3,
        researchMode: batchArgs.crawlParams.researchMode || false,
        minContentQuality: batchArgs.crawlParams.minContentQuality || 0.3,
        enablePIIDetection: batchArgs.crawlParams.enablePIIDetection ?? true,
        piiSanitization: batchArgs.crawlParams.piiSanitization || 'redact',
        maxPIIRisk: batchArgs.crawlParams.maxPIIRisk || 'high',
        enableAuditLogging: batchArgs.crawlParams.enableAuditLogging ?? true
      }

      // Process URLs in batches with concurrency control
      const batchSize = batchArgs.concurrency
      const allResults: Array<{ url: string; results: CrawlResult[]; errors: string[] }> = []

      for (let i = 0; i < batchArgs.urls.length; i += batchSize) {
        const batch = batchArgs.urls.slice(i, i + batchSize)

        const batchPromises = batch.map(async (url) => {
          try {
            const crawler = new AdvancedWebCrawler(crawlParams)
            const results = await crawler.crawl(url)
            return { url, results, errors: [] }
          } catch (error: unknown) {
            const err = error as Error
            console.error(`Batch crawl error for ${url}:`, err.message)
            return { url, results: [], errors: [err.message] }
          }
        })

        const batchResults = await Promise.all(batchPromises)
        allResults.push(...batchResults)
      }

      const summary = {
        totalUrls: batchArgs.urls.length,
        successfulUrls: allResults.filter((result) => result.results.length > 0).length,
        totalPages: allResults.reduce((sum, result) => sum + result.results.length, 0),
        totalErrors: allResults.reduce((sum, result) => sum + result.errors.length, 0),
        timestamp: new Date().toISOString()
      }

      const outputData = {
        summary,
        results: allResults,
        crawlParams
      }

      console.log(
        `Batch crawl completed: ${summary.totalPages} total pages from ${summary.successfulUrls}/${summary.totalUrls} URLs`
      )

      return {
        content: [{ type: 'text', text: JSON.stringify(outputData, null, 2) }]
      }
    } catch (error: unknown) {
      const err = error as Error
      console.error('[BatchCrawler Error]', err.message)

      const errorOutput = {
        error: err.message,
        urls: batchArgs.urls,
        timestamp: new Date().toISOString()
      }

      return {
        content: [{ type: 'text', text: JSON.stringify(errorOutput, null, 2) }],
        isError: true
      }
    }
  }

  private async handleGetAuditLogs(args: unknown) {
    const parsed = GetAuditLogsArgsSchema.safeParse(args)
    if (!parsed.success) {
      throw new Error(`Invalid audit logs parameters: ${parsed.error}`)
    }

    const auditArgs = parsed.data

    try {
      console.log('Retrieving crawler audit logs')

      let logs = CrawlerAuditLogger.getAuditLogs()

      // Apply filters
      if (auditArgs.filterByEvent) {
        logs = logs.filter((log) => log.event === auditArgs.filterByEvent)
      }

      if (auditArgs.filterByRiskLevel) {
        logs = logs.filter((log) => log.piiRisk === auditArgs.filterByRiskLevel)
      }

      // Limit results
      logs = logs.slice(-auditArgs.limit) // Get most recent entries

      const sessionSummary = auditArgs.includeSessionSummary
        ? CrawlerAuditLogger.getSessionSummary()
        : undefined

      const outputData = {
        sessionSummary,
        totalLogs: CrawlerAuditLogger.getAuditLogs().length,
        filteredLogs: logs.length,
        logs,
        filters: {
          event: auditArgs.filterByEvent,
          riskLevel: auditArgs.filterByRiskLevel,
          limit: auditArgs.limit
        },
        timestamp: new Date().toISOString()
      }

      console.log(`Retrieved ${logs.length} audit log entries`)

      return {
        content: [{ type: 'text', text: JSON.stringify(outputData, null, 2) }]
      }
    } catch (error: unknown) {
      const err = error as Error
      console.error('[AuditLogs Error]', err.message)

      const errorOutput = {
        error: err.message,
        timestamp: new Date().toISOString()
      }

      return {
        content: [{ type: 'text', text: JSON.stringify(errorOutput, null, 2) }],
        isError: true
      }
    }
  }
}
