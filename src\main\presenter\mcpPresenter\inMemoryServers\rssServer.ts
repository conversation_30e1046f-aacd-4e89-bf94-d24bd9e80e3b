import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport'
import axios from 'axios'
import { parseStringPromise } from 'xml2js'
import { proxyConfig } from '../../proxyConfig'
import { HttpsProxyAgent } from 'https-proxy-agent'

// Schema definitions for RSS operations
const FetchRSSFeedArgsSchema = z.object({
  url: z.string().url().describe('RSS feed URL to fetch'),
  limit: z
    .number()
    .optional()
    .default(10)
    .describe('Maximum number of items to return (1-50, default 10)'),
  includeContent: z
    .boolean()
    .optional()
    .default(false)
    .describe('Whether to include full content/description')
})

const ParseRSSContentArgsSchema = z.object({
  xmlContent: z.string().describe('Raw RSS/Atom XML content to parse'),
  limit: z
    .number()
    .optional()
    .default(10)
    .describe('Maximum number of items to return (1-50, default 10)'),
  includeContent: z
    .boolean()
    .optional()
    .default(false)
    .describe('Whether to include full content/description')
})

const SearchRSSFeedsArgsSchema = z.object({
  query: z.string().describe('Search query to filter RSS items'),
  feeds: z.array(z.string().url()).describe('Array of RSS feed URLs to search'),
  limit: z
    .number()
    .optional()
    .default(20)
    .describe('Maximum number of items to return (1-100, default 20)'),
  includeContent: z
    .boolean()
    .optional()
    .default(false)
    .describe('Whether to include full content/description')
})

const GetRSSFeedInfoArgsSchema = z.object({
  url: z.string().url().describe('RSS feed URL to get information about')
})

const ValidateRSSFeedArgsSchema = z.object({
  url: z.string().url().describe('RSS feed URL to validate')
})

// RSS item interface
interface RSSItem {
  title: string
  link: string
  description?: string
  content?: string
  pubDate?: string
  author?: string
  categories?: string[]
  guid?: string
  enclosure?: {
    url: string
    type: string
    length?: number
  }
}

// RSS feed info interface
interface RSSFeedInfo {
  title: string
  description: string
  link: string
  language?: string
  lastBuildDate?: string
  generator?: string
  managingEditor?: string
  webMaster?: string
  category?: string[]
  image?: {
    url: string
    title: string
    link: string
  }
  itemCount: number
  feedType: 'rss' | 'atom' | 'unknown'
}

export class RSSServer {
  private server: Server
  private requestCount = 0
  private lastResetTime = Date.now()
  private readonly MAX_REQUESTS_PER_MINUTE = 60

  constructor() {
    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/rss-server',
        version: '0.1.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set request handlers
    this.setupRequestHandlers()
  }

  // Start server
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  // Rate limiting check
  private checkRateLimit(): void {
    const now = Date.now()
    if (now - this.lastResetTime > 60000) {
      // Reset every minute
      this.requestCount = 0
      this.lastResetTime = now
    }

    if (this.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
      throw new Error('Rate limit exceeded. Please wait before making more requests.')
    }

    this.requestCount++
  }

  // Set request handlers
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'fetch_rss_feed',
            description:
              'Fetches and parses an RSS/Atom feed from a URL. Returns structured feed items with title, link, description, publication date, and other metadata. ' +
              'Supports RSS 2.0, RSS 1.0, and Atom feeds. Includes rate limiting and error handling.',
            inputSchema: zodToJsonSchema(FetchRSSFeedArgsSchema)
          },
          {
            name: 'parse_rss_content',
            description:
              'Parses raw RSS/Atom XML content and returns structured feed items. ' +
              'Useful when you already have RSS XML content and need to extract structured data. ' +
              'Supports RSS 2.0, RSS 1.0, and Atom feeds.',
            inputSchema: zodToJsonSchema(ParseRSSContentArgsSchema)
          },
          {
            name: 'search_rss_feeds',
            description:
              'Searches multiple RSS feeds for items matching a query. ' +
              'Fetches items from all provided feeds and filters them based on the search query. ' +
              'Searches in title, description, and content fields. Returns ranked results.',
            inputSchema: zodToJsonSchema(SearchRSSFeedsArgsSchema)
          },
          {
            name: 'get_rss_feed_info',
            description:
              'Gets metadata information about an RSS feed including title, description, language, ' +
              'last update time, item count, and feed type. Useful for feed discovery and validation.',
            inputSchema: zodToJsonSchema(GetRSSFeedInfoArgsSchema)
          },
          {
            name: 'validate_rss_feed',
            description:
              "Validates an RSS feed URL by checking if it's accessible and contains valid RSS/Atom content. " +
              'Returns validation status, feed type, and any errors encountered.',
            inputSchema: zodToJsonSchema(ValidateRSSFeedArgsSchema)
          }
        ]
      }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        const { name, arguments: args } = request.params

        switch (name) {
          case 'fetch_rss_feed': {
            const parsed = FetchRSSFeedArgsSchema.safeParse(args)
            if (!parsed.success) {
              throw new Error(`Invalid arguments for fetch_rss_feed: ${parsed.error}`)
            }

            const { url, limit, includeContent } = parsed.data
            const items = await this.fetchRSSFeed(url, limit, includeContent)

            return {
              content: [
                {
                  type: 'text',
                  text: `Successfully fetched ${items.length} items from RSS feed: ${url}`
                },
                {
                  type: 'text',
                  text: JSON.stringify(items, null, 2)
                }
              ]
            }
          }

          case 'parse_rss_content': {
            const parsed = ParseRSSContentArgsSchema.safeParse(args)
            if (!parsed.success) {
              throw new Error(`Invalid arguments for parse_rss_content: ${parsed.error}`)
            }

            const { xmlContent, limit, includeContent } = parsed.data
            const items = await this.parseRSSContent(xmlContent, limit, includeContent)

            return {
              content: [
                {
                  type: 'text',
                  text: `Successfully parsed ${items.length} items from RSS content`
                },
                {
                  type: 'text',
                  text: JSON.stringify(items, null, 2)
                }
              ]
            }
          }

          case 'search_rss_feeds': {
            const parsed = SearchRSSFeedsArgsSchema.safeParse(args)
            if (!parsed.success) {
              throw new Error(`Invalid arguments for search_rss_feeds: ${parsed.error}`)
            }

            const { query, feeds, limit, includeContent } = parsed.data
            const results = await this.searchRSSFeeds(query, feeds, limit, includeContent)

            return {
              content: [
                {
                  type: 'text',
                  text: `Found ${results.length} items matching "${query}" across ${feeds.length} feeds`
                },
                {
                  type: 'text',
                  text: JSON.stringify(results, null, 2)
                }
              ]
            }
          }

          case 'get_rss_feed_info': {
            const parsed = GetRSSFeedInfoArgsSchema.safeParse(args)
            if (!parsed.success) {
              throw new Error(`Invalid arguments for get_rss_feed_info: ${parsed.error}`)
            }

            const { url } = parsed.data
            const feedInfo = await this.getRSSFeedInfo(url)

            return {
              content: [
                {
                  type: 'text',
                  text: `RSS Feed Information for: ${url}`
                },
                {
                  type: 'text',
                  text: JSON.stringify(feedInfo, null, 2)
                }
              ]
            }
          }

          case 'validate_rss_feed': {
            const parsed = ValidateRSSFeedArgsSchema.safeParse(args)
            if (!parsed.success) {
              throw new Error(`Invalid arguments for validate_rss_feed: ${parsed.error}`)
            }

            const { url } = parsed.data
            const validation = await this.validateRSSFeed(url)

            return {
              content: [
                {
                  type: 'text',
                  text: `RSS Feed Validation for: ${url}`
                },
                {
                  type: 'text',
                  text: JSON.stringify(validation, null, 2)
                }
              ]
            }
          }

          default:
            throw new Error(`Unknown tool: ${name}`)
        }
      } catch (error) {
        console.error('RSS Server tool call error:', error)
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
            }
          ],
          isError: true
        }
      }
    })
  }

  // Fetch RSS feed from URL
  private async fetchRSSFeed(
    url: string,
    limit: number = 10,
    includeContent: boolean = false
  ): Promise<RSSItem[]> {
    this.checkRateLimit()

    try {
      const proxyUrl = proxyConfig.getProxyUrl()
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; RSS Reader; +https://docomoe.com)',
          Accept: 'application/rss+xml, application/xml, text/xml, application/atom+xml'
        },
        httpAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
        httpsAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined
      })

      return await this.parseRSSContent(response.data, limit, includeContent)
    } catch (error) {
      console.error(`Failed to fetch RSS feed ${url}:`, error)
      throw new Error(
        `Failed to fetch RSS feed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  // Parse RSS content from XML string
  private async parseRSSContent(
    xmlContent: string,
    limit: number = 10,
    includeContent: boolean = false
  ): Promise<RSSItem[]> {
    try {
      const result = await parseStringPromise(xmlContent, {
        explicitArray: false,
        ignoreAttrs: false,
        mergeAttrs: true
      })

      let items: RSSItem[] = []

      // Handle RSS 2.0 format
      if (result.rss && result.rss.channel) {
        const channel = result.rss.channel
        const rssItems = Array.isArray(channel.item) ? channel.item : [channel.item].filter(Boolean)

        items = rssItems.map((item: any) => this.parseRSSItem(item, includeContent))
      }
      // Handle Atom format
      else if (result.feed && result.feed.entry) {
        const entries = Array.isArray(result.feed.entry) ? result.feed.entry : [result.feed.entry]

        items = entries.map((entry: any) => this.parseAtomEntry(entry, includeContent))
      }
      // Handle RSS 1.0 format
      else if (result['rdf:RDF'] && result['rdf:RDF'].item) {
        const rdfItems = Array.isArray(result['rdf:RDF'].item)
          ? result['rdf:RDF'].item
          : [result['rdf:RDF'].item]

        items = rdfItems.map((item: any) => this.parseRSSItem(item, includeContent))
      }

      // Apply limit and return
      return items.slice(0, Math.min(limit, 50))
    } catch (error) {
      console.error('Failed to parse RSS content:', error)
      throw new Error(
        `Failed to parse RSS content: ${error instanceof Error ? error.message : 'Invalid XML format'}`
      )
    }
  }

  // Parse RSS 2.0/1.0 item
  private parseRSSItem(item: any, includeContent: boolean): RSSItem {
    const rssItem: RSSItem = {
      title: this.cleanText(item.title || ''),
      link: item.link || item.guid || '',
      description: includeContent ? this.cleanText(item.description || '') : undefined,
      pubDate: item.pubDate || item['dc:date'] || undefined,
      author: item.author || item['dc:creator'] || undefined,
      guid: item.guid || undefined
    }

    // Handle categories
    if (item.category) {
      rssItem.categories = Array.isArray(item.category)
        ? item.category.map((cat: any) => (typeof cat === 'string' ? cat : cat._))
        : [typeof item.category === 'string' ? item.category : item.category._]
    }

    // Handle enclosures (media attachments)
    if (item.enclosure) {
      rssItem.enclosure = {
        url: item.enclosure.url,
        type: item.enclosure.type,
        length: item.enclosure.length ? parseInt(item.enclosure.length) : undefined
      }
    }

    // Handle content:encoded for full content
    if (includeContent && item['content:encoded']) {
      rssItem.content = this.cleanText(item['content:encoded'])
    }

    return rssItem
  }

  // Parse Atom entry
  private parseAtomEntry(entry: any, includeContent: boolean): RSSItem {
    const atomItem: RSSItem = {
      title: this.cleanText(entry.title || ''),
      link: entry.link ? (Array.isArray(entry.link) ? entry.link[0].href : entry.link.href) : '',
      description: includeContent ? this.cleanText(entry.summary || '') : undefined,
      pubDate: entry.published || entry.updated || undefined,
      author: entry.author ? entry.author.name || entry.author.email : undefined,
      guid: entry.id || undefined
    }

    // Handle categories
    if (entry.category) {
      atomItem.categories = Array.isArray(entry.category)
        ? entry.category.map((cat: any) => cat.term || cat.label)
        : [entry.category.term || entry.category.label]
    }

    // Handle content
    if (includeContent && entry.content) {
      atomItem.content = this.cleanText(entry.content._ || entry.content)
    }

    return atomItem
  }

  // Search across multiple RSS feeds
  private async searchRSSFeeds(
    query: string,
    feeds: string[],
    limit: number = 20,
    includeContent: boolean = false
  ): Promise<RSSItem[]> {
    const allItems: RSSItem[] = []
    const searchTerms = query.toLowerCase().split(' ')

    // Fetch items from all feeds
    for (const feedUrl of feeds) {
      try {
        const items = await this.fetchRSSFeed(feedUrl, 50, includeContent) // Get more items for better search results
        allItems.push(...items)
      } catch (error) {
        console.warn(`Failed to fetch feed ${feedUrl}:`, error)
        // Continue with other feeds
      }
    }

    // Filter and rank items based on search query
    const filteredItems = allItems.filter((item) => {
      const searchText =
        `${item.title} ${item.description || ''} ${item.content || ''}`.toLowerCase()
      return searchTerms.some((term) => searchText.includes(term))
    })

    // Sort by relevance (simple scoring based on term matches)
    const scoredItems = filteredItems.map((item) => {
      let score = 0

      searchTerms.forEach((term) => {
        const titleMatches = (item.title.toLowerCase().match(new RegExp(term, 'g')) || []).length
        const descMatches = (
          (item.description || '').toLowerCase().match(new RegExp(term, 'g')) || []
        ).length
        const contentMatches = (
          (item.content || '').toLowerCase().match(new RegExp(term, 'g')) || []
        ).length

        score += titleMatches * 3 + descMatches * 2 + contentMatches * 1
      })

      return { item, score }
    })

    // Sort by score and return top results
    return scoredItems
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.min(limit, 100))
      .map((scored) => scored.item)
  }

  // Get RSS feed information
  private async getRSSFeedInfo(url: string): Promise<RSSFeedInfo> {
    this.checkRateLimit()

    try {
      const proxyUrl = proxyConfig.getProxyUrl()
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; RSS Reader; +https://docomoe.com)',
          Accept: 'application/rss+xml, application/xml, text/xml, application/atom+xml'
        },
        httpAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
        httpsAgent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined
      })

      const result = await parseStringPromise(response.data, {
        explicitArray: false,
        ignoreAttrs: false,
        mergeAttrs: true
      })

      let feedInfo: RSSFeedInfo

      // Handle RSS format
      if (result.rss && result.rss.channel) {
        const channel = result.rss.channel
        const items = Array.isArray(channel.item) ? channel.item : [channel.item].filter(Boolean)

        feedInfo = {
          title: this.cleanText(channel.title || ''),
          description: this.cleanText(channel.description || ''),
          link: channel.link || '',
          language: channel.language || undefined,
          lastBuildDate: channel.lastBuildDate || channel.pubDate || undefined,
          generator: channel.generator || undefined,
          managingEditor: channel.managingEditor || undefined,
          webMaster: channel.webMaster || undefined,
          category: channel.category
            ? Array.isArray(channel.category)
              ? channel.category
              : [channel.category]
            : undefined,
          itemCount: items.length,
          feedType: 'rss'
        }

        // Handle image
        if (channel.image) {
          feedInfo.image = {
            url: channel.image.url || '',
            title: channel.image.title || '',
            link: channel.image.link || ''
          }
        }
      }
      // Handle Atom format
      else if (result.feed) {
        const feed = result.feed
        const entries = Array.isArray(feed.entry) ? feed.entry : [feed.entry].filter(Boolean)

        feedInfo = {
          title: this.cleanText(feed.title || ''),
          description: this.cleanText(feed.subtitle || ''),
          link: feed.link ? (Array.isArray(feed.link) ? feed.link[0].href : feed.link.href) : '',
          language: feed['xml:lang'] || undefined,
          lastBuildDate: feed.updated || undefined,
          generator: feed.generator ? feed.generator._ || feed.generator : undefined,
          itemCount: entries.length,
          feedType: 'atom'
        }
      } else {
        throw new Error('Unrecognized feed format')
      }

      return feedInfo
    } catch (error) {
      console.error(`Failed to get RSS feed info for ${url}:`, error)
      throw new Error(
        `Failed to get feed information: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  // Validate RSS feed
  private async validateRSSFeed(
    url: string
  ): Promise<{ isValid: boolean; feedType?: string; error?: string; itemCount?: number }> {
    this.checkRateLimit()

    try {
      const feedInfo = await this.getRSSFeedInfo(url)
      return {
        isValid: true,
        feedType: feedInfo.feedType,
        itemCount: feedInfo.itemCount
      }
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown validation error'
      }
    }
  }

  // Clean text content (remove HTML tags, decode entities, trim whitespace)
  private cleanText(text: string): string {
    if (!text) return ''

    return text
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
  }
}
