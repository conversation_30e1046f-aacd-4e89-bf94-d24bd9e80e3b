# File and Directory Access Architecture

## Overview

This document outlines the architecture for transitioning from MCP-only file service access to a hybrid approach that combines Electron APIs for user-initiated operations with MCP for LLM file operations, while maintaining security separation of concerns and workspace context within the app file domain.

## Current State Analysis

### Existing Architecture
- **MCP File Service**: In-memory filesystem server (`buildInFileSystem`) providing file operations via MCP protocol
- **Presenter Pattern**: Main-renderer IPC communication using proxy-based presenter pattern
- **File Operations**: Currently handled through MCP filesystem server for LLM access and limited Electron dialog usage
- **Security**: Path validation and allowed directories restriction in MCP server
- **Context Isolation**: Enabled in preload with limited Electron API exposure

### Current Components
- **MCP Filesystem Server**: `src/main/presenter/mcpPresenter/inMemoryServers/filesystem.ts`
- **FilePresenter**: `src/main/presenter/filePresenter/FilePresenter.ts` (file processing/adaptation)
- **DevicePresenter**: `src/main/presenter/devicePresenter/index.ts` (limited directory selection)
- **FileMenu Component**: `src/renderer/shell/components/app-bar/FileMenu.vue` (placeholder UI)

## Proposed Hybrid Architecture

### Design Principles
1. **Separation of Concerns**: User operations vs LLM operations
2. **Security First**: Workspace-scoped access with explicit user consent
3. **Unidirectional Data Flow**: Prevent circular dependencies through event-driven communication
4. **Backward Compatibility**: Preserve existing MCP functionality
5. **Progressive Enhancement**: Incremental implementation without breaking changes

### Architecture Components

#### 1. WorkspacePresenter (New)
**Purpose**: Central coordinator for file/directory operations and workspace management

**Responsibilities**:
- File and folder dialog management using Electron APIs
- Workspace context establishment and maintenance
- Recent files/workspaces tracking
- Bridge between user operations and system components
- Event emission for workspace state changes

**Key Methods**:
- `openFileDialog(options)`: Multi-file selection with filters
- `openFolderDialog(options)`: Directory selection for workspace
- `setWorkspace(path)`: Establish workspace context
- `getWorkspace()`: Retrieve current workspace
- `getRecentFiles()`: Recent file access history
- `getRecentWorkspaces()`: Recent workspace history
- `validateWorkspacePath(path)`: Security validation

#### 2. Enhanced FilePresenter
**Purpose**: File processing and adaptation with workspace awareness

**New Responsibilities**:
- Workspace-scoped file operations
- Integration with WorkspacePresenter for context
- Enhanced security validation
- File metadata enrichment with workspace context

#### 3. Workspace-Aware MCP Filesystem Server
**Purpose**: LLM file operations within workspace boundaries

**Enhancements**:
- Dynamic allowed directories based on workspace context
- Event-driven workspace boundary updates
- Enhanced security with workspace-scoped operations
- Backward compatibility with existing MCP tools

#### 4. Enhanced FileMenu Component
**Purpose**: User interface for file/directory operations

**New Features**:
- File/folder picker integration
- Workspace management UI
- Recent items display
- Workspace status indication

### Data Flow Architecture

#### User-Initiated Operations Flow
```
User Action (FileMenu)
    → WorkspacePresenter (Electron APIs)
    → Workspace State Update
    → Event Emission (workspace:changed)
    → UI State Update
    → MCP Context Update (event-driven)
```

#### LLM-Initiated Operations Flow
```
LLM Request
    → MCP Filesystem Server
    → Workspace Boundary Validation
    → File Operation (within workspace)
    → Response to LLM
```

#### Workspace Context Flow
```
Workspace Selection
    → WorkspacePresenter.setWorkspace()
    → State Persistence
    → Event: workspace:changed
    → MCP Server Updates Allowed Directories
    → UI Updates Workspace Status
```

## Security Model

### Access Control Layers
1. **User Consent Layer**: Explicit user action required for file system access
2. **Workspace Boundary Layer**: All operations scoped to current workspace
3. **Path Validation Layer**: Security validation of all file paths
4. **MCP Restriction Layer**: LLM operations limited to workspace context

### Security Boundaries
- **User Operations**: Full Electron API access within workspace
- **LLM Operations**: MCP-restricted access within workspace
- **Cross-Boundary**: Event-driven communication only
- **Persistence**: Workspace context stored securely

## Event System Design

### Event Categories
- **Workspace Events**: `workspace:changed`, `workspace:cleared`
- **File Events**: `file:opened`, `file:recent-updated`
- **Security Events**: `security:boundary-violation`, `security:access-denied`

### Event Flow Prevention of Circular Dependencies
- **One-Way Communication**: Components emit events but don't directly import each other
- **Event Bus Mediation**: Central event bus prevents direct dependencies
- **State Management**: Centralized state prevents circular updates

## Type System Architecture

### Consolidated Type Definitions
**Location**: `src/shared/presenter.d.ts` (single source of truth)

**New Interfaces**:
- `IWorkspacePresenter`: Workspace management operations
- `WorkspaceState`: Current workspace context
- `FileDialogOptions`: File/folder dialog configuration
- `WorkspaceSecurityContext`: Security boundary definitions
- `RecentItem`: Recent files/workspaces structure

### Type Safety Measures
- **Interface Segregation**: Separate interfaces for different concerns
- **Dependency Injection**: Interfaces for testability and flexibility
- **Event Type Safety**: Strongly typed event payloads

## Implementation Strategy

### Phase 1: Foundation (MVP)
**Scope**: Basic workspace management and file dialogs

**Deliverables**:
- WorkspacePresenter implementation
- Enhanced FileMenu component
- Basic workspace state management
- File/folder dialog integration

**Success Criteria**:
- User can select workspace directory
- File/folder dialogs functional
- Workspace context persisted

### Phase 2: Integration
**Scope**: MCP integration and security enhancement

**Deliverables**:
- MCP filesystem server workspace awareness
- Event-driven communication system
- Security boundary enforcement
- Recent items tracking

**Success Criteria**:
- MCP operations scoped to workspace
- Security boundaries enforced
- No circular dependencies

### Phase 3: Enhancement
**Scope**: Advanced features and optimization

**Deliverables**:
- File explorer component
- Advanced workspace management
- Performance optimization
- Comprehensive testing

**Success Criteria**:
- Full file navigation within workspace
- Optimal performance
- Comprehensive test coverage

## Development Guidelines

### Dependency Management
- **Avoid Direct Imports**: Use event-driven communication between major components
- **Interface-Based Design**: Program to interfaces, not implementations
- **Dependency Injection**: Constructor injection for testability

### Security Considerations
- **Principle of Least Privilege**: Minimal necessary access rights
- **Explicit User Consent**: Clear user actions for file system access
- **Workspace Scoping**: All operations within defined boundaries
- **Audit Trail**: Logging of security-relevant operations

### Testing Strategy
- **Unit Testing**: Individual component testing with mocks
- **Integration Testing**: Component interaction testing
- **Security Testing**: Boundary violation and access control testing
- **User Acceptance Testing**: End-to-end workflow validation

### Performance Considerations
- **Lazy Loading**: Load workspace context on demand
- **Caching**: Cache recent items and workspace metadata
- **Event Debouncing**: Prevent excessive event firing
- **Memory Management**: Proper cleanup of file handles and watchers

## Migration Strategy

### Backward Compatibility
- **Existing MCP Tools**: Continue to function within workspace context
- **API Compatibility**: Maintain existing presenter interfaces
- **Configuration Migration**: Automatic migration of existing settings

### Rollout Plan
- **Feature Flags**: Gradual feature enablement
- **A/B Testing**: Compare hybrid vs MCP-only approaches
- **Monitoring**: Performance and error monitoring
- **Rollback Plan**: Quick reversion capability if issues arise

## Monitoring and Maintenance

### Key Metrics
- **File Operation Performance**: Response times for file operations
- **Security Violations**: Attempted boundary violations
- **User Adoption**: Usage of new file management features
- **Error Rates**: File operation failure rates

### Maintenance Procedures
- **Regular Security Audits**: Periodic security boundary testing
- **Performance Monitoring**: Continuous performance tracking
- **User Feedback Integration**: Regular user experience assessment
- **Documentation Updates**: Keep documentation current with changes

## Technical Specifications

### File Dialog Configuration
**Supported Operations**:
- Single/multiple file selection
- Directory selection with creation option
- File type filtering and validation
- Recent locations integration

**Dialog Options**:
- File filters by extension and MIME type
- Default directory (workspace-aware)
- Multi-selection capabilities
- Custom button labels and titles

### Workspace Management Specifications
**Workspace Definition**:
- Root directory path
- Workspace metadata (name, description, created date)
- Security context and permissions
- Recent files within workspace

**Workspace Persistence**:
- Electron Store integration for workspace settings
- Recent workspace history (configurable limit)
- Workspace-specific preferences
- Cross-session workspace restoration

### MCP Integration Specifications
**Dynamic Directory Management**:
- Real-time allowed directory updates
- Workspace boundary enforcement
- Path normalization and validation
- Symbolic link handling

**Tool Compatibility**:
- Existing MCP tools continue to function
- Workspace-scoped tool operations
- Enhanced error messaging for boundary violations
- Graceful degradation for unsupported operations

## Error Handling and Recovery

### Error Categories
**User Errors**:
- Invalid file/directory selection
- Permission denied scenarios
- Workspace boundary violations
- File system access failures

**System Errors**:
- MCP server communication failures
- File system corruption or unavailability
- Memory or resource exhaustion
- Network-related file access issues

### Recovery Strategies
**Graceful Degradation**:
- Fallback to MCP-only mode if Electron APIs fail
- Alternative file access methods
- User notification of limited functionality
- Automatic retry mechanisms

**Error Reporting**:
- Structured error logging
- User-friendly error messages
- Developer debugging information
- Error analytics and monitoring

## Configuration Management

### Workspace Configuration
**Settings Structure**:
- Current workspace path
- Workspace history and metadata
- File type associations
- Security preferences

**Configuration Storage**:
- Electron Store for persistence
- JSON schema validation
- Migration scripts for version updates
- Backup and restore capabilities

### Security Configuration
**Access Control Settings**:
- Allowed file extensions
- Restricted directory patterns
- User consent requirements
- Audit logging preferences

**Boundary Definitions**:
- Workspace root restrictions
- Symbolic link policies
- Network drive access rules
- Temporary file handling

## Integration Points

### Existing System Integration
**Presenter Pattern Integration**:
- Seamless integration with existing presenter architecture
- Consistent IPC communication patterns
- Shared type definitions and interfaces
- Event bus compatibility

**UI Component Integration**:
- Vue.js component architecture compatibility
- Tailwind CSS styling consistency
- Icon system integration
- Responsive design considerations

### External Tool Integration
**MCP Server Compatibility**:
- Existing MCP tool support
- Third-party MCP server integration
- Custom MCP tool development guidelines
- Version compatibility matrix

**File System Integration**:
- Operating system file dialog integration
- Native file manager integration
- File association handling
- Context menu integration

## Development Workflow

### Development Environment Setup
**Prerequisites**:
- Node.js and pnpm package manager
- Electron development tools
- TypeScript compiler
- Vue.js development tools

**Local Development**:
- Hot reload for renderer process changes
- Main process restart for presenter changes
- Type checking and linting integration
- Automated testing execution

### Code Organization
**Directory Structure**:
- Presenter implementations in `src/main/presenter/`
- Shared types in `src/shared/`
- UI components in `src/renderer/`
- Documentation in `docs/`

**Naming Conventions**:
- PascalCase for classes and interfaces
- camelCase for methods and properties
- kebab-case for file names
- UPPER_CASE for constants

### Quality Assurance

### Code Review Process
**Review Criteria**:
- Security boundary compliance
- Performance impact assessment
- Type safety verification
- Documentation completeness

**Automated Checks**:
- TypeScript compilation
- ESLint rule compliance
- Unit test coverage
- Security vulnerability scanning

### Testing Requirements
**Test Coverage Targets**:
- Unit tests: 90% code coverage
- Integration tests: Critical path coverage
- Security tests: Boundary violation scenarios
- Performance tests: File operation benchmarks

**Test Categories**:
- Presenter unit tests
- Component integration tests
- End-to-end workflow tests
- Security boundary tests

## Deployment and Release

### Release Strategy
**Version Management**:
- Semantic versioning for releases
- Feature flag management
- Backward compatibility guarantees
- Migration script execution

**Deployment Pipeline**:
- Automated build and test execution
- Security scanning and validation
- Performance regression testing
- Staged rollout to user segments

### Monitoring and Observability
**Application Metrics**:
- File operation performance metrics
- Error rate and type tracking
- User interaction analytics
- Resource utilization monitoring

**Alerting and Notifications**:
- Critical error notifications
- Performance degradation alerts
- Security violation warnings
- System health monitoring

## Future Considerations

### Scalability Planning
**Performance Optimization**:
- File operation caching strategies
- Lazy loading of workspace content
- Background file indexing
- Memory usage optimization

**Feature Expansion**:
- Advanced file search capabilities
- File versioning and history
- Collaborative workspace features
- Cloud storage integration

### Technology Evolution
**Framework Updates**:
- Electron version upgrade planning
- Vue.js framework evolution
- TypeScript language updates
- Security standard compliance

**API Evolution**:
- Presenter interface versioning
- MCP protocol updates
- File system API changes
- Cross-platform compatibility
