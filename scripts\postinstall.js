console.log('postinstall')

// Install Puppeteer Chrome browser if not already installed
import { execSync } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

try {
  // Check if Chrome is already installed for Puppeteer
  const puppeteerPath = path.join(__dirname, '..', 'node_modules', 'puppeteer')
  if (fs.existsSync(puppeteerPath)) {
    console.log('Installing Chrome browser for Puppeteer...')
    execSync('pnpm exec puppeteer browsers install chrome', {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    })
    console.log('Chrome browser installed successfully for Puppeteer')
  }
} catch (error) {
  console.warn('Warning: Failed to install Chrome browser for Puppeteer:', error.message)
  console.warn('You may need to run "pnpm exec puppeteer browsers install chrome" manually')
}
