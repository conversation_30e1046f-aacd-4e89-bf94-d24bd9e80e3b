import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js'
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ErrorCode
} from '@modelcontextprotocol/sdk/types.js'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'

// Tool argument schemas
const NavigateArgsSchema = z.object({
  url: z.string().url().describe('URL to navigate to'),
  waitFor: z
    .enum(['load', 'domcontentloaded', 'networkidle0', 'networkidle2'])
    .optional()
    .default('load')
    .describe('Wait condition'),
  timeout: z.number().optional().default(30000).describe('Navigation timeout in milliseconds')
})

const ExtractContentArgsSchema = z.object({
  selector: z.string().optional().describe('CSS selector to extract content from (optional)'),
  attribute: z.string().optional().describe('Attribute to extract (e.g., "href", "src")'),
  multiple: z.boolean().optional().default(false).describe('Extract multiple elements'),
  textOnly: z.boolean().optional().default(true).describe('Extract text content only')
})

const ClickElementArgsSchema = z.object({
  selector: z.string().describe('CSS selector of element to click'),
  waitForNavigation: z
    .boolean()
    .optional()
    .default(false)
    .describe('Wait for navigation after click'),
  timeout: z.number().optional().default(5000).describe('Click timeout in milliseconds')
})

const FillFormArgsSchema = z.object({
  selector: z.string().describe('CSS selector of input field'),
  value: z.string().describe('Value to fill'),
  clear: z.boolean().optional().default(true).describe('Clear field before filling')
})

const ScreenshotArgsSchema = z.object({
  fullPage: z.boolean().optional().default(false).describe('Take full page screenshot'),
  quality: z.number().optional().default(80).describe('JPEG quality (1-100)'),
  format: z.enum(['png', 'jpeg']).optional().default('png').describe('Image format'),
  saveToFile: z.boolean().optional().default(false).describe('Save screenshot to file (optional)'),
  filename: z.string().optional().describe('Custom filename for saved screenshot (optional)'),
  selector: z.string().optional().describe('CSS selector to screenshot specific element (optional)')
})

const WaitForElementArgsSchema = z.object({
  selector: z.string().describe('CSS selector to wait for'),
  timeout: z.number().optional().default(10000).describe('Wait timeout in milliseconds'),
  visible: z.boolean().optional().default(true).describe('Wait for element to be visible')
})

const ExecuteScriptArgsSchema = z.object({
  script: z.string().describe('JavaScript code to execute'),
  args: z.array(z.any()).optional().describe('Arguments to pass to the script')
})

/**
 * Rate limiter for browser operations
 */
class BrowserRateLimiter {
  private lastRequestTime: number = 0
  private readonly minInterval: number

  constructor(intervalMs: number = 1000) {
    this.minInterval = intervalMs
  }

  async acquire(): Promise<void> {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime

    if (timeSinceLastRequest < this.minInterval) {
      const waitTime = this.minInterval - timeSinceLastRequest
      await new Promise((resolve) => setTimeout(resolve, waitTime))
    }

    this.lastRequestTime = Date.now()
  }
}

/**
 * Browser session manager
 */
class BrowserSessionManager {
  private browser: Browser | null = null
  private page: Page | null = null
  private rateLimiter: BrowserRateLimiter
  private sessionTimeout: NodeJS.Timeout | null = null
  private readonly SESSION_TIMEOUT_MS = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.rateLimiter = new BrowserRateLimiter(1000) // 1 second between operations
  }

  async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      try {
        this.browser = await puppeteer.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ],
          // Explicitly set timeout for browser launch
          timeout: 30000
        })
      } catch (error) {
        throw new Error(
          `Failed to launch browser: ${error instanceof Error ? error.message : 'Unknown error'}. Make sure Chrome is installed by running: npx puppeteer browsers install chrome`
        )
      }
    }
    return this.browser
  }

  async getPage(): Promise<Page> {
    if (!this.page) {
      const browser = await this.getBrowser()
      this.page = await browser.newPage()

      // Set user agent
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      )

      // Set viewport
      await this.page.setViewport({ width: 1920, height: 1080 })
    }

    this.resetSessionTimeout()
    return this.page
  }

  private resetSessionTimeout(): void {
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout)
    }

    this.sessionTimeout = setTimeout(() => {
      this.cleanup()
    }, this.SESSION_TIMEOUT_MS)
  }

  async cleanup(): Promise<void> {
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout)
      this.sessionTimeout = null
    }

    if (this.page) {
      await this.page.close()
      this.page = null
    }

    if (this.browser) {
      await this.browser.close()
      this.browser = null
    }
  }

  async withRateLimit<T>(operation: () => Promise<T>): Promise<T> {
    await this.rateLimiter.acquire()
    return await operation()
  }
}

/**
 * Puppeteer MCP Server for advanced browser automation
 */
export class PuppeteerServer {
  private server: Server
  private sessionManager: BrowserSessionManager

  constructor() {
    this.sessionManager = new BrowserSessionManager()

    // Create server instance
    this.server = new Server(
      {
        name: 'docomoe-inmemory/puppeteer-server',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // Set up cleanup on process exit
    process.on('exit', () => this.cleanup())
    process.on('SIGINT', () => {
      this.cleanup()
      process.exit()
    })

    // Set request handlers
    this.setupRequestHandlers()
  }

  private async cleanup(): Promise<void> {
    await this.sessionManager.cleanup()
  }

  // Set request handlers
  private setupRequestHandlers(): void {
    // Set tool list handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'navigate',
            description: 'Navigate to a URL and wait for page load',
            inputSchema: zodToJsonSchema(NavigateArgsSchema)
          },
          {
            name: 'extract_content',
            description: 'Extract content from the current page using CSS selectors',
            inputSchema: zodToJsonSchema(ExtractContentArgsSchema)
          },
          {
            name: 'click_element',
            description: 'Click an element on the page',
            inputSchema: zodToJsonSchema(ClickElementArgsSchema)
          },
          {
            name: 'fill_form',
            description: 'Fill a form field with a value',
            inputSchema: zodToJsonSchema(FillFormArgsSchema)
          },
          {
            name: 'take_screenshot',
            description:
              'Take a screenshot of the current page, specific elements, or save to file. Returns base64 image data.',
            inputSchema: zodToJsonSchema(ScreenshotArgsSchema)
          },
          {
            name: 'wait_for_element',
            description: 'Wait for an element to appear on the page',
            inputSchema: zodToJsonSchema(WaitForElementArgsSchema)
          },
          {
            name: 'execute_script',
            description: 'Execute JavaScript code on the page',
            inputSchema: zodToJsonSchema(ExecuteScriptArgsSchema)
          }
        ]
      }
    })

    // Set tool call handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params

      try {
        switch (name) {
          case 'navigate':
            return await this.handleNavigate(args)
          case 'extract_content':
            return await this.handleExtractContent(args)
          case 'click_element':
            return await this.handleClickElement(args)
          case 'fill_form':
            return await this.handleFillForm(args)
          case 'take_screenshot':
            return await this.handleTakeScreenshot(args)
          case 'wait_for_element':
            return await this.handleWaitForElement(args)
          case 'execute_script':
            return await this.handleExecuteScript(args)
          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${errorMessage}`)
      }
    })
  }

  private async handleNavigate(args: unknown) {
    const parsed = NavigateArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      await page.goto(parsed.url, {
        waitUntil: parsed.waitFor,
        timeout: parsed.timeout
      })

      const title = await page.title()
      const url = page.url()

      return {
        content: [
          {
            type: 'text',
            text: `Successfully navigated to: ${url}\nPage title: ${title}`
          }
        ]
      }
    })
  }

  private async handleExtractContent(args: unknown) {
    const parsed = ExtractContentArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      let result: any

      if (parsed.selector) {
        if (parsed.multiple) {
          result = await page.$$eval(
            parsed.selector,
            (elements, attribute, textOnly) => {
              return elements.map((el) => {
                if (attribute) {
                  return el.getAttribute(attribute)
                }
                return textOnly ? el.textContent?.trim() : el.innerHTML
              })
            },
            parsed.attribute,
            parsed.textOnly
          )
        } else {
          result = await page.$eval(
            parsed.selector,
            (element, attribute, textOnly) => {
              if (attribute) {
                return element.getAttribute(attribute)
              }
              return textOnly ? element.textContent?.trim() : element.innerHTML
            },
            parsed.attribute,
            parsed.textOnly
          )
        }
      } else {
        // Extract full page content
        result = await page.evaluate(() => document.body.textContent?.trim())
      }

      return {
        content: [
          {
            type: 'text',
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
          }
        ]
      }
    })
  }

  private async handleClickElement(args: unknown) {
    const parsed = ClickElementArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      await page.waitForSelector(parsed.selector, { timeout: parsed.timeout })

      if (parsed.waitForNavigation) {
        await Promise.all([page.waitForNavigation(), page.click(parsed.selector)])
      } else {
        await page.click(parsed.selector)
      }

      return {
        content: [
          {
            type: 'text',
            text: `Successfully clicked element: ${parsed.selector}`
          }
        ]
      }
    })
  }

  private async handleFillForm(args: unknown) {
    const parsed = FillFormArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      await page.waitForSelector(parsed.selector)

      if (parsed.clear) {
        await page.click(parsed.selector, { clickCount: 3 })
      }

      await page.type(parsed.selector, parsed.value)

      return {
        content: [
          {
            type: 'text',
            text: `Successfully filled form field: ${parsed.selector} with value: ${parsed.value}`
          }
        ]
      }
    })
  }

  private async handleTakeScreenshot(args: unknown) {
    const parsed = ScreenshotArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      // Wait for page to be ready
      try {
        await page.waitForSelector('body', { timeout: 5000 })
      } catch (error) {
        // Page might not have body element, continue anyway
      }

      const screenshotOptions: any = {
        fullPage: parsed.fullPage,
        quality: parsed.format === 'jpeg' ? parsed.quality : undefined,
        type: parsed.format,
        encoding: 'base64'
      }

      let screenshotTarget: any = page
      let screenshotDescription = `${parsed.format}, ${parsed.fullPage ? 'full page' : 'viewport'}`

      // Handle element-specific screenshot
      if (parsed.selector) {
        try {
          await page.waitForSelector(parsed.selector, { timeout: 5000 })
          const element = await page.$(parsed.selector)
          if (element) {
            screenshotTarget = element
            screenshotDescription = `${parsed.format}, element: ${parsed.selector}`
          } else {
            throw new Error(`Element not found: ${parsed.selector}`)
          }
        } catch (error) {
          // Fallback to full page screenshot if element not found
          console.warn(
            `Element screenshot failed for ${parsed.selector}, falling back to full page`
          )
          screenshotTarget = page
          screenshotDescription = `${parsed.format}, full page (element ${parsed.selector} not found)`
        }
      }

      // Take screenshot with error handling
      let screenshot: string
      try {
        screenshot = await screenshotTarget.screenshot(screenshotOptions)
      } catch (error) {
        throw new Error(
          `Failed to take screenshot: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }

      const content: any[] = [
        {
          type: 'text',
          text: `Screenshot taken successfully (${screenshotDescription})`
        }
      ]

      // Handle file saving if requested
      if (parsed.saveToFile) {
        try {
          const fs = await import('fs')
          const path = await import('path')
          const os = await import('os')

          // Create screenshots directory in user's home directory
          const userHome = os.homedir()
          const screenshotsDir = path.join(userHome, 'screenshots')

          // Ensure directory exists
          if (!fs.existsSync(screenshotsDir)) {
            fs.mkdirSync(screenshotsDir, { recursive: true })
          }

          // Generate filename
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
          const defaultFilename = `screenshot-${timestamp}.${parsed.format}`
          const filename = parsed.filename || defaultFilename
          const filepath = path.join(screenshotsDir, filename)

          // Save file
          const buffer = Buffer.from(screenshot, 'base64')
          fs.writeFileSync(filepath, buffer)

          content[0].text += `\nSaved to: ${filepath}`
        } catch (error) {
          content.push({
            type: 'text',
            text: `Warning: Failed to save screenshot to file: ${error instanceof Error ? error.message : 'Unknown error'}`
          })
        }
      }

      // Always include the image data
      content.push({
        type: 'image',
        data: screenshot,
        mimeType: `image/${parsed.format}`
      })

      return { content }
    })
  }

  private async handleWaitForElement(args: unknown) {
    const parsed = WaitForElementArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      await page.waitForSelector(parsed.selector, {
        timeout: parsed.timeout,
        visible: parsed.visible
      })

      return {
        content: [
          {
            type: 'text',
            text: `Element found: ${parsed.selector}`
          }
        ]
      }
    })
  }

  private async handleExecuteScript(args: unknown) {
    const parsed = ExecuteScriptArgsSchema.parse(args)

    return await this.sessionManager.withRateLimit(async () => {
      const page = await this.sessionManager.getPage()

      // Execute the script with arguments
      const result = await page.evaluate(
        (script: string, scriptArgs: any[]) => {
          // Create a function from the script and execute it with arguments
          const func = new Function('...args', script)
          return func(...scriptArgs)
        },
        parsed.script,
        parsed.args || []
      )

      return {
        content: [
          {
            type: 'text',
            text: `Script executed successfully. Result: ${JSON.stringify(result, null, 2)}`
          }
        ]
      }
    })
  }

  // Start server - required method for in-memory servers
  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }
}
