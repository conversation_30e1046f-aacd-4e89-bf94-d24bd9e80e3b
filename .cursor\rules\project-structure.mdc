---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This project is a desktop application based on Electron + Vue 3. The main directory structure is as follows:

- `src/`: Source code directory
  - `main/`: Electron main process code
  - `renderer/`: Renderer process code (Vue 3 app)
  - `shared/`: Type definitions and tools shared by main process and renderer process

- `resources/`: Application resource files
- `build/`: Build-related configuration
- `scripts/`: Build and development scripts
- `docs/`: Project documentation
- `tests/`: Test files

Main configuration files:
- [electron.vite.config.ts](mdc:electron.vite.config.ts): Vite build configuration
- [electron-builder.yml](mdc:electron-builder.yml): Electron Builder configuration
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration
- [tailwind.config.js](mdc:tailwind.config.js): Tailwind CSS configuration

Development guidelines:
1. All new features should be developed in the `src` directory
2. Shared type definitions should be placed in the `shared` directory
3. Main process code should be placed in `src/main`
4. Renderer process code should be placed in `src/renderer`
5. Test files should be placed in the `tests` directory
