import { Tray, Menu, app, nativeImage, NativeImage } from 'electron'
import path from 'path'
import { getContextMenuLabels } from '@shared/i18n'
import { presenter } from '.'
import { eventBus } from '@/eventbus'
import { TRAY_EVENTS } from '@/events'

export class TrayPresenter {
  private tray: Tray | null = null
  private iconPath: string

  constructor() {
    this.iconPath = path.join(app.getAppPath(), 'resources')
  }

  private createTray() {
    // Choose different icon based on platform
    let image: NativeImage | undefined = undefined
    if (process.platform === 'darwin') {
      image = nativeImage.createFromPath(path.join(this.iconPath, 'macTrayTemplate.png'))
      image = image.resize({ width: 24, height: 24 })
      image.setTemplateImage(true)
    } else {
      image = nativeImage.createFromPath(path.join(this.iconPath, 'win_tray.ico'))
    }
    this.tray = new Tray(image)
    this.tray.setToolTip('Docomoe')

    // Get current system language
    const locale = presenter.configPresenter.getLanguage?.() || 'en-US'
    const labels = getContextMenuLabels(locale)
    const contextMenu = Menu.buildFromTemplate([
      {
        label: labels.open || 'Open',
        click: () => {
          eventBus.emit(TRAY_EVENTS.SHOW_HIDDEN_WINDOW)
        }
      },
      {
        label: labels.quit || 'Quit',
        click: () => {
          app.quit()
        }
      }
    ])

    this.tray.setContextMenu(contextMenu)

    // Click tray icon to show window
    this.tray.on('click', () => {
      eventBus.emit(TRAY_EVENTS.SHOW_HIDDEN_WINDOW, true)
    })
  }

  public init(): void {
    this.createTray()
  }

  destroy() {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }
  }
}
