# Docomoe Data Synchronization Documentation

## Functional Overview

The data synchronization feature allows users to back up the application data (including chat history and configuration information) to a specified sync folder and import these data on other devices. This feature is mainly used in the following scenarios:

1. Data backup: Prevent data loss
2. Cross-device synchronization: Share the same chat history and configuration on multiple devices
3. Migrate data: Migrate data when changing devices or reinstalling the system

## Technical Implementation

### Core Components

1. **SyncPresenter**: The main class responsible for handling synchronization logic, including backup and import functions
2. **ConfigPresenter Extension**: Added synchronization-related configuration items
3. **DataSettings.vue**: Provides the user interface, allowing users to control the synchronization settings

### Data Flow

#### Backup Process

1. User enables the synchronization feature and sets the synchronization folder
2. When data changes (such as adding/modifying messages, creating conversations, etc.), the system will automatically trigger a backup after a period of time without changes (default 60 seconds)
3. During the backup process, temporary files are created, and the final files are replaced after completion, avoiding file conflicts during the import process
4. The backed up data includes:
   - Database file (chat.db)
   - Configuration file (xxx.json, provider-models/xxx.json)

#### Import Process

1. User clicks "Import Data" on the settings interface
2. The system will check if there is a valid backup file in the synchronization folder
3. A backup of the current data will be made before the import, to ensure recovery in case of import failure
4. After importing data, the application must be restarted to apply the changes

### Security Measures

1. The backup process uses temporary files to avoid file conflicts during the import process
2. A backup of the current data will be made before the import, to ensure recovery
3. The synchronization folder path will not be synchronized, avoiding path conflicts on different devices
4. The backup and import operations have complete error handling and status feedback

## User Interface

The user interface of the data synchronization feature is located in the "Data" tab on the settings page, including the following features:

1. Sync feature switch: Enable/disable the sync feature
2. Sync folder setting: Select the target folder for data backup
3. Open sync folder: Open the file browser to view the sync folder directly
4. Last sync time: Display the time of the last successful backup
5. Manual backup: Manually trigger data backup
6. Import data: Import data from the sync folder (default incremental import)

## Event System

The system uses an event mechanism to notify the status changes of backup and import:

1. `sync:backup-started`: Backup started
2. `sync:backup-completed`: Backup completed
3. `sync:backup-error`: Backup error
4. `sync:import-started`: Import started
5. `sync:import-completed`: Import completed
6. `sync:import-error`: Import error

These events are passed between the main process and the render process to ensure that the user interface can reflect the sync status in real time.

## Configuration Items

The configuration items related to the synchronization feature include:

1. `syncEnabled`: Whether to enable the synchronization feature (boolean)
2. `syncFolderPath`: Sync folder path (string)
3. `lastSyncTime`: Last sync time (timestamp)

These configuration items are stored in the application's configuration file, but the `syncFolderPath` will be filtered out during the backup to avoid path conflicts on different devices.

## Suggestions for Use

1. Choose a stable storage location as the sync folder, such as the sync directory of a cloud disk.
2. Periodically check the backup status to ensure that data is backed up normally.
3. Ensure that there are no important unsaved data before importing data.
4. Restart the application after importing data to ensure that all changes take effect.

## Limitations and Notes

1. The synchronization feature does not synchronize data in real time, but performs a backup after a period of time (default 60 seconds) after data changes (temporarily disabled, causing more io, currently low cost-effectiveness)
2. The import operation will replace all current data, including chat history and configuration.
3. The synchronization folder path will not be synchronized, and needs to be set separately on each device.
4. After importing data, the application must be restarted to fully apply the changes.