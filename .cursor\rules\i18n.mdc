---
description: Internationalization for renderer
globs: src/renderer/src/**
alwaysApply: false
---
# Internationalization
i18n:
  framework: 'vue-i18n'
  location: 'src/renderer/src/i18n'
  requirement: 'all user-facing strings must use i18n keys'
  locales: ['zh-CN','en-US', 'en-GB', 'en-NZ', 'mi-NZ', 'ja-JP','ko-KR','ru-RU','zh-HK','fr-FR','es-ES','de-DE']

# Internationalization Development Guide

## Multi-Language Support

This project supports multiple languages, including:
- Simplified Chinese
- English - US, GB, NZ
- Japanese
- Korean
- Russian
- Traditional Chinese
- French
- Māori - NZ
- Spanish
- German

## Technical Implementation

- Framework: vue-i18n
- Location: src/renderer/src/i18n
- Requirement: all user-facing strings must use i18n keys

## File Structure

- Language files are located in the `src/renderer/src/i18n/` directory
- Each language has its own JSON file
- Shared translation key values are placed in `common.json`

## Usage Specification

1. Translation key naming specification:
- Use period-separated hierarchy structure
- Use lowercase letters
- Use meaningful descriptive names
- For example: `common.button.submit`

2. Adding new translations:
- Add shared translations in `common.json`
- Add specific translations in language-specific files
- Maintain consistency of key values across all language files

3. Using in code:
```typescript
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
// Use translation system to get the translated text for the key 'common.button.submit'
const message = t('common.button.submit')
```

4. Dynamically switch languages:
```typescript
const { locale } = useI18n()
// Switch languages by setting the locale value to 'zh-CN' (for example) to switch to Simplified Chinese. See https://vue-i18n.intlify.dev/guide/essentials/locale.html for more details.
locale.value = 'zh-CN'
```

## Best Practices

1. Avoid hardcoded text
2. Use meaningful key names
3. Maintain consistent translation file structure
4. Regularly check for unused translation keys
5. Ensure all user-visible text uses translation system
