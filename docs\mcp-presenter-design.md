# MCP Presenter Design Document

## 1. Core Class Design

### 1.1 McpPresenter

`McpPresenter` (`src/main/presenter/mcpPresenter/index.ts`) is the main entry point of the module, implementing the `IMCPPresenter` interface. Main responsibilities:

- Depends on `IConfigPresenter` to get configuration.
- Initializes and manages `ServerManager` and `ToolManager`.
- **Initialization process**:
    - Test npm registry speed (through `ServerManager`).
    - Start default MCP servers based on configuration.
- Provides interfaces for managing server lifecycle (start/stop), configuration, and default settings.
- Gets all available tool definitions (through `ToolManager`), handles name conflicts.
- Provides methods for converting between MCP tool formats and different LLM providers (OpenAI, Anthropic, Gemini) formats.
- Receives tool call requests from LLM Provider, converts them to standard format and dispatches to `ToolManager`.
- Triggers MCP-related events through `eventBus`.

**Key Methods**:

- `initialize()`: Execute initialization logic.
- `getMcpServers()`, `startServer()`, `stopServer()`, `addMcpServer()`, `removeMcpServer()`, `updateMcpServer()`: Server Management.
- `getMcpDefaultServers()`, `addMcpDefaultServer()`, `removeMcpDefaultServer()`, `toggleMcpDefaultServer()`: Default Server Management.
- `getAllToolDefinitions()`: Get Tool Definitions.
- `callTool()`: Receive normalized tool call request and delegate to `ToolManager`.

### 1.2 ServerManager

`ServerManager` (`src/main/presenter/mcpPresenter/serverManager.ts`) is responsible for managing the lifecycle and management of MCP server instances (`McpClient`):

- **NPM Registry Management**:
    - Automatically tests multiple npm registries (`NPM_REGISTRY_LIST`) and selects the fastest.
    - Passes the selected registry to `McpClient` instances.
- **Client Management**:
    - Maintains running `McpClient` instances (`clients` Map).
    - Provides methods for starting and stopping servers.
    - Gets running clients.
    - Handles errors when server startup fails.
    - Triggers `MCP_EVENTS.CLIENT_LIST_UPDATED` event.

### 1.3 ToolManager

`ToolManager` (`src/main/presenter/mcpPresenter/toolManager.ts`) 负责 MCP 工具的管理和调用：

- **工具定义获取与缓存**:
    - 从所有运行中的 `McpClient` 获取工具定义 (`listTools`)。
    - **冲突处理**: 检测并自动重命名来自不同服务器的同名工具 (格式: `serverName_toolName`)。
    - 缓存处理后的工具定义列表 (`cachedToolDefinitions`) 和工具名称到目标的映射 (`toolNameToTargetMap`)。
    - 监听 `MCP_EVENTS.CLIENT_LIST_UPDATED` 事件以清除缓存。
- **工具调用处理**:
    - `callTool()`: 接收标准化的 `MCPToolCall` 请求。
    - **查找目标**: 使用 `toolNameToTargetMap` 找到处理该工具的 `McpClient` 和原始工具名称。
    - **权限控制**: 调用 `checkToolPermission()` 检查权限，基于服务器配置中的 `autoApprove` 列表 (支持 `all`, `read`, `write` 等)。
    - **执行调用**: 使用 *原始* 工具名称调用目标 `McpClient` 的 `callTool` 方法。
    - 格式化工具调用结果并触发 `MCP_EVENTS.TOOL_CALL_RESULT` 事件。

### 1.4 McpClient

`McpClient` (`src/main/presenter/mcpPresenter/mcpClient.ts`) is an implementation of a client that communicates with a single MCP server:

- **Communication and Transport**:
    - Handles connection establishment (`connect`) and disconnection (`disconnect`) with MCP servers.
    - Supports multiple transport layers (`stdio`, `sse`, `http`, `inmemory`).
    - Executes tool calls (`callTool`), lists tools (`listTools`), reads resources (`readResource`), etc.
- **Environment and Configuration**:
    - Handles `stdio` type environment variables, especially `PATH` merging (system, default, custom, runtime) and proxy settings (`http_proxy`, `https_proxy`).
    - Uses `npmRegistry` provided by `ServerManager`.
    - Handles authentication (`AuthProvider` for Bearer Token) and custom headers (`customHeaders`).
- **Connection Management**:
    - 5-minute connection timeout.
    - Triggers `MCP_EVENTS.SERVER_STATUS_CHANGED` event.

## 2. Tool Call Flow (Example: OpenAI Provider)

```mermaid
sequenceDiagram
    participant LLM as "LLM Provider (e.g., OpenAICompatibleProvider)"
    participant McpPresenter
    participant ToolManager
    participant McpClient
    participant MCP Server

    Note over LLM, McpPresenter: Initialize/Get Tool Definitions
    LLM->>McpPresenter: getAllToolDefinitions()
    McpPresenter->>ToolManager: getAllToolDefinitions()
    ToolManager->>McpClient: listTools() (for all running clients)
    McpClient-->>ToolManager: original tool list
    ToolManager->>ToolManager: handle conflicts, cache definitions and mappings
    ToolManager-->>McpPresenter: processed tool definition list
    McpPresenter->>LLM: mcpToolsToOpenAITools(definitions)
    LLM-->>McpPresenter: OpenAI format tools

    Note over LLM, McpPresenter: User request, LLM determines tool call
    LLM->>LLM: LLM generates tool call request (OpenAI format)
    LLM->>McpPresenter: openAIToolsToMcpTool(tool_call)
    McpPresenter-->>LLM: normalized MCPToolCall

    Note over LLM, McpPresenter: Execute tool call
    LLM->>McpPresenter: callTool(mcpToolCall)
    McpPresenter->>ToolManager: callTool(mcpToolCall)
    ToolManager->>ToolManager: lookup target client & original name (toolNameToTargetMap)
    ToolManager->>ToolManager: checkToolPermission()
    alt Permission Denied
        ToolManager-->>McpPresenter: Error Response
        McpPresenter-->>LLM: Error Response (wrapped)
    else Permission Granted
        ToolManager->>McpClient: callTool(originalToolName, args)
        McpClient->>MCP Server: Execute Tool
        MCP Server-->>McpClient: Raw Result
        McpClient-->>ToolManager: ToolCallResult
        ToolManager->>ToolManager: Format Result
        ToolManager-->>McpPresenter: MCPToolResponse (triggers event)
        McpPresenter-->>LLM: Final Response (wrapped)
    end
    LLM->>LLM: Handle response, continue (e.g., add tool responses to context, generate next response, or further calls)
```

**流程说明**:

1.  **获取工具**: LLM Provider (如 `OpenAICompatibleProvider`) 在需要时向 `McpPresenter` 请求工具定义。`McpPresenter` 委托 `ToolManager`，后者从所有运行的 `McpClient` 获取原始工具列表，处理命名冲突，缓存结果，并将处理后的定义返回给 `McpPresenter`。`McpPresenter` 将其转换为 LLM Provider 所需的格式。
2.  **生成调用**: LLM 根据用户输入和可用工具，生成一个工具调用请求 (LLM 特定格式)。
3.  **转换调用**: LLM Provider 将此请求传递给 `McpPresenter`，后者将其转换为标准的 `MCPToolCall` 格式。
4.  **执行调用**: LLM Provider 调用 `McpPresenter.callTool()` 并传入标准化的 `MCPToolCall`。
5.  **查找与检查**: `McpPresenter` 委托给 `ToolManager`。`ToolManager` 使用内部映射找到负责该工具的 `McpClient` 实例和该工具在服务器上的原始名称，并检查调用权限。
6.  **实际执行**: 如果权限允许，`ToolManager` 调用目标 `McpClient` 的 `callTool` 方法，并使用 *原始* 工具名称和参数。
7.  **结果返回**: `McpClient` 与 MCP 服务器通信，获取结果并返回给 `ToolManager`。
8.  **格式化与响应**: `ToolManager` 格式化结果为 `MCPToolResponse`，触发事件，并将响应返回给 `McpPresenter`。`McpPresenter` 可能进一步包装响应，最终返回给 LLM Provider。
9.  **后续处理**: LLM Provider 处理工具调用的结果，可能将其添加到对话历史中，并让 LLM 基于结果生成下一步的响应或进行下一轮工具调用。

## 3. 事件系统

MCP Presenter 通过 `eventBus` 发出以下事件：

| 事件名称                           | 触发时机                         | 触发源        | 参数                                             |
| ---------------------------------- | -------------------------------- | ------------- | ------------------------------------------------ |
| `MCP_EVENTS.SERVER_STARTED`        | 服务器成功启动                   | McpPresenter  | serverName (string)                              |
| `MCP_EVENTS.SERVER_STOPPED`        | 服务器停止                       | McpPresenter  | serverName (string)                              |
| `MCP_EVENTS.TOOL_CALL_RESULT`      | 工具调用完成                     | ToolManager   | MCPToolResponse                                  |
| `MCP_EVENTS.CONFIG_CHANGED`        | MCP 配置 (服务器/默认/启用) 变更 | McpConfHelper | { mcpServers, defaultServers, mcpEnabled }       |
| `MCP_EVENTS.SERVER_STATUS_CHANGED` | MCP 客户端连接状态变化           | McpClient     | { name: string, status: 'running' \| 'stopped' } |
| `MCP_EVENTS.CLIENT_LIST_UPDATED`   | 运行中的 MCP 客户端列表更新      | ServerManager | (无)                                             |

## 4. 配置管理

MCP 相关配置通过 `McpConfHelper` (`src/main/presenter/configPresenter/mcpConfHelper.ts`) 管理，并存储在 ElectronStore (`mcp-settings`) 中。

**核心配置项**:

- `mcpServers`: `Record<string, MCPServerConfig>` - 存储所有已配置的 MCP 服务器及其配置。
- `defaultServers`: `string[]` - 默认启动的服务器名称列表。
- `mcpEnabled`: `boolean` - 全局启用/禁用 MCP 功能的开关。

**`MCPServerConfig` 接口**:

```typescript
interface MCPServerConfig {
  command?: string // 可执行命令 (stdio 类型)
  args?: string[] // 命令行参数
  env?: Record<string, string> // 环境变量
  type?: 'stdio' | 'sse' | 'http' | 'inmemory' // 服务器类型
  baseUrl?: string // 用于 SSE/HTTP 类型的服务器 URL
  autoApprove?: string[] // 自动批准的权限列表 ('all', 'read', 'write', 或具体工具名)
  icons?: string // 服务器图标 (emoji 或 URL)
  descriptions?: string // 服务器描述
  disable?: boolean // 是否禁用该服务器 (UI 层面)
  customHeaders?: Record<string, string> // 用于 SSE/HTTP 的自定义请求头 (包含 Authorization)
}
```

`McpConfHelper` 还提供了恢复默认配置、添加内置服务器 (如 `buildInFileSystem`, `Artifacts`) 以及处理版本升级迁移的逻辑。

## 5. 扩展指南

### 5.1 添加新服务器类型

1. 在 `McpClient` 中添加新的传输类型处理逻辑 (继承或实现 `Transport` 接口)。
2. 更新 `MCPServerConfig` 类型定义 (如果需要新的配置项)。
3. 在 `McpClient` 的 `connect` 方法中添加根据 `type` 创建新 Transport 的分支。
4. 更新 `ServerManager` (如果需要特定的管理逻辑)。

### 5.2 添加新工具格式转换

1. 在 `McpPresenter` 中添加新的转换方法 (如 `mcpToolsToNewFormat()`)。
2. 实现对应的反向转换方法 (如 `newFormatToMcpTool()`)。
3. 更新相关 LLM Provider 代码以使用新的转换方法。

### 5.3 自定义权限控制

1. 修改 `ToolManager.checkToolPermission()` 方法以实现新的权限逻辑。
2. 可能需要更新 `MCPServerConfig` 接口以支持新的权限配置。
3. 更新相关文档和用户界面以反映新的权限模型。
