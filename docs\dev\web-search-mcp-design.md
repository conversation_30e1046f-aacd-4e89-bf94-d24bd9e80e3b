# Comprehensive Web Search Strategy Design

## Overview

This document outlines Docomoe's **comprehensive, tiered web search strategy** using modular MCP tools. The strategy provides everything needed for most web interactions through four specialized tiers, from basic browser search to advanced automation, ensuring optimal performance and resource utilization for different use cases.

## Architecture Context

### Tiered Web Search Strategy
The modular approach separates web search and scraping into focused, specialized tiers:

## **Tier 1: General Browser Search** (Existing)
   - **Location**: `src/main/presenter/threadPresenter/searchManager.ts`
   - **Search Engines**: Google, Bing, DuckDuckGo, Baidu, Sogou, Google Scholar, <PERSON><PERSON>esh<PERSON>
   - **Technology**: Electron BrowserWindow with JavaScript extraction + AI fallback
   - **Use Cases**: Broad LLM web search in conversations, quick searches, general browsing
   - **Advantages**: No API costs, handles dynamic content, built-in fallback AI extraction

## **Tier 2: API-based Search Providers** (Existing)
   - **Brave Search MCP**: Web + local search with API authentication
   - **Bocha Search MCP**: Web + AI search with API authentication
   - **Technology**: Direct API calls with structured responses
   - **Use Cases**: High-quality searches, structured data, rate-limited professional use
   - **Advantages**: Clean structured results, reliable, professional-grade

## **Tier 3: Lightweight Web Scraper MCP** (Existing)
   - **Purpose**: Fast static content extraction and DuckDuckGo HTML search
   - **Technology**: Cheerio + Axios for lightweight scraping, direct DuckDuckGo HTML parsing
   - **Use Cases**: Static websites, search + scrape workflows, content extraction
   - **Memory Management**: Smart batching, temp file handling, rate limiting
   - **Advantages**: No API costs, memory efficient, self-contained

## **Tier 4: Advanced Browser Automation MCP** (New Implementation)
   - **Purpose**: Dynamic content extraction, complex web interactions
   - **Technology**: Puppeteer or Playwright for full browser automation
   - **Use Cases**: SPAs, JavaScript-heavy sites, form submissions, complex workflows
   - **Advantages**: Handles any web content, full browser capabilities, screenshot support


### Implementation Status
- ✅ **Tier 1**: General Browser Search (Production Ready)
- ✅ **Tier 2**: API-based Search Providers (Brave + Bocha MCPs Ready)
- ✅ **Tier 3**: Lightweight Web Scraper MCP (Production Ready)
- ✅ **Tier 4**: Advanced Browser Automation MCP (Puppeteer Implementation Complete)

### Strategy Benefits
- **Comprehensive Coverage**: Handles all web interaction scenarios
- **Cost Optimization**: Free tiers for basic needs, paid APIs for premium features
- **Performance Scaling**: Lightweight to heavy-duty options based on requirements
- **Modular Design**: Each tier can be used independently or combined
- **Resource Efficiency**: Smart selection based on task complexity

## Tier 3: Lightweight Web Scraper MCP (Current Implementation)

### Server Registration
The Lightweight Web Scraper MCP Server is registered in the system as:
- **Server Name**: `webScraper`
- **Display Name**: `docomoe-inmemory/web-scraper-server`
- **Version**: `1.0.0`
- **Icon**: 🕷️
- **Auto-approve**: `read` operations
- **Status**: Available and enabled by default

## Core Tools (2 Total)

### 1. DuckDuckGoWebSearch
**Purpose**: Direct DuckDuckGo HTML search without external dependencies

**Features**:
- ✅ **Direct HTML Parsing**: No external dependencies, direct HTML parsing with Cheerio
- ✅ **Rate Limiting**: Built-in rate limiting with smart throttling
- ✅ **Clean Results**: Automatic redirect URL cleaning and ad filtering
- ✅ **LLM-Optimized Output**: Structured results formatted for AI consumption
- ✅ **Error Handling**: Comprehensive timeout and error management

**Input Schema**:
```typescript
{
  query: string,           // Search query string
  maxResults?: number      // Maximum results (default: 10)
}
```

**Output**: Formatted search results with title, URL, and snippet for each result

### 2. UrlContentExtractor
**Purpose**: Memory-efficient content extraction from single or multiple URLs

**Features**:
- ✅ **Memory Management**: Smart batching based on available system memory (70% threshold)
- ✅ **Temp File Handling**: Large content (>1MB) processed via temporary files
- ✅ **Batch Processing**: Multiple URLs processed efficiently with controlled concurrency
- ✅ **Content Cleaning**: Automatic removal of scripts, styles, navigation elements
- ✅ **Size Limits**: Content truncation at 8000 characters with indicators
- ✅ **Garbage Collection**: Automatic memory cleanup between batches

**Input Schema**:
```typescript
{
  url: string | string[]   // Single URL or array of URLs to extract content from
}
```

**Output**:
- Single URL: Clean text content
- Multiple URLs: JSON object with URL-to-content mapping

## Technical Implementation Details

### Rate Limiting System
The lightweight web scraper implements a simple but effective rate limiting system:

```typescript
class RateLimiter {
  private lastRequestTime: number = 0
  private readonly minInterval: number = 2000 // 2 seconds between requests

  async acquire(): Promise<void> {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime

    if (timeSinceLastRequest < this.minInterval) {
      const waitTime = this.minInterval - timeSinceLastRequest
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }

    this.lastRequestTime = Date.now()
  }
}
```

### Memory Management
The system intelligently handles memory usage:

- **Memory Threshold**: Monitors system memory usage (70% threshold)
- **Large Content Handling**: Files >1MB are processed via temporary files
- **Batch Processing**: Multiple URLs processed in controlled batches
- **Garbage Collection**: Automatic cleanup between operations

### Content Processing Pipeline
1. **Fetch**: HTTP request with proper headers and timeout
2. **Parse**: Cheerio-based HTML parsing
3. **Clean**: Remove scripts, styles, navigation elements
4. **Extract**: Get meaningful text content
5. **Format**: Truncate and format for LLM consumption

## Technology Stack

### Core Dependencies
1. **Cheerio** - jQuery-like server-side HTML parsing
   - ✅ Fast and lightweight DOM manipulation
   - ✅ Perfect for static HTML content extraction
   - ✅ Low memory footprint

2. **Axios** - HTTP client for web requests
   - ✅ Promise-based HTTP requests
   - ✅ Built-in timeout and error handling
   - ✅ Configurable headers and redirects

3. **Zod** - TypeScript-first schema validation
   - ✅ Runtime type checking for tool parameters
   - ✅ Automatic JSON schema generation
   - ✅ Type-safe input validation

### Supporting Technologies
- **Node.js fs/promises** - File system operations for temp file handling
- **Node.js os** - System memory monitoring
- **Node.js path** - Cross-platform path handling
- **URLSearchParams** - Form data encoding for search requests

## Performance & Optimization

### Rate Limiting Implementation
Simple but effective rate limiting with 2-second intervals between requests:

```typescript
class RateLimiter {
  private lastRequestTime: number = 0
  private readonly minInterval: number = 2000 // 2 seconds

  async acquire(): Promise<void> {
    const timeSinceLastRequest = Date.now() - this.lastRequestTime
    if (timeSinceLastRequest < this.minInterval) {
      await new Promise(resolve =>
        setTimeout(resolve, this.minInterval - timeSinceLastRequest)
      )
    }
    this.lastRequestTime = Date.now()
  }
}
```

### Performance Features
- ✅ **Memory Monitoring**: Real-time system memory usage tracking
- ✅ **Batch Processing**: Controlled concurrency for multiple URLs
- ✅ **Timeout Handling**: 30-second timeouts for all HTTP requests
- ✅ **Temp File Management**: Large content processed via temporary files
- ✅ **Garbage Collection**: Automatic cleanup between batch operations

## Data Structures

### Core Interfaces
The lightweight implementation uses simple, focused data structures:

```typescript
// Search result structure
interface SearchResult {
  title: string
  link: string
  snippet: string
  position: number
}

// Memory monitoring
interface MemoryStats {
  totalMemory: number
  freeMemory: number
  usedMemory: number
  usagePercentage: number
}

// Context interface for error handling
interface Context {
  error(message: string): Promise<void>
}
```

### Tool Schemas
Input validation using Zod schemas:

```typescript
const DuckDuckGoWebSearchArgsSchema = z.object({
  query: z.string().describe('Search query string'),
  maxResults: z.number().optional().default(10)
})

const UrlContentExtractorArgsSchema = z.object({
  url: z.union([
    z.string().describe('Single webpage URL'),
    z.array(z.string()).describe('Array of webpage URLs')
  ])
})
```

## Integration with Existing Architecture

### MCP Server Registration
The lightweight web scraper is registered as an in-memory MCP server:

```typescript
// Server registration in builder.ts
case 'webScraper':
  return new WebScraperServer()

// Configuration in mcpConfHelper.ts
webScraper: {
  args: [],
  descriptions: 'Lightweight web scraping and content extraction service',
  icons: '🕷️',
  autoApprove: ['read'],
  type: 'inmemory' as MCPServerType,
  command: 'webScraper',
  env: {},
  disable: false
}
```

### System Integration Features
- ✅ **Self-Contained**: Complete implementation within single file
- ✅ **No External Dependencies**: Uses only Node.js built-ins and core libraries
- ✅ **Memory Efficient**: Smart resource management and cleanup
- ✅ **Error Resilient**: Comprehensive error handling and recovery
- ✅ **Type Safe**: Full TypeScript implementation with Zod validation

## Security & Best Practices

### Responsible Scraping
- ✅ **Rate Limiting**: 2-second intervals between requests to respect server resources
- ✅ **User-Agent**: Proper browser User-Agent identification
- ✅ **Timeout Handling**: 30-second timeouts to prevent hanging requests
- ✅ **Memory Limits**: Automatic temp file handling for large content
- ✅ **Resource Cleanup**: Proper cleanup of temporary files and memory

### Data Privacy & Security
- ✅ **No Persistent Storage**: All content is processed and returned immediately
- ✅ **Input Validation**: Zod schema validation for all tool parameters
- ✅ **URL Validation**: HTTP/HTTPS URL validation and sanitization
- ✅ **Memory Management**: Smart memory monitoring and cleanup
- ✅ **Error Isolation**: Individual URL failures don't affect batch operations

## Error Handling

### Error Management Strategy
Simple but effective error handling for reliable operation:

```typescript
// Error handling in batch processing
const batchResults = await Promise.all(
  batch.map(async (url) => {
    try {
      const content = await this.fetchAndParse(url, ctx)
      return { url, content }
    } catch (error) {
      return {
        url,
        content: `Error processing URL: ${(error as Error).message}`
      }
    }
  })
)
```

### Error Recovery Features
- ✅ **Graceful Degradation**: Individual failures don't stop batch operations
- ✅ **Detailed Error Messages**: Clear error descriptions for debugging
- ✅ **Timeout Protection**: Prevents hanging on unresponsive servers
- ✅ **Memory Protection**: Automatic cleanup even when operations fail
- ✅ **Rate Limit Compliance**: Built-in delays prevent overwhelming servers

## Future Enhancement Opportunities

### Potential Extensions
The lightweight foundation enables future enhancements:

1. **Additional Search Engines**: Extend DuckDuckGo search with other providers
2. **Content Caching**: Optional caching layer for frequently accessed content
3. **Custom Selectors**: User-defined CSS selectors for targeted extraction
4. **Proxy Integration**: Enhanced proxy support for different regions
5. **Content Monitoring**: Scheduled checks for content changes

### Extensibility Foundation
- ✅ **Simple Architecture**: Easy to understand and extend
- ✅ **Type-Safe**: Full TypeScript implementation
- ✅ **Modular Design**: Clean separation of concerns
- ✅ **Memory Efficient**: Optimized for resource-constrained environments

## Tier 4: Advanced Browser Automation MCP (New Implementation)

### Server Registration
The Puppeteer MCP Server is registered in the system as:
- **Server Name**: `puppeteerServer`
- **Display Name**: `docomoe-inmemory/puppeteer-server`
- **Version**: `1.0.0`
- **Icon**: 🎭
- **Auto-approve**: `read` operations
- **Status**: Available and enabled by default

### Core Tools (7 Total)

#### 1. Navigate
**Purpose**: Navigate to URLs with advanced wait conditions
- **Features**: Multiple wait conditions (load, domcontentloaded, networkidle0, networkidle2)
- **Timeout Control**: Configurable navigation timeouts
- **User Agent**: Automatic browser user agent simulation

#### 2. Extract Content
**Purpose**: Advanced content extraction with CSS selectors
- **Features**: Single/multiple element extraction, attribute extraction, text/HTML content
- **Flexibility**: Optional selectors for full page content
- **Data Types**: Support for various content types and attributes

#### 3. Click Element
**Purpose**: Interactive element clicking with navigation support
- **Features**: Wait for element availability, optional navigation waiting
- **Safety**: Automatic element waiting before interaction
- **Flexibility**: Configurable timeouts and behavior

#### 4. Fill Form
**Purpose**: Form field interaction and data input
- **Features**: Automatic field clearing, type simulation
- **Safety**: Element waiting and validation
- **Flexibility**: Configurable clearing behavior

#### 5. Take Screenshot
**Purpose**: Visual page capture with format options
- **Features**: Full page/viewport screenshots, multiple formats (PNG/JPEG)
- **Quality Control**: JPEG quality settings
- **Base64 Output**: Direct base64 encoding for easy integration

#### 6. Wait for Element
**Purpose**: Advanced element waiting with visibility control
- **Features**: Visibility/existence waiting, configurable timeouts
- **Reliability**: Robust element detection
- **Flexibility**: Multiple wait conditions

#### 7. Execute Script
**Purpose**: Custom JavaScript execution on pages
- **Features**: Arbitrary script execution, parameter passing
- **Power**: Full browser JavaScript capabilities
- **Safety**: Controlled execution environment

### Technical Implementation

#### Browser Session Management
- **Session Timeout**: 5-minute automatic cleanup
- **Resource Management**: Automatic browser/page cleanup
- **Rate Limiting**: 1-second intervals between operations
- **Memory Optimization**: Efficient browser instance reuse

#### Security & Performance
- **Headless Mode**: Runs in headless browser mode
- **Sandbox**: Secure browser arguments for safety
- **User Agent**: Realistic browser identification
- **Viewport**: Standard 1920x1080 viewport simulation

## Implementation Structure

### Complete Code Organization
The comprehensive web search strategy is implemented across multiple specialized files:

```
src/main/presenter/mcpPresenter/inMemoryServers/
├── webScraperServer.ts          # ✅ Tier 3: Lightweight scraping (2 tools)
│   ├── DuckDuckGoSearcher       # Search functionality
│   ├── WebContentFetcher        # Content extraction
│   ├── RateLimiter             # Rate limiting
│   └── WebScraperServer        # MCP server wrapper
├── puppeteerServer.ts           # ✅ Tier 4: Advanced automation (7 tools)
│   ├── BrowserSessionManager    # Session management
│   ├── BrowserRateLimiter      # Operation rate limiting
│   └── PuppeteerServer         # MCP server wrapper
├── braveSearchServer.ts         # ✅ Tier 2: Brave API search
├── bochaSearchServer.ts         # ✅ Tier 2: Bocha API search
└── deepResearchServer.ts        # ✅ Combined research workflows
```

### Tier 1 Integration
```
src/main/presenter/threadPresenter/
└── searchManager.ts             # ✅ Tier 1: General browser search
    ├── Search Engine Templates  # Multiple search engines
    ├── JavaScript Extraction   # Dynamic content extraction
    └── AI Fallback Extraction  # LLM-powered result parsing
```

### Testing Recommendations
- **Unit Tests**: Test search and extraction functions with mock data
- **Integration Tests**: Test with real websites (rate-limited)
- **Memory Tests**: Validate memory management with large content
- **Error Tests**: Verify graceful error handling and recovery

## Implementation Status: Complete ✅

The **Comprehensive Web Search Strategy** is **successfully implemented and ready for use**!

### Achievement Summary
- ✅ **Tier 1**: General Browser Search (7 search engines + AI fallback)
- ✅ **Tier 2**: API-based Search Providers (Brave + Bocha with 4 tools total)
- ✅ **Tier 3**: Lightweight Web Scraper (2 essential tools)
- ✅ **Tier 4**: Advanced Browser Automation (7 powerful tools)
- ✅ **Total Coverage**: 20+ specialized web interaction tools
- ✅ **Production Ready**: Comprehensive error handling, rate limiting, and memory management
- ✅ **Modular Design**: Each tier can be used independently or combined
- ✅ **Type-Safe**: Full TypeScript implementation with Zod validation

### Ready for Use
Users can now access the complete web search ecosystem:

#### **Tier 1: General Browser Search**
- **Built-in**: Always available in conversation search
- **Coverage**: Google, Bing, DuckDuckGo, Baidu, Sogou, Google Scholar, Baidu Xueshu
- **Features**: Dynamic content handling + AI fallback extraction

#### **Tier 2: API-based Search Providers**
1. **Enable Brave Search MCP** (🦁) - Professional web + local search
2. **Enable Bocha Search MCP** (🔍) - AI-powered web + research search

#### **Tier 3: Lightweight Web Scraper**
1. **Enable Web Scraper MCP** (🕷️) - Fast static content extraction
2. **Tools**: DuckDuckGoWebSearch + UrlContentExtractor

#### **Tier 4: Advanced Browser Automation**
1. **Enable Puppeteer MCP** (🎭) - Full browser automation capabilities
2. **Tools**: Navigate, Extract Content, Click Element, Fill Form, Take Screenshot, Wait for Element, Execute Script

### Strategy Benefits Realized
- **🎯 Comprehensive Coverage**: Handles all web interaction scenarios from simple searches to complex automation
- **💰 Cost Optimization**: Free tiers (1,3,4) for basic needs, paid APIs (2) for premium features
- **⚡ Performance Scaling**: Lightweight to heavy-duty options based on requirements
- **🔧 Modular Design**: Each tier can be used independently or combined for complex workflows
- **🧠 Resource Efficiency**: Smart selection based on task complexity and requirements

The Comprehensive Web Search Strategy provides everything needed for most web interactions through four specialized, modular tiers! 🌐✨

### Quick Start Guide
1. **Navigate to MCP settings** in Docomoe
2. **Enable desired tiers**:
   - 🕷️ **Web Scraper** (Tier 3) - For basic scraping needs
   - 🎭 **Puppeteer Server** (Tier 4) - For advanced automation
   - 🦁 **Brave Search** (Tier 2) - For professional API search (requires API key)
   - 🔍 **Bocha Search** (Tier 2) - For AI-powered search (requires API key)
3. **Start using tools** based on your specific web interaction needs
4. **Combine tiers** for complex workflows (e.g., search + scrape + automate)
