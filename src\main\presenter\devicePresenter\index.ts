import { IDevicePresenter, DeviceInfo, MemoryInfo, DiskInfo } from '../../../shared/presenter'
import os from 'os'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'
import { app, dialog } from 'electron'
import { nanoid } from 'nanoid'
import axios from 'axios'
const execAsync = promisify(exec)

export class DevicePresenter implements IDevicePresenter {
  static getDefaultHeaders(): Record<string, string> {
    return {
      'HTTP-Referer': 'https://calmren.com',
      'X-Title': 'Docomoe'
    }
  }
  async getAppVersion(): Promise<string> {
    return app.getVersion()
  }

  async getDeviceInfo(): Promise<DeviceInfo> {
    return {
      platform: process.platform,
      arch: process.arch,
      cpuModel: os.cpus()[0].model,
      totalMemory: os.totalmem(),
      osVersion: os.release()
    }
  }

  async getCPUUsage(): Promise<number> {
    const startMeasure = os.cpus().map((cpu) => cpu.times)

    // Wait for 100ms to get a meaningful CPU usage measurement
    await new Promise((resolve) => setTimeout(resolve, 100))

    const endMeasure = os.cpus().map((cpu) => cpu.times)

    const idleDifferences = endMeasure.map((end, i) => {
      const start = startMeasure[i]
      const idle = end.idle - start.idle
      const total =
        end.user -
        start.user +
        (end.nice - start.nice) +
        (end.sys - start.sys) +
        (end.irq - start.irq) +
        idle
      return 1 - idle / total
    })

    // Return average CPU usage across all cores
    return (idleDifferences.reduce((sum, idle) => sum + idle, 0) / idleDifferences.length) * 100
  }

  async getMemoryUsage(): Promise<MemoryInfo> {
    const total = os.totalmem()
    const free = os.freemem()
    const used = total - free

    return {
      total,
      free,
      used
    }
  }

  async getDiskSpace(): Promise<DiskInfo> {
    if (process.platform === 'win32') {
      // Windows implementation
      const { stdout } = await execAsync('wmic logicaldisk get size,freespace')
      const lines = stdout.trim().split('\n').slice(1)
      let total = 0
      let free = 0

      lines.forEach((line) => {
        const [freeSpace, size] = line.trim().split(/\s+/).map(Number)
        if (!isNaN(freeSpace) && !isNaN(size)) {
          free += freeSpace
          total += size
        }
      })

      return {
        total,
        free,
        used: total - free
      }
    } else {
      // Unix-like systems implementation
      const { stdout } = await execAsync('df -k /')
      const [, line] = stdout.trim().split('\n')
      const [, total, , used, free] = line.split(/\s+/)

      return {
        total: parseInt(total) * 1024,
        free: parseInt(free) * 1024,
        used: parseInt(used) * 1024
      }
    }
  }

  /**
   * Cache image to local file system
   * @param imageData Image data, can be URL or Base64 encoding
   * @returns Returns imgcache:// protocol image URL or original URL (download failed)
   */
  async cacheImage(imageData: string): Promise<string> {
    // If already imgcache protocol, directly return
    if (imageData.startsWith('imgcache://')) {
      return imageData
    }

    // Create cache directory
    const cacheDir = path.join(app.getPath('userData'), 'images')
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true })
    }

    // Generate unique file name
    const timestamp = Date.now()
    const uniqueId = nanoid(8)
    const fileName = `img_${timestamp}_${uniqueId}`

    // Determine image type
    if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
      // Handle URL image
      return this.cacheImageFromUrl(imageData, cacheDir, fileName)
    } else if (imageData.startsWith('data:image/')) {
      // Handle Base64 image
      return this.cacheImageFromBase64(imageData, cacheDir, fileName)
    } else {
      console.warn('Unsupported image format')
      return imageData // Return original data
    }
  }

  /**
   * Download and cache image from URL
   * @param url Image URL
   * @param cacheDir Cache directory
   * @param fileName File name (without extension)
   * @returns Returns imgcache protocol URL or original URL (download failed)
   */
  private async cacheImageFromUrl(
    url: string,
    cacheDir: string,
    fileName: string
  ): Promise<string> {
    try {
      // Use axios to download image
      const response = await axios({
        method: 'get',
        url: url,
        responseType: 'arraybuffer',
        timeout: 10000 // 10 second timeout
      })

      // Get content type and determine file extension
      const contentType = response.headers['content-type'] || 'image/jpeg'
      let extension = 'jpg'

      if (contentType.includes('png')) {
        extension = 'png'
      } else if (contentType.includes('gif')) {
        extension = 'gif'
      } else if (contentType.includes('webp')) {
        extension = 'webp'
      } else if (contentType.includes('svg')) {
        extension = 'svg'
      }

      const saveFileName = `${fileName}.${extension}`
      const fullPath = path.join(cacheDir, saveFileName)

      // Write downloaded data to file
      await fs.promises.writeFile(fullPath, Buffer.from(response.data))

      // Return imgcache protocol URL
      return `imgcache://${saveFileName}`
    } catch (error) {
      console.error('Download image failed:', error)
      // Return original URL on download failure
      return url
    }
  }

  /**
   * Cache image from Base64 data
   * @param base64Data Base64 encoded image data
   * @param cacheDir Cache directory
   * @param fileName File name (without extension)
   * @returns Returns imgcache protocol URL or original data (handle failed)
   */
  private async cacheImageFromBase64(
    base64Data: string,
    cacheDir: string,
    fileName: string
  ): Promise<string> {
    try {
      // Parse MIME type and actual Base64 data
      const matches = base64Data.match(/^data:([^;]+);base64,(.*)$/)
      if (!matches || matches.length !== 3) {
        console.warn('Invalid Base64 image data')
        return base64Data
      }

      const mimeType = matches[1]
      const base64Content = matches[2]

      // Determine file extension based on MIME type
      let extension = 'jpg'
      if (mimeType.includes('png')) {
        extension = 'png'
      } else if (mimeType.includes('gif')) {
        extension = 'gif'
      } else if (mimeType.includes('webp')) {
        extension = 'webp'
      } else if (mimeType.includes('svg')) {
        extension = 'svg'
      }

      const saveFileName = `${fileName}.${extension}`
      const fullPath = path.join(cacheDir, saveFileName)

      // Convert Base64 data to Buffer and save as image file
      const imageBuffer = Buffer.from(base64Content, 'base64')
      await fs.promises.writeFile(fullPath, imageBuffer)

      // 返回imgcache协议URL
      return `imgcache://${saveFileName}`
    } catch (error) {
      console.error('保存Base64图片失败:', error)
      return base64Data // 出错时返回原始数据
    }
  }

  async resetData(): Promise<void> {
    return new Promise((resolve, reject) => {
      const response = dialog.showMessageBoxSync({
        type: 'warning',
        buttons: ['OK', 'Cancel'],
        defaultId: 0,
        message: 'Clear all local data',
        detail: 'Warning: This operation will permanently delete all local records. Are you sure?'
      })
      if (response === 0) {
        try {
          const dbPath = path.join(app.getPath('userData'), 'app_db')
          const removeDirectory = (dirPath: string): void => {
            if (fs.existsSync(dirPath)) {
              fs.readdirSync(dirPath).forEach((file) => {
                const currentPath = path.join(dirPath, file)
                if (fs.lstatSync(currentPath).isDirectory()) {
                  removeDirectory(currentPath)
                } else {
                  fs.unlinkSync(currentPath)
                }
              })
              fs.rmdirSync(dirPath)
            }
          }
          removeDirectory(dbPath)

          app.relaunch()
          app.exit()
          resolve()
        } catch (err) {
          console.error('softReset failed')
          reject(err)
          return
        }
      }
    })
  }

  /**
   * 选择目录
   * @returns 返回所选目录的路径，如果用户取消则返回null
   */
  async selectDirectory(): Promise<{ canceled: boolean; filePaths: string[] }> {
    return dialog.showOpenDialog({
      properties: ['openDirectory', 'createDirectory']
    })
  }

  /**
   * 重启应用程序
   */
  restartApp(): Promise<void> {
    console.log('restartApp')
    app.relaunch()
    app.exit()
    return Promise.resolve()
  }
}
